import { app as o, <PERSON><PERSON><PERSON><PERSON>indow as s, ipc<PERSON><PERSON> as l, dialog as c } from "electron";
import { createRequire as p } from "node:module";
import { fileURLToPath as d } from "node:url";
import n from "node:path";
p(import.meta.url);
const m = n.dirname(d(import.meta.url));
process.env.APP_ROOT = n.join(m, "..");
const t = process.env.VITE_DEV_SERVER_URL, h = n.join(process.env.APP_ROOT, "dist-electron"), r = n.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = t ? n.join(process.env.APP_ROOT, "public") : r;
let e;
function a() {
  e = new s({
    width: 1200,
    height: 800,
    icon: n.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      nodeIntegration: !1,
      contextIsolation: !0
    }
  }), e.webContents.on("did-finish-load", () => {
    e?.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  }), t ? e.loadURL(t) : e.loadFile(n.join(r, "index.html"));
}
o.on("window-all-closed", () => {
  process.platform !== "darwin" && (o.quit(), e = null);
});
o.on("activate", () => {
  s.getAllWindows().length === 0 && a();
});
o.whenReady().then(a);
l.handle("select-photos", async () => {
  const i = await c.showOpenDialog(e, {
    properties: ["openFile", "multiSelections"],
    filters: [
      {
        name: "Images",
        extensions: ["jpg", "jpeg", "png", "tiff", "tif", "webp"]
      }
    ]
  });
  return i.canceled ? [] : i.filePaths;
});
export {
  h as MAIN_DIST,
  r as RENDERER_DIST,
  t as VITE_DEV_SERVER_URL
};
