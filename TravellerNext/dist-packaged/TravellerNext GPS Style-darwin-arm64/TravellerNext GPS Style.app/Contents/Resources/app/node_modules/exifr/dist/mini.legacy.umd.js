!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define("exifr",["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).exifr={})}(this,(function(e){"use strict";function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}});var n=["prototype","__proto__","caller","arguments","length","name"];Object.getOwnPropertyNames(t).forEach((function(r){-1===n.indexOf(r)&&e[r]!==t[r]&&(e[r]=t[r])})),t&&u(e,t)}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u(e,t){return(u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function o(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function f(e,t,n){return(f=o()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&u(i,n.prototype),i}).apply(null,arguments)}function c(e){var t="function"==typeof Map?new Map:void 0;return(c=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return f(e,arguments,s(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u(r,e)})(e)}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function l(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?h(e):t}function d(e){var t=o();return function(){var n,r=s(e);if(t){var i=s(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l(this,n)}}function v(e,t,n){return(v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=s(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}var p="undefined"!=typeof self?self:global,y=Object.values||function(e){var t=[];for(var n in e)t.push(e[n]);return t},g=Object.entries||function(e){var t=[];for(var n in e)t.push([n,e[n]]);return t},k=Object.assign||function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach((function(t){for(var n in t)e[n]=t[n]})),e},m=Object.fromEntries||function(e){var t={};return b(e).forEach((function(e){var n=e[0],r=e[1];t[n]=r})),t},b=Array.from||function(e){if(e instanceof P){var t=[];return e.forEach((function(e,n){return t.push([n,e])})),t}return Array.prototype.slice.call(e)};function A(e){return-1!==this.indexOf(e)}Array.prototype.includes||(Array.prototype.includes=A),String.prototype.includes||(String.prototype.includes=A),String.prototype.startsWith||(String.prototype.startsWith=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.substring(t,t+e.length)===e}),String.prototype.endsWith||(String.prototype.endsWith=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.length;return this.substring(t-e.length,t)===e});var w=function(e){var t=[];if(Object.defineProperties(t,{size:{get:function(){return this.length}},has:{value:function(e){return-1!==this.indexOf(e)}},add:{value:function(e){this.has(e)||this.push(e)}},delete:{value:function(e){if(this.has(e)){var t=this.indexOf(e);this.splice(t,1)}}}}),Array.isArray(e))for(var n=0;n<e.length;n++)t.add(e[n]);return t},O=function(e){return new P(e)},P=void 0!==p.Map&&void 0!==p.Map.prototype.keys?p.Map:function(){function e(n){if(t(this,e),this.clear(),n)for(var r=0;r<n.length;r++)this.set(n[r][0],n[r][1])}return r(e,[{key:"clear",value:function(){this._map={},this._keys=[]}},{key:"size",get:function(){return this._keys.length}},{key:"get",value:function(e){return this._map["map_"+e]}},{key:"set",value:function(e,t){return this._map["map_"+e]=t,this._keys.indexOf(e)<0&&this._keys.push(e),this}},{key:"has",value:function(e){return this._keys.indexOf(e)>=0}},{key:"delete",value:function(e){var t=this._keys.indexOf(e);return!(t<0)&&(delete this._map["map_"+e],this._keys.splice(t,1),!0)}},{key:"keys",value:function(){return this._keys.slice(0)}},{key:"values",value:function(){var e=this;return this._keys.map((function(t){return e.get(t)}))}},{key:"entries",value:function(){var e=this;return this._keys.map((function(t){return[t,e.get(t)]}))}},{key:"forEach",value:function(e,t){for(var n=0;n<this._keys.length;n++)e.call(t,this._map["map_"+this._keys[n]],this._keys[n],this)}}]),e}(),S=p.fetch;p.fetch||(S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(n,r){var i=new XMLHttpRequest;if(i.open("get",e,!0),i.responseType="arraybuffer",i.onerror=r,t.headers)for(var a in t.headers)i.setRequestHeader(a,t.headers[a]);i.onload=function(){n({status:i.status,arrayBuffer:function(){return Promise.resolve(i.response)}})},i.send(null)}))});var U="undefined"!=typeof navigator,x=U&&"undefined"==typeof HTMLImageElement,C=!("undefined"==typeof global||"undefined"==typeof process||!process.versions||!process.versions.node),j=p.Buffer,B=!!j,_=function(e){return void 0!==e};function V(e){return void 0===e||(e instanceof P?0===e.size:0===y(e).filter(_).length)}function T(e){var t=new Error(e);throw delete t.stack,t}function I(e){var t=function(e){var t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}var L=function(e){return String.fromCharCode.apply(null,e)},z="undefined"!=typeof TextDecoder?new TextDecoder("utf-8"):void 0;function F(e){return z?z.decode(e):B?Buffer.from(e).toString("utf8"):decodeURIComponent(escape(L(e)))}var E=function(){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;if(t(this,e),"boolean"==typeof a&&(this.le=a),Array.isArray(n)&&(n=new Uint8Array(n)),0===n)this.byteOffset=0,this.byteLength=0;else if(n instanceof ArrayBuffer){void 0===i&&(i=n.byteLength-r);var s=new DataView(n,r,i);this._swapDataView(s)}else if(n instanceof Uint8Array||n instanceof DataView||n instanceof e){void 0===i&&(i=n.byteLength-r),(r+=n.byteOffset)+i>n.byteOffset+n.byteLength&&T("Creating view outside of available memory in ArrayBuffer");var u=new DataView(n.buffer,r,i);this._swapDataView(u)}else if("number"==typeof n){var o=new DataView(new ArrayBuffer(n));this._swapDataView(o)}else T("Invalid input argument for BufferView: "+n)}return r(e,[{key:"_swapArrayBuffer",value:function(e){this._swapDataView(new DataView(e))}},{key:"_swapBuffer",value:function(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}},{key:"_swapDataView",value:function(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}},{key:"_lengthToEnd",value:function(e){return this.byteLength-e}},{key:"set",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;t instanceof DataView||t instanceof e?t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t instanceof ArrayBuffer&&(t=new Uint8Array(t)),t instanceof Uint8Array||T("BufferView.set(): Invalid data argument.");var i=this.toUint8();return i.set(t,n),new r(this,n,t.byteLength)}},{key:"subarray",value:function(t,n){return new e(this,t,n=n||this._lengthToEnd(t))}},{key:"toUint8",value:function(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}},{key:"getUint8Array",value:function(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}},{key:"getString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=this.getUint8Array(e,t);return F(n)}},{key:"getLatin1String",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=this.getUint8Array(e,t);return L(n)}},{key:"getUnicodeString",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=[],r=0;r<t&&e+r<this.byteLength;r+=2)n.push(this.getUint16(e+r));return L(n)}},{key:"getInt8",value:function(e){return this.dataView.getInt8(e)}},{key:"getUint8",value:function(e){return this.dataView.getUint8(e)}},{key:"getInt16",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getInt16(e,t)}},{key:"getInt32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getInt32(e,t)}},{key:"getUint16",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getUint16(e,t)}},{key:"getUint32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getUint32(e,t)}},{key:"getFloat32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat32(e,t)}},{key:"getFloat64",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat64(e,t)}},{key:"getFloat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat32(e,t)}},{key:"getDouble",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat64(e,t)}},{key:"getUintBytes",value:function(e,t,n){switch(t){case 1:return this.getUint8(e,n);case 2:return this.getUint16(e,n);case 4:return this.getUint32(e,n);case 8:return this.getUint64&&this.getUint64(e,n)}}},{key:"getUint",value:function(e,t,n){switch(t){case 8:return this.getUint8(e,n);case 16:return this.getUint16(e,n);case 32:return this.getUint32(e,n);case 64:return this.getUint64&&this.getUint64(e,n)}}},{key:"toString",value:function(e){return this.dataView.toString(e,this.constructor.name)}},{key:"ensureChunk",value:function(){}}],[{key:"from",value:function(t,n){return t instanceof this&&t.le===n?t:new e(t,void 0,void 0,n)}}]),e}();function D(e,t){T("".concat(e," '").concat(t,"' was not loaded, try using full build of exifr."))}var R=function(e){a(i,e);var n=d(i);function i(e){var r;return t(this,i),(r=n.call(this)).kind=e,r}return r(i,[{key:"get",value:function(e,t){return this.has(e)||D(this.kind,e),t&&(e in t||function(e,t){T("Unknown ".concat(e," '").concat(t,"'."))}(this.kind,e),t[e].enabled||D(this.kind,e)),v(s(i.prototype),"get",this).call(this,e)}},{key:"keyList",value:function(){return b(this.keys())}}]),i}(c(P)),N=new R("file parser"),M=new R("segment parser"),W=new R("file reader");function K(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var H=X((function(e,t){return K(t(e),(function(e){return new E(e)}))}));function X(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var Y=X((function(e,t,n){var r=new(W.get(n))(e,t);return K(r.read(),(function(){return r}))})),G=X((function(e,t,n,r){return W.has(n)?Y(e,t,n):r?H(e,r):(T("Parser ".concat(n," is not loaded")),K())})),J="Invalid input argument";function q(e,t){return(n=e).startsWith("data:")||n.length>1e4?Y(e,t,"base64"):C&&e.includes("://")?G(e,t,"url",Q):C?Y(e,t,"fs"):U?G(e,t,"url",Q):void T(J);var n}var Q=function(e){return S(e).then((function(e){return e.arrayBuffer()}))},Z=function(e){return new Promise((function(t,n){var r=new FileReader;r.onloadend=function(){return t(r.result||new ArrayBuffer)},r.onerror=n,r.readAsArrayBuffer(e)}))};var $=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"tagKeys",get:function(){return this.allKeys||(this.allKeys=b(this.keys())),this.allKeys}},{key:"tagValues",get:function(){return this.allValues||(this.allValues=b(this.values())),this.allValues}}]),i}(c(P));function ee(e,t,n){var r=new $,i=n;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++){var s=i[a],u=s[0],o=s[1];r.set(u,o)}if(Array.isArray(t)){var f=t;Array.isArray(f)||("function"==typeof f.entries&&(f=f.entries()),f=b(f));for(var c=0;c<f.length;c++){var h=f[c];e.set(h,r)}}else e.set(t,r);return r}function te(e,t,n){var r,i=e.get(t),a=n;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=b(a));for(var s=0;s<a.length;s++)r=a[s],i.set(r[0],r[1])}var ne=O(),re=O(),ie=O(),ae=37500,se=37510,ue=33723,oe=34675,fe=34665,ce=34853,he=40965,le=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],de=["jfif","xmp","icc","iptc","ihdr"],ve=["tiff"].concat(de),pe=["ifd0","ifd1","exif","gps","interop"],ye=[].concat(ve,pe),ge=["makerNote","userComment"],ke=["translateKeys","translateValues","reviveValues","multiSegment"],me=[].concat(ke,["sanitize","mergeOutput","silentErrors"]),be=function(){function e(){t(this,e)}return r(e,[{key:"translate",get:function(){return this.translateKeys||this.translateValues||this.reviveValues}}]),e}(),Ae=function(e){a(s,e);var n=d(s);function s(e,r,a,u){var o;if(t(this,s),i(h(o=n.call(this)),"enabled",!1),i(h(o),"skip",w()),i(h(o),"pick",w()),i(h(o),"deps",w()),i(h(o),"translateKeys",!1),i(h(o),"translateValues",!1),i(h(o),"reviveValues",!1),o.key=e,o.enabled=r,o.parse=o.enabled,o.applyInheritables(u),o.canBeFiltered=pe.includes(e),o.canBeFiltered&&(o.dict=ne.get(e)),void 0!==a)if(Array.isArray(a))o.parse=o.enabled=!0,o.canBeFiltered&&a.length>0&&o.translateTagSet(a,o.pick);else if("object"==typeof a){if(o.enabled=!0,o.parse=!1!==a.parse,o.canBeFiltered){var f=a.pick,c=a.skip;f&&f.length>0&&o.translateTagSet(f,o.pick),c&&c.length>0&&o.translateTagSet(c,o.skip)}o.applyInheritables(a)}else!0===a||!1===a?o.parse=o.enabled=a:T("Invalid options argument: ".concat(a));return o}return r(s,[{key:"needed",get:function(){return this.enabled||this.deps.size>0}},{key:"applyInheritables",value:function(e){var t,n,r=ke;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++)void 0!==(n=e[t=r[i]])&&(this[t]=n)}},{key:"translateTagSet",value:function(e,t){if(this.dict){var n,r,i=this.dict,a=i.tagKeys,s=i.tagValues,u=e;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=b(u));for(var o=0;o<u.length;o++)"string"==typeof(n=u[o])?(-1===(r=s.indexOf(n))&&(r=a.indexOf(Number(n))),-1!==r&&t.add(Number(a[r]))):t.add(n)}else{var f=e;Array.isArray(f)||("function"==typeof f.entries&&(f=f.entries()),f=b(f));for(var c=0;c<f.length;c++){var h=f[c];t.add(h)}}}},{key:"finalizeFilters",value:function(){!this.enabled&&this.deps.size>0?(this.enabled=!0,xe(this.pick,this.deps)):this.enabled&&this.pick.size>0&&xe(this.pick,this.deps)}}]),s}(be),we={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},Oe=O(),Pe=function(e){a(i,e);var n=d(i);function i(e){var r;return t(this,i),r=n.call(this),!0===e?r.setupFromTrue():void 0===e?r.setupFromUndefined():Array.isArray(e)?r.setupFromArray(e):"object"==typeof e?r.setupFromObject(e):T("Invalid options argument ".concat(e)),void 0===r.firstChunkSize&&(r.firstChunkSize=U?r.firstChunkSizeBrowser:r.firstChunkSizeNode),r.mergeOutput&&(r.ifd1.enabled=!1),r.filterNestedSegmentTags(),r.traverseTiffDependencyTree(),r.checkLoadedPlugins(),r}return r(i,[{key:"setupFromUndefined",value:function(){var e,t=le;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=b(t));for(var n=0;n<t.length;n++)this[e=t[n]]=we[e];var r=me;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++)this[e=r[i]]=we[e];var a=ge;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=b(a));for(var s=0;s<a.length;s++)this[e=a[s]]=we[e];var u=ye;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=b(u));for(var o=0;o<u.length;o++)this[e=u[o]]=new Ae(e,we[e],void 0,this)}},{key:"setupFromTrue",value:function(){var e,t=le;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=b(t));for(var n=0;n<t.length;n++)this[e=t[n]]=we[e];var r=me;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++)this[e=r[i]]=we[e];var a=ge;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=b(a));for(var s=0;s<a.length;s++)this[e=a[s]]=!0;var u=ye;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=b(u));for(var o=0;o<u.length;o++)this[e=u[o]]=new Ae(e,!0,void 0,this)}},{key:"setupFromArray",value:function(e){var t,n=le;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++)this[t=n[r]]=we[t];var i=me;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++)this[t=i[a]]=we[t];var s=ge;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=b(s));for(var u=0;u<s.length;u++)this[t=s[u]]=we[t];var o=ye;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=b(o));for(var f=0;f<o.length;f++)this[t=o[f]]=new Ae(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,pe)}},{key:"setupFromObject",value:function(e){var t;pe.ifd0=pe.ifd0||pe.image,pe.ifd1=pe.ifd1||pe.thumbnail,k(this,e);var n=le;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++)this[t=n[r]]=Ue(e[t],we[t]);var i=me;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++)this[t=i[a]]=Ue(e[t],we[t]);var s=ge;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=b(s));for(var u=0;u<s.length;u++)this[t=s[u]]=Ue(e[t],we[t]);var o=ve;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=b(o));for(var f=0;f<o.length;f++)this[t=o[f]]=new Ae(t,we[t],e[t],this);var c=pe;Array.isArray(c)||("function"==typeof c.entries&&(c=c.entries()),c=b(c));for(var h=0;h<c.length;h++)this[t=c[h]]=new Ae(t,we[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,pe,ye),!0===e.tiff?this.batchEnableWithBool(pe,!0):!1===e.tiff?this.batchEnableWithUserValue(pe,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,pe):"object"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,pe)}},{key:"batchEnableWithBool",value:function(e,t){var n=e;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){this[n[r]].enabled=t}}},{key:"batchEnableWithUserValue",value:function(e,t){var n=e;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r],a=t[i];this[i].enabled=!1!==a&&void 0!==a}}},{key:"setupGlobalFilters",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;if(e&&e.length){var i=r;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++){var s=i[a];this[s].enabled=!1}var u=Se(e,n),o=u;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=b(o));for(var f=0;f<o.length;f++){var c=o[f],h=c[0],l=c[1];xe(this[h].pick,l),this[h].enabled=!0}}else if(t&&t.length){var d=Se(t,n),v=d;Array.isArray(v)||("function"==typeof v.entries&&(v=v.entries()),v=b(v));for(var p=0;p<v.length;p++){var y=v[p],g=y[0],k=y[1];xe(this[g].skip,k)}}}},{key:"filterNestedSegmentTags",value:function(){var e=this.ifd0,t=this.exif,n=this.xmp,r=this.iptc,i=this.icc;this.makerNote?t.deps.add(ae):t.skip.add(ae),this.userComment?t.deps.add(se):t.skip.add(se),n.enabled||e.skip.add(700),r.enabled||e.skip.add(ue),i.enabled||e.skip.add(oe)}},{key:"traverseTiffDependencyTree",value:function(){var e=this,t=this.ifd0,n=this.exif,r=this.gps;this.interop.needed&&(n.deps.add(he),t.deps.add(he)),n.needed&&t.deps.add(fe),r.needed&&t.deps.add(ce),this.tiff.enabled=pe.some((function(t){return!0===e[t].enabled}))||this.makerNote||this.userComment;var i=pe;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++){this[i[a]].finalizeFilters()}}},{key:"onlyTiff",get:function(){var e=this;return!de.map((function(t){return e[t].enabled})).some((function(e){return!0===e}))&&this.tiff.enabled}},{key:"checkLoadedPlugins",value:function(){var e=ve;Array.isArray(e)||("function"==typeof e.entries&&(e=e.entries()),e=b(e));for(var t=0;t<e.length;t++){var n=e[t];this[n].enabled&&!M.has(n)&&D("segment parser",n)}}}],[{key:"useCached",value:function(e){var t=Oe.get(e);return void 0!==t||(t=new this(e),Oe.set(e,t)),t}}]),i}(be);function Se(e,t){var n,r,i,a=[],s=t;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=b(s));for(var u=0;u<s.length;u++){r=s[u],n=[];var o=ne.get(r);Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=b(o));for(var f=0;f<o.length;f++)i=o[f],(e.includes(i[0])||e.includes(i[1]))&&n.push(i[0]);n.length&&a.push([r,n])}return a}function Ue(e,t){return void 0!==e?e:void 0!==t?t:void 0}function xe(e,t){var n=t;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r];e.add(i)}}function Ce(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function je(){}function Be(e,t){if(!t)return e&&e.then?e.then(je):Promise.resolve()}function _e(e,t){var n=e();return n&&n.then?n.then(t):t(n)}i(Pe,"default",we);var Ve=function(){function e(n){var r=this;t(this,e),i(this,"parsers",{}),i(this,"output",{}),i(this,"errors",[]),i(this,"pushToErrors",(function(e){return r.errors.push(e)})),this.options=Pe.useCached(n)}return r(e,[{key:"read",value:function(e){try{var t=this;return Ce(function(e,t){return"string"==typeof e?q(e,t):U&&!x&&e instanceof HTMLImageElement?q(e.src,t):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new E(e):U&&e instanceof Blob?G(e,t,"blob",Z):void T(J)}(e,t.options),(function(e){t.file=e}))}catch(e){return Promise.reject(e)}}},{key:"setup",value:function(){if(!this.fileParser){var e=this.file,t=e.getUint16(0),n=N;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],s=i[1];if(s.canHandle(e,t))return this.fileParser=new s(this.options,this.file,this.parsers),e[a]=!0}this.file.close&&this.file.close(),T("Unknown file format")}}},{key:"parse",value:function(){try{var e=this,t=e.output,n=e.errors;return e.setup(),_e((function(){return e.options.silentErrors?Ce(e.executeParsers().catch(e.pushToErrors),(function(){n.push.apply(n,e.fileParser.errors)})):Be(e.executeParsers())}),(function(){return e.file.close&&e.file.close(),e.options.silentErrors&&n.length>0&&(t.errors=n),V(r=t)?void 0:r;var r}))}catch(e){return Promise.reject(e)}}},{key:"executeParsers",value:function(){try{var e=this,t=e.output;return Ce(e.fileParser.parse(),(function(){var n=y(e.parsers).map(function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){return Ce(e.parse(),(function(n){e.assignToOutput(t,n)}))})));return e.options.silentErrors&&(n=n.map((function(t){return t.catch(e.pushToErrors)}))),Be(Promise.all(n))}))}catch(e){return Promise.reject(e)}}},{key:"extractThumbnail",value:function(){try{var e=this;e.setup();var t,n=e.options,r=e.file,i=M.get("tiff",n);return _e((function(){if(!r.tiff)return function(e){var t=e();if(t&&t.then)return t.then(je)}((function(){if(r.jpeg)return Ce(e.fileParser.getOrFindSegment("tiff"),(function(e){t=e}))}));t={start:0,type:"tiff"}}),(function(){if(void 0!==t)return Ce(e.fileParser.ensureSegmentChunk(t),(function(t){return Ce((e.parsers.tiff=new i(t,n,r)).extractThumbnail(),(function(e){return r.close&&r.close(),e}))}))}))}catch(e){return Promise.reject(e)}}}]),e}();var Te=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e,t){var n,r,i,a=new Ve(t);return n=a.read(e),r=function(){return a.parse()},i?r?r(n):n:(n&&n.then||(n=Promise.resolve(n)),r?n.then(r):n)})),Ie=Object.freeze({__proto__:null,parse:Te,Exifr:Ve,fileParsers:N,segmentParsers:M,fileReaders:W,tagKeys:ne,tagValues:re,tagRevivers:ie,createDictionary:ee,extendDictionary:te,fetchUrlAsArrayBuffer:Q,readBlobAsArrayBuffer:Z,chunkedProps:le,otherSegments:de,segments:ve,tiffBlocks:pe,segmentsAndBlocks:ye,tiffExtractables:ge,inheritables:ke,allFormatters:me,Options:Pe});function Le(){}var ze=function(){function e(n,r,a){var s=this;t(this,e),i(this,"errors",[]),i(this,"ensureSegmentChunk",function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t,n,r,i=e.start,a=e.size||65536;return t=function(){if(s.file.chunked)return function(e){var t=e();if(t&&t.then)return t.then(Le)}((function(){if(!s.file.available(i,a))return function(e){if(e&&e.then)return e.then(Le)}(function(e,t){try{var n=e()}catch(e){return t(e)}return n&&n.then?n.then(void 0,t):n}((function(){return t=s.file.readChunk(i,a),n=function(t){e.chunk=t},r?n?n(t):t:(t&&t.then||(t=Promise.resolve(t)),n?t.then(n):t);var t,n,r}),(function(t){T("Couldn't read segment: ".concat(JSON.stringify(e),". ").concat(t.message))})));e.chunk=s.file.subarray(i,a)}));s.file.byteLength>i+a?e.chunk=s.file.subarray(i,a):void 0===e.size?e.chunk=s.file.subarray(i):T("Segment unreachable: "+JSON.stringify(e))},n=function(){return e.chunk},(r=t())&&r.then?r.then(n):n(r)}))),this.extendOptions&&this.extendOptions(n),this.options=n,this.file=r,this.parsers=a}return r(e,[{key:"injectSegment",value:function(e,t){this.options[e].enabled&&this.createParser(e,t)}},{key:"createParser",value:function(e,t){var n=new(M.get(e))(t,this.options,this.file);return this.parsers[e]=n}},{key:"createParsers",value:function(e){var t=e;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=b(t));for(var n=0;n<t.length;n++){var r=t[n],i=r.type,a=r.chunk,s=this.options[i];if(s&&s.enabled){var u=this.parsers[i];u&&u.append||u||this.createParser(i,a)}}}},{key:"readSegments",value:function(e){try{var t=e.map(this.ensureSegmentChunk);return function(e,t){if(!t)return e&&e.then?e.then(Le):Promise.resolve()}(Promise.all(t))}catch(e){return Promise.reject(e)}}}]),e}(),Fe=function(){function e(n){var r=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;t(this,e),i(this,"errors",[]),i(this,"raw",O()),i(this,"handleError",(function(e){if(!r.options.silentErrors)throw e;r.errors.push(e.message)})),this.chunk=this.normalizeInput(n),this.file=s,this.type=this.constructor.type,this.globalOptions=this.options=a,this.localOptions=a[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}return r(e,[{key:"normalizeInput",value:function(e){return e instanceof E?e:new E(e)}},{key:"translate",value:function(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}},{key:"output",get:function(){return this.translated?this.translated:this.raw?m(this.raw):void 0}},{key:"translateBlock",value:function(e,t){var n=ie.get(t),r=re.get(t),i=ne.get(t),a=this.options[t],s=a.reviveValues&&!!n,u=a.translateValues&&!!r,o=a.translateKeys&&!!i,f={},c=e;Array.isArray(c)||("function"==typeof c.entries&&(c=c.entries()),c=b(c));for(var h=0;h<c.length;h++){var l=c[h],d=l[0],v=l[1];s&&n.has(d)?v=n.get(d)(v):u&&r.has(d)&&(v=this.translateValue(v,r.get(d))),o&&i.has(d)&&(d=i.get(d)||d),f[d]=v}return f}},{key:"translateValue",value:function(e,t){return t[e]||t.DEFAULT||e}},{key:"assignToOutput",value:function(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}},{key:"assignObjectToOutput",value:function(e,t,n){if(this.globalOptions.mergeOutput)return k(e,n);e[t]?k(e[t],n):e[t]=n}}],[{key:"findPosition",value:function(e,t){var n=e.getUint16(t+2)+2,r="function"==typeof this.headerLength?this.headerLength(e,t,n):this.headerLength,i=t+r,a=n-r;return{offset:t,length:n,headerLength:r,start:i,size:a,end:i+a}}},{key:"parse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=new Pe(i({},this.type,t)),r=new this(e,n,e);return r.parse()}}]),e}();function Ee(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}i(Fe,"headerLength",4),i(Fe,"type",void 0),i(Fe,"multiSegment",!1),i(Fe,"canHandle",(function(){return!1}));function De(){}function Re(e,t){if(!t)return e&&e.then?e.then(De):Promise.resolve()}function Ne(e){var t=e();if(t&&t.then)return t.then(De)}function Me(e,t){var n=e();return n&&n.then?n.then(t):t(n)}function We(e,t,n){if(!e.s){if(n instanceof Ke){if(!n.s)return void(n.o=We.bind(null,e,t));1&t&&(t=n.s),n=n.v}if(n&&n.then)return void n.then(We.bind(null,e,t),We.bind(null,e,2));e.s=t,e.v=n;var r=e.o;r&&r(e)}}var Ke=function(){function e(){}return e.prototype.then=function(t,n){var r=new e,i=this.s;if(i){var a=1&i?t:n;if(a){try{We(r,1,a(this.v))}catch(e){We(r,2,e)}return r}return this}return this.o=function(e){try{var i=e.v;1&e.s?We(r,1,t?t(i):i):n?We(r,1,n(i)):We(r,2,i)}catch(e){We(r,2,e)}},r},e}();function He(e){return e instanceof Ke&&1&e.s}function Xe(e,t,n){for(var r;;){var i=e();if(He(i)&&(i=i.v),!i)return a;if(i.then){r=0;break}var a=n();if(a&&a.then){if(!He(a)){r=1;break}a=a.s}if(t){var s=t();if(s&&s.then&&!He(s)){r=2;break}}}var u=new Ke,o=We.bind(null,u,2);return(0===r?i.then(c):1===r?a.then(f):s.then(h)).then(void 0,o),u;function f(r){a=r;do{if(t&&(s=t())&&s.then&&!He(s))return void s.then(h).then(void 0,o);if(!(i=e())||He(i)&&!i.v)return void We(u,1,a);if(i.then)return void i.then(c).then(void 0,o);He(a=n())&&(a=a.v)}while(!a||!a.then);a.then(f).then(void 0,o)}function c(e){e?(a=n())&&a.then?a.then(f).then(void 0,o):f(a):We(u,1,a)}function h(){(i=e())?i.then?i.then(c).then(void 0,o):c(i):We(u,1,a)}}function Ye(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function Ge(e){return e>=224&&e<=239}function Je(e,t,n){var r=M;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++){var a=r[i],s=a[0];if(a[1].canHandle(e,t,n))return s}}var qe=function(e){a(s,e);var n=d(s);function s(){var e;t(this,s);for(var r=arguments.length,a=new Array(r),u=0;u<r;u++)a[u]=arguments[u];return i(h(e=n.call.apply(n,[this].concat(a))),"appSegments",[]),i(h(e),"jpegSegments",[]),i(h(e),"unknownSegments",[]),e}return r(s,[{key:"parse",value:function(){try{var e=this;return Ee(e.findAppSegments(),(function(){return Ee(e.readSegments(e.appSegments),(function(){e.mergeMultiSegments(),e.createParsers(e.mergedAppSegments||e.appSegments)}))}))}catch(e){return Promise.reject(e)}}},{key:"setupSegmentFinderArgs",value:function(e){var t=this;!0===e?(this.findAll=!0,this.wanted=w(M.keyList())):(e=void 0===e?M.keyList().filter((function(e){return t.options[e].enabled})):e.filter((function(e){return t.options[e].enabled&&M.has(e)})),this.findAll=!1,this.remaining=w(e),this.wanted=w(e)),this.unfinishedMultiSegment=!1}},{key:"findAppSegments",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;try{var n=this;n.setupSegmentFinderArgs(t);var r=n.file,i=n.findAll,a=n.wanted,s=n.remaining;return Me((function(){if(!i&&n.file.chunked)return i=b(a).some((function(e){var t=M.get(e),r=n.options[e];return t.multiSegment&&r.multiSegment})),Ne((function(){if(i)return Re(n.file.readWhole())}))}),(function(){var t=!1;if(e=n.findAppSegmentsInRange(e,r.byteLength),!n.options.onlyTiff)return function(){if(r.chunked){var i=!1;return Xe((function(){return!t&&s.size>0&&!i&&(!!r.canReadNextChunk||!!n.unfinishedMultiSegment)}),void 0,(function(){var a=r.nextChunkOffset,s=n.appSegments.some((function(e){return!n.file.available(e.offset||e.start,e.length||e.size)}));return Me((function(){return e>a&&!s?Ee(r.readNextChunk(e),(function(e){i=!e})):Ee(r.readNextChunk(a),(function(e){i=!e}))}),(function(){void 0===(e=n.findAppSegmentsInRange(e,r.byteLength))&&(t=!0)}))}))}}()}))}catch(e){return Promise.reject(e)}}},{key:"findAppSegmentsInRange",value:function(e,t){t-=2;for(var n,r,i,a,s,u,o=this.file,f=this.findAll,c=this.wanted,h=this.remaining,l=this.options;e<t;e++)if(255===o.getUint8(e))if(Ge(n=o.getUint8(e+1))){if(r=o.getUint16(e+2),(i=Je(o,e,r))&&c.has(i)&&(s=(a=M.get(i)).findPosition(o,e),u=l[i],s.type=i,this.appSegments.push(s),!f&&(a.multiSegment&&u.multiSegment?(this.unfinishedMultiSegment=s.chunkNumber<s.chunkCount,this.unfinishedMultiSegment||h.delete(i)):h.delete(i),0===h.size)))break;l.recordUnknownSegments&&((s=Fe.findPosition(o,e)).marker=n,this.unknownSegments.push(s)),e+=r+1}else if(Ye(n)){if(r=o.getUint16(e+2),218===n&&!1!==l.stopAfterSos)return;l.recordJpegSegments&&this.jpegSegments.push({offset:e,length:r,marker:n}),e+=r+1}return e}},{key:"mergeMultiSegments",value:function(){var e=this;if(this.appSegments.some((function(e){return e.multiSegment}))){var t=function(e,t){for(var n,r,i,a=O(),s=0;s<e.length;s++)r=(n=e[s])[t],a.has(r)?i=a.get(r):a.set(r,i=[]),i.push(n);return b(a)}(this.appSegments,"type");this.mergedAppSegments=t.map((function(t){var n=t[0],r=t[1],i=M.get(n,e.options);return i.handleMultiSegments?{type:n,chunk:i.handleMultiSegments(r)}:r[0]}))}}},{key:"getSegment",value:function(e){return this.appSegments.find((function(t){return t.type===e}))}},{key:"getOrFindSegment",value:function(e){try{var t=this,n=t.getSegment(e);return Me((function(){if(void 0===n)return Ee(t.findAppSegments(0,[e]),(function(){n=t.getSegment(e)}))}),(function(){return n}))}catch(e){return Promise.reject(e)}}}],[{key:"canHandle",value:function(e,t){return 65496===t}}]),s}(ze);function Qe(){}i(qe,"type","jpeg"),N.set("jpeg",qe);function Ze(e,t){if(!t)return e&&e.then?e.then(Qe):Promise.resolve()}function $e(e,t){var n=e();return n&&n.then?n.then(t):t(n)}var et=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];var tt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){try{var e=this;e.parseHeader();var t=e.options;return $e((function(){if(t.ifd0.enabled)return Ze(e.parseIfd0Block())}),(function(){return $e((function(){if(t.exif.enabled)return Ze(e.safeParse("parseExifBlock"))}),(function(){return $e((function(){if(t.gps.enabled)return Ze(e.safeParse("parseGpsBlock"))}),(function(){return $e((function(){if(t.interop.enabled)return Ze(e.safeParse("parseInteropBlock"))}),(function(){return $e((function(){if(t.ifd1.enabled)return Ze(e.safeParse("parseThumbnailBlock"))}),(function(){return e.createOutput()}))}))}))}))}))}catch(e){return Promise.reject(e)}}},{key:"safeParse",value:function(e){var t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}},{key:"findIfd0Offset",value:function(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}},{key:"findIfd1Offset",value:function(){if(void 0===this.ifd1Offset){this.findIfd0Offset();var e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}},{key:"parseBlock",value:function(e,t){var n=O();return this[t]=n,this.parseTags(e,t,n),n}},{key:"parseIfd0Block",value:function(){try{var e=this;if(e.ifd0)return;var t=e.file;return e.findIfd0Offset(),e.ifd0Offset<8&&T("Malformed EXIF data"),!t.chunked&&e.ifd0Offset>t.byteLength&&T("IFD0 offset points to outside of file.\nthis.ifd0Offset: ".concat(e.ifd0Offset,", file.byteLength: ").concat(t.byteLength)),$e((function(){if(t.tiff)return Ze(t.ensureChunk(e.ifd0Offset,I(e.options)))}),(function(){var t=e.parseBlock(e.ifd0Offset,"ifd0");if(0!==t.size)return e.exifOffset=t.get(fe),e.interopOffset=t.get(he),e.gpsOffset=t.get(ce),e.xmp=t.get(700),e.iptc=t.get(ue),e.icc=t.get(oe),e.options.sanitize&&(t.delete(fe),t.delete(he),t.delete(ce),t.delete(700),t.delete(ue),t.delete(oe)),t}))}catch(e){return Promise.reject(e)}}},{key:"parseExifBlock",value:function(){try{var e=this;if(e.exif)return;return $e((function(){if(!e.ifd0)return Ze(e.parseIfd0Block())}),(function(){if(void 0!==e.exifOffset)return $e((function(){if(e.file.tiff)return Ze(e.file.ensureChunk(e.exifOffset,I(e.options)))}),(function(){var t=e.parseBlock(e.exifOffset,"exif");return e.interopOffset||(e.interopOffset=t.get(he)),e.makerNote=t.get(ae),e.userComment=t.get(se),e.options.sanitize&&(t.delete(he),t.delete(ae),t.delete(se)),e.unpack(t,41728),e.unpack(t,41729),t}))}))}catch(e){return Promise.reject(e)}}},{key:"unpack",value:function(e,t){var n=e.get(t);n&&1===n.length&&e.set(t,n[0])}},{key:"parseGpsBlock",value:function(){try{var e=this;if(e.gps)return;return $e((function(){if(!e.ifd0)return Ze(e.parseIfd0Block())}),(function(){if(void 0!==e.gpsOffset){var t=e.parseBlock(e.gpsOffset,"gps");return t&&t.has(2)&&t.has(4)&&(t.set("latitude",nt.apply(void 0,t.get(2).concat([t.get(1)]))),t.set("longitude",nt.apply(void 0,t.get(4).concat([t.get(3)])))),t}}))}catch(e){return Promise.reject(e)}}},{key:"parseInteropBlock",value:function(){try{var e=this;if(e.interop)return;return $e((function(){if(!e.ifd0)return Ze(e.parseIfd0Block())}),(function(){return $e((function(){if(void 0===e.interopOffset&&!e.exif)return Ze(e.parseExifBlock())}),(function(){if(void 0!==e.interopOffset)return e.parseBlock(e.interopOffset,"interop")}))}))}catch(e){return Promise.reject(e)}}},{key:"parseThumbnailBlock",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{var t=this;if(t.ifd1||t.ifd1Parsed)return;if(t.options.mergeOutput&&!e)return;return t.findIfd1Offset(),t.ifd1Offset>0&&(t.parseBlock(t.ifd1Offset,"ifd1"),t.ifd1Parsed=!0),t.ifd1}catch(e){return Promise.reject(e)}}},{key:"extractThumbnail",value:function(){try{var e=this;return e.headerParsed||e.parseHeader(),$e((function(){if(!e.ifd1Parsed)return Ze(e.parseThumbnailBlock(!0))}),(function(){if(void 0!==e.ifd1){var t=e.ifd1.get(513),n=e.ifd1.get(514);return e.chunk.getUint8Array(t,n)}}))}catch(e){return Promise.reject(e)}}},{key:"image",get:function(){return this.ifd0}},{key:"thumbnail",get:function(){return this.ifd1}},{key:"createOutput",value:function(){var e,t,n,r={},i=pe;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++)if(!V(e=this[t=i[a]]))if(n=this.canTranslate?this.translateBlock(e,t):m(e),this.options.mergeOutput){if("ifd1"===t)continue;k(r,n)}else r[t]=n;return this.makerNote&&(r.makerNote=this.makerNote),this.userComment&&(r.userComment=this.userComment),r}},{key:"assignToOutput",value:function(e,t){if(this.globalOptions.mergeOutput)k(e,t);else{var n=g(t);Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],s=i[1];this.assignObjectToOutput(e,a,s)}}}}],[{key:"canHandle",value:function(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}}]),i}(function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parseHeader",value:function(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}},{key:"parseTags",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:O(),r=this.options[t],i=r.pick,a=r.skip,s=(i=w(i)).size>0,u=0===a.size,o=this.chunk.getUint16(e);e+=2;for(var f=0;f<o;f++){var c=this.chunk.getUint16(e);if(s){if(i.has(c)&&(n.set(c,this.parseTag(e,c,t)),i.delete(c),0===i.size))break}else!u&&a.has(c)||n.set(c,this.parseTag(e,c,t));e+=12}return n}},{key:"parseTag",value:function(e,t,n){var r,i=this.chunk,a=i.getUint16(e+2),s=i.getUint32(e+4),u=et[a];if(u*s<=4?e+=8:e=i.getUint32(e+8),(a<1||a>13)&&T("Invalid TIFF value type. block: ".concat(n.toUpperCase(),", tag: ").concat(t.toString(16),", type: ").concat(a,", offset ").concat(e)),e>i.byteLength&&T("Invalid TIFF value offset. block: ".concat(n.toUpperCase(),", tag: ").concat(t.toString(16),", type: ").concat(a,", offset ").concat(e," is outside of chunk size ").concat(i.byteLength)),1===a)return i.getUint8Array(e,s);if(2===a)return""===(r=function(e){for(;e.endsWith("\0");)e=e.slice(0,-1);return e}(r=i.getString(e,s)).trim())?void 0:r;if(7===a)return i.getUint8Array(e,s);if(1===s)return this.parseTagValue(a,e);for(var o=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(a))(s),f=u,c=0;c<s;c++)o[c]=this.parseTagValue(a,e),e+=f;return o}},{key:"parseTagValue",value:function(e,t){var n=this.chunk;switch(e){case 1:return n.getUint8(t);case 3:return n.getUint16(t);case 4:return n.getUint32(t);case 5:return n.getUint32(t)/n.getUint32(t+4);case 6:return n.getInt8(t);case 8:return n.getInt16(t);case 9:return n.getInt32(t);case 10:return n.getInt32(t)/n.getInt32(t+4);case 11:return n.getFloat(t);case 12:return n.getDouble(t);case 13:return n.getUint32(t);default:T("Invalid tiff type ".concat(e))}}}]),i}(Fe));function nt(e,t,n,r){var i=e+t/60+n/3600;return"S"!==r&&"W"!==r||(i*=-1),i}i(tt,"type","tiff"),i(tt,"headerLength",10),M.set("tiff",tt);var rt=Object.freeze({__proto__:null,default:Ie,Exifr:Ve,fileParsers:N,segmentParsers:M,fileReaders:W,tagKeys:ne,tagValues:re,tagRevivers:ie,createDictionary:ee,extendDictionary:te,fetchUrlAsArrayBuffer:Q,readBlobAsArrayBuffer:Z,chunkedProps:le,otherSegments:de,segments:ve,tiffBlocks:pe,segmentsAndBlocks:ye,tiffExtractables:ge,inheritables:ke,allFormatters:me,Options:Pe,parse:Te}),it={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};function at(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var st=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t=new Ve(ut);return at(t.read(e),(function(){return at(t.parse(),(function(e){if(e&&e.gps){var t=e.gps;return{latitude:t.latitude,longitude:t.longitude}}}))}))})),ut=k({},it,{firstChunkSize:4e4,gps:[1,2,3,4]});function ot(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function ft(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var ct=ft((function(e){return ot(this.thumbnail(e),(function(e){if(void 0!==e){var t=new Blob([e]);return URL.createObjectURL(t)}}))})),ht=ft((function(e){var t=new Ve(lt);return ot(t.read(e),(function(){return ot(t.extractThumbnail(),(function(e){return e&&B?j.from(e):e}))}))})),lt=k({},it,{tiff:!1,ifd1:!0,mergeOutput:!1});function dt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var vt=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t=new Ve(pt);return dt(t.read(e),(function(){return dt(t.parse(),(function(e){if(e&&e.ifd0)return e.ifd0[274]}))}))})),pt=k({},it,{firstChunkSize:4e4,ifd0:[274]}),yt=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});if(e.rotateCanvas=!0,e.rotateCss=!0,"object"==typeof navigator){var gt=navigator.userAgent;if(gt.includes("iPad")||gt.includes("iPhone")){var kt=gt.match(/OS (\d+)_(\d+)/);if(kt){var mt=kt[1],bt=kt[2],At=Number(mt)+.1*Number(bt);e.rotateCanvas=At<13.4,e.rotateCss=!1}}else if(gt.includes("OS X 10")){var wt=gt.match(/OS X 10[_.](\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(wt)<15}if(gt.includes("Chrome/")){var Ot=gt.match(/Chrome\/(\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(Ot)<81}else if(gt.includes("Firefox/")){var Pt=gt.match(/Firefox\/(\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(Pt)<77}}function St(){}var Ut=function(e){a(u,e);var n=d(u);function u(){var e;t(this,u);for(var r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];return i(h(e=n.call.apply(n,[this].concat(a))),"ranges",new xt),0!==e.byteLength&&e.ranges.add(0,e.byteLength),e}return r(u,[{key:"_tryExtend",value:function(e,t,n){if(0===e&&0===this.byteLength&&n){var r=new DataView(n.buffer||n,n.byteOffset,n.byteLength);this._swapDataView(r)}else{var i=e+t;if(i>this.byteLength){var a=this._extend(i).dataView;this._swapDataView(a)}}}},{key:"_extend",value:function(e){var t;t=B?j.allocUnsafe(e):new Uint8Array(e);var n=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:n}}},{key:"subarray",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t=t||this._lengthToEnd(e),n&&this._tryExtend(e,t),this.ranges.add(e,t),v(s(u.prototype),"subarray",this).call(this,e,t)}},{key:"set",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n&&this._tryExtend(t,e.byteLength,e);var r=v(s(u.prototype),"set",this).call(this,e,t);return this.ranges.add(t,r.byteLength),r}},{key:"ensureChunk",value:function(e,t){try{var n=this;if(!n.chunked)return;if(n.ranges.available(e,t))return;return function(e,t){if(!t)return e&&e.then?e.then(St):Promise.resolve()}(n.readChunk(e,t))}catch(e){return Promise.reject(e)}}},{key:"available",value:function(e,t){return this.ranges.available(e,t)}}]),u}(E),xt=function(){function e(){t(this,e),i(this,"list",[])}return r(e,[{key:"length",get:function(){return this.list.length}},{key:"add",value:function(e,t){var n=e+t,r=this.list.filter((function(t){return Ct(e,t.offset,n)||Ct(e,t.end,n)}));if(r.length>0){e=Math.min.apply(Math,[e].concat(r.map((function(e){return e.offset})))),t=(n=Math.max.apply(Math,[n].concat(r.map((function(e){return e.end})))))-e;var i=r.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((function(e){return!r.includes(e)}))}else this.list.push({offset:e,length:t,end:n})}},{key:"available",value:function(e,t){var n=e+t;return this.list.some((function(t){return t.offset<=e&&n<=t.end}))}}]),e}();function Ct(e,t,n){return e<=t&&t<=n}function jt(){}function Bt(e,t){if(!t)return e&&e.then?e.then(jt):Promise.resolve()}function _t(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var Vt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,_t(Z(e.input),(function(t){e._swapArrayBuffer(t)}))}catch(e){return Promise.reject(e)}}},{key:"readChunked",value:function(){return this.chunked=!0,this.size=this.input.size,v(s(i.prototype),"readChunked",this).call(this)}},{key:"_readChunk",value:function(e,t){try{var n=this,r=t?e+t:void 0,i=n.input.slice(e,r);return _t(Z(i),(function(t){return n.set(t,e,!0)}))}catch(e){return Promise.reject(e)}}}]),i}(function(e){a(s,e);var n=d(s);function s(e,r){var a;return t(this,s),i(h(a=n.call(this,0)),"chunksRead",0),a.input=e,a.options=r,a}return r(s,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,Bt(e.readChunk(e.nextChunkOffset))}catch(e){return Promise.reject(e)}}},{key:"readChunked",value:function(){try{var e=this;return e.chunked=!0,Bt(e.readChunk(0,e.options.firstChunkSize))}catch(e){return Promise.reject(e)}}},{key:"readNextChunk",value:function(e){try{var t=this;if(void 0===e&&(e=t.nextChunkOffset),t.fullyRead)return t.chunksRead++,!1;var n=t.options.chunkSize;return r=t.readChunk(e,n),i=function(e){return!!e&&e.byteLength===n},a?i?i(r):r:(r&&r.then||(r=Promise.resolve(r)),i?r.then(i):r)}catch(e){return Promise.reject(e)}var r,i,a}},{key:"readChunk",value:function(e,t){try{var n=this;if(n.chunksRead++,0===(t=n.safeWrapAddress(e,t)))return;return n._readChunk(e,t)}catch(e){return Promise.reject(e)}}},{key:"safeWrapAddress",value:function(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}},{key:"nextChunkOffset",get:function(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}},{key:"canReadNextChunk",get:function(){return this.chunksRead<this.options.chunkLimit}},{key:"fullyRead",get:function(){return void 0!==this.size&&this.nextChunkOffset===this.size}},{key:"read",value:function(){return this.options.chunked?this.readChunked():this.readWhole()}},{key:"close",value:function(){}}]),s}(Ut));W.set("blob",Vt),e.Exifr=Ve,e.Options=Pe,e.allFormatters=me,e.chunkedProps=le,e.createDictionary=ee,e.default=rt,e.extendDictionary=te,e.fetchUrlAsArrayBuffer=Q,e.fileParsers=N,e.fileReaders=W,e.gps=st,e.gpsOnlyOptions=ut,e.inheritables=ke,e.orientation=vt,e.orientationOnlyOptions=pt,e.otherSegments=de,e.parse=Te,e.readBlobAsArrayBuffer=Z,e.rotation=function(t){return dt(vt(t),(function(t){return k({canvas:e.rotateCanvas,css:e.rotateCss},yt[t])}))},e.rotations=yt,e.segmentParsers=M,e.segments=ve,e.segmentsAndBlocks=ye,e.tagKeys=ne,e.tagRevivers=ie,e.tagValues=re,e.thumbnail=ht,e.thumbnailOnlyOptions=lt,e.thumbnailUrl=ct,e.tiffBlocks=pe,e.tiffExtractables=ge,Object.defineProperty(e,"__esModule",{value:!0})}));
