function e(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var t="undefined"!=typeof self?self:global;const i="undefined"!=typeof navigator,s=i&&"undefined"==typeof HTMLImageElement,n=!("undefined"==typeof global||"undefined"==typeof process||!process.versions||!process.versions.node),r=t.<PERSON>uff<PERSON>,a=t.BigInt,o=!!r,h=e=>f(e)?void 0:e,l=e=>void 0!==e;function f(e){return void 0===e||(e instanceof Map?0===e.size:0===Object.values(e).filter(l).length)}function u(e){let t=new Error(e);throw delete t.stack,t}function d(e){return""===(e=function(e){for(;e.endsWith("\0");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function c(e){let t=function(e){let t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}const g=e=>String.fromCharCode.apply(null,e),p="undefined"!=typeof TextDecoder?new TextDecoder("utf-8"):void 0;function m(e){return p?p.decode(e):o?Buffer.from(e).toString("utf8"):decodeURIComponent(escape(g(e)))}class y{static from(e,t){return e instanceof this&&e.le===t?e:new y(e,void 0,void 0,t)}constructor(e,t=0,i,s){if("boolean"==typeof s&&(this.le=s),Array.isArray(e)&&(e=new Uint8Array(e)),0===e)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){void 0===i&&(i=e.byteLength-t);let s=new DataView(e,t,i);this._swapDataView(s)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof y){void 0===i&&(i=e.byteLength-t),(t+=e.byteOffset)+i>e.byteOffset+e.byteLength&&u("Creating view outside of available memory in ArrayBuffer");let s=new DataView(e.buffer,t,i);this._swapDataView(s)}else if("number"==typeof e){let t=new DataView(new ArrayBuffer(e));this._swapDataView(t)}else u("Invalid input argument for BufferView: "+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,i=y){return e instanceof DataView||e instanceof y?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||u("BufferView.set(): Invalid data argument."),this.toUint8().set(e,t),new i(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new y(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return m(this.getUint8Array(e,t))}getLatin1String(e=0,t=this.byteLength){let i=this.getUint8Array(e,t);return g(i)}getUnicodeString(e=0,t=this.byteLength){const i=[];for(let s=0;s<t&&e+s<this.byteLength;s+=2)i.push(this.getUint16(e+s));return g(i)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,i){switch(t){case 1:return this.getUint8(e,i);case 2:return this.getUint16(e,i);case 4:return this.getUint32(e,i);case 8:return this.getUint64&&this.getUint64(e,i)}}getUint(e,t,i){switch(t){case 8:return this.getUint8(e,i);case 16:return this.getUint16(e,i);case 32:return this.getUint32(e,i);case 64:return this.getUint64&&this.getUint64(e,i)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function b(e,t){u(`${e} '${t}' was not loaded, try using full build of exifr.`)}class w extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||b(this.kind,e),t&&(e in t||function(e,t){u(`Unknown ${e} '${t}'.`)}(this.kind,e),t[e].enabled||b(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var S=new w("file parser"),k=new w("segment parser"),v=new w("file reader");let O=t.fetch;function x(e,t){return(s=e).startsWith("data:")||s.length>1e4?P(e,t,"base64"):n&&e.includes("://")?C(e,t,"url",A):n?P(e,t,"fs"):i?C(e,t,"url",A):void u("Invalid input argument");var s}async function C(e,t,i,s){return v.has(i)?P(e,t,i):s?async function(e,t){let i=await t(e);return new y(i)}(e,s):void u(`Parser ${i} is not loaded`)}async function P(e,t,i){let s=new(v.get(i))(e,t);return await s.read(),s}const A=e=>O(e).then((e=>e.arrayBuffer())),U=e=>new Promise(((t,i)=>{let s=new FileReader;s.onloadend=()=>t(s.result||new ArrayBuffer),s.onerror=i,s.readAsArrayBuffer(e)}));class I extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}}function B(e,t,i){let s=new I;for(let[e,t]of i)s.set(e,t);if(Array.isArray(t))for(let i of t)e.set(i,s);else e.set(t,s);return s}function F(e,t,i){let s,n=e.get(t);for(s of i)n.set(s[0],s[1])}const L=new Map,D=new Map,T=new Map,z=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],N=["jfif","xmp","icc","iptc","ihdr"],V=["tiff",...N],M=["ifd0","ifd1","exif","gps","interop"],E=[...V,...M],R=["makerNote","userComment"],j=["translateKeys","translateValues","reviveValues","multiSegment"],G=[...j,"sanitize","mergeOutput","silentErrors"];class H{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class _ extends H{get needed(){return this.enabled||this.deps.size>0}constructor(t,i,s,n){if(super(),e(this,"enabled",!1),e(this,"skip",new Set),e(this,"pick",new Set),e(this,"deps",new Set),e(this,"translateKeys",!1),e(this,"translateValues",!1),e(this,"reviveValues",!1),this.key=t,this.enabled=i,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=M.includes(t),this.canBeFiltered&&(this.dict=L.get(t)),void 0!==s)if(Array.isArray(s))this.parse=this.enabled=!0,this.canBeFiltered&&s.length>0&&this.translateTagSet(s,this.pick);else if("object"==typeof s){if(this.enabled=!0,this.parse=!1!==s.parse,this.canBeFiltered){let{pick:e,skip:t}=s;e&&e.length>0&&this.translateTagSet(e,this.pick),t&&t.length>0&&this.translateTagSet(t,this.skip)}this.applyInheritables(s)}else!0===s||!1===s?this.parse=this.enabled=s:u(`Invalid options argument: ${s}`)}applyInheritables(e){let t,i;for(t of j)i=e[t],void 0!==i&&(this[t]=i)}translateTagSet(e,t){if(this.dict){let i,s,{tagKeys:n,tagValues:r}=this.dict;for(i of e)"string"==typeof i?(s=r.indexOf(i),-1===s&&(s=n.indexOf(Number(i))),-1!==s&&t.add(Number(n[s]))):t.add(i)}else for(let i of e)t.add(i)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,q(this.pick,this.deps)):this.enabled&&this.pick.size>0&&q(this.pick,this.deps)}}var W={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},$=new Map;class K extends H{static useCached(e){let t=$.get(e);return void 0!==t||(t=new this(e),$.set(e,t)),t}constructor(e){super(),!0===e?this.setupFromTrue():void 0===e?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):"object"==typeof e?this.setupFromObject(e):u(`Invalid options argument ${e}`),void 0===this.firstChunkSize&&(this.firstChunkSize=i?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of z)this[e]=W[e];for(e of G)this[e]=W[e];for(e of R)this[e]=W[e];for(e of E)this[e]=new _(e,W[e],void 0,this)}setupFromTrue(){let e;for(e of z)this[e]=W[e];for(e of G)this[e]=W[e];for(e of R)this[e]=!0;for(e of E)this[e]=new _(e,!0,void 0,this)}setupFromArray(e){let t;for(t of z)this[t]=W[t];for(t of G)this[t]=W[t];for(t of R)this[t]=W[t];for(t of E)this[t]=new _(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,M)}setupFromObject(e){let t;for(t of(M.ifd0=M.ifd0||M.image,M.ifd1=M.ifd1||M.thumbnail,Object.assign(this,e),z))this[t]=Y(e[t],W[t]);for(t of G)this[t]=Y(e[t],W[t]);for(t of R)this[t]=Y(e[t],W[t]);for(t of V)this[t]=new _(t,W[t],e[t],this);for(t of M)this[t]=new _(t,W[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,M,E),!0===e.tiff?this.batchEnableWithBool(M,!0):!1===e.tiff?this.batchEnableWithUserValue(M,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,M):"object"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,M)}batchEnableWithBool(e,t){for(let i of e)this[i].enabled=t}batchEnableWithUserValue(e,t){for(let i of e){let e=t[i];this[i].enabled=!1!==e&&void 0!==e}}setupGlobalFilters(e,t,i,s=i){if(e&&e.length){for(let e of s)this[e].enabled=!1;let t=X(e,i);for(let[e,i]of t)q(this[e].pick,i),this[e].enabled=!0}else if(t&&t.length){let e=X(t,i);for(let[t,i]of e)q(this[t].skip,i)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:i,iptc:s,icc:n}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),i.enabled||e.skip.add(700),s.enabled||e.skip.add(33723),n.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:i,interop:s}=this;s.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),i.needed&&e.deps.add(34853),this.tiff.enabled=M.some((e=>!0===this[e].enabled))||this.makerNote||this.userComment;for(let e of M)this[e].finalizeFilters()}get onlyTiff(){return!N.map((e=>this[e].enabled)).some((e=>!0===e))&&this.tiff.enabled}checkLoadedPlugins(){for(let e of V)this[e].enabled&&!k.has(e)&&b("segment parser",e)}}function X(e,t){let i,s,n,r,a=[];for(n of t){for(r of(i=L.get(n),s=[],i))(e.includes(r[0])||e.includes(r[1]))&&s.push(r[0]);s.length&&a.push([n,s])}return a}function Y(e,t){return void 0!==e?e:void 0!==t?t:void 0}function q(e,t){for(let i of t)e.add(i)}e(K,"default",W);class J{constructor(t){e(this,"parsers",{}),e(this,"output",{}),e(this,"errors",[]),e(this,"pushToErrors",(e=>this.errors.push(e))),this.options=K.useCached(t)}async read(e){this.file=await function(e,t){return"string"==typeof e?x(e,t):i&&!s&&e instanceof HTMLImageElement?x(e.src,t):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new y(e):i&&e instanceof Blob?C(e,t,"blob",U):void u("Invalid input argument")}(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[i,s]of S)if(s.canHandle(e,t))return this.fileParser=new s(this.options,this.file,this.parsers),e[i]=!0;this.file.close&&this.file.close(),u("Unknown file format")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),h(e)}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map((async t=>{let i=await t.parse();t.assignToOutput(e,i)}));this.options.silentErrors&&(t=t.map((e=>e.catch(this.pushToErrors)))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,i=k.get("tiff",e);var s;if(t.tiff?s={start:0,type:"tiff"}:t.jpeg&&(s=await this.fileParser.getOrFindSegment("tiff")),void 0===s)return;let n=await this.fileParser.ensureSegmentChunk(s),r=this.parsers.tiff=new i(n,e,t),a=await r.extractThumbnail();return t.close&&t.close(),a}}async function Z(e,t){let i=new J(t);return await i.read(e),i.parse()}var Q=Object.freeze({__proto__:null,parse:Z,Exifr:J,fileParsers:S,segmentParsers:k,fileReaders:v,tagKeys:L,tagValues:D,tagRevivers:T,createDictionary:B,extendDictionary:F,fetchUrlAsArrayBuffer:A,readBlobAsArrayBuffer:U,chunkedProps:z,otherSegments:N,segments:V,tiffBlocks:M,segmentsAndBlocks:E,tiffExtractables:R,inheritables:j,allFormatters:G,Options:K});class ee{constructor(t,i,s){e(this,"errors",[]),e(this,"ensureSegmentChunk",(async e=>{let t=e.start,i=e.size||65536;if(this.file.chunked)if(this.file.available(t,i))e.chunk=this.file.subarray(t,i);else try{e.chunk=await this.file.readChunk(t,i)}catch(t){u(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`)}else this.file.byteLength>t+i?e.chunk=this.file.subarray(t,i):void 0===e.size?e.chunk=this.file.subarray(t):u("Segment unreachable: "+JSON.stringify(e));return e.chunk})),this.extendOptions&&this.extendOptions(t),this.options=t,this.file=i,this.parsers=s}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let i=new(k.get(e))(t,this.options,this.file);return this.parsers[e]=i}createParsers(e){for(let t of e){let{type:e,chunk:i}=t,s=this.options[e];if(s&&s.enabled){let t=this.parsers[e];t&&t.append||t||this.createParser(e,i)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}class te{static findPosition(e,t){let i=e.getUint16(t+2)+2,s="function"==typeof this.headerLength?this.headerLength(e,t,i):this.headerLength,n=t+s,r=i-s;return{offset:t,length:i,headerLength:s,start:n,size:r,end:n+r}}static parse(e,t={}){return new this(e,new K({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof y?e:new y(e)}constructor(t,i={},s){e(this,"errors",[]),e(this,"raw",new Map),e(this,"handleError",(e=>{if(!this.options.silentErrors)throw e;this.errors.push(e.message)})),this.chunk=this.normalizeInput(t),this.file=s,this.type=this.constructor.type,this.globalOptions=this.options=i,this.localOptions=i[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let i=T.get(t),s=D.get(t),n=L.get(t),r=this.options[t],a=r.reviveValues&&!!i,o=r.translateValues&&!!s,h=r.translateKeys&&!!n,l={};for(let[t,r]of e)a&&i.has(t)?r=i.get(t)(r):o&&s.has(t)&&(r=this.translateValue(r,s.get(t))),h&&n.has(t)&&(t=n.get(t)||t),l[t]=r;return l}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,i){if(this.globalOptions.mergeOutput)return Object.assign(e,i);e[t]?Object.assign(e[t],i):e[t]=i}}e(te,"headerLength",4),e(te,"type",void 0),e(te,"multiSegment",!1),e(te,"canHandle",(()=>!1));function ie(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function se(e){return e>=224&&e<=239}function ne(e,t,i){for(let[s,n]of k)if(n.canHandle(e,t,i))return s}class re extends ee{constructor(...t){super(...t),e(this,"appSegments",[]),e(this,"jpegSegments",[]),e(this,"unknownSegments",[])}static canHandle(e,t){return 65496===t}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){!0===e?(this.findAll=!0,this.wanted=new Set(k.keyList())):(e=void 0===e?k.keyList().filter((e=>this.options[e].enabled)):e.filter((e=>this.options[e].enabled&&k.has(e))),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:i,findAll:s,wanted:n,remaining:r}=this;if(!s&&this.file.chunked&&(s=Array.from(n).some((e=>{let t=k.get(e),i=this.options[e];return t.multiSegment&&i.multiSegment})),s&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,i.byteLength),!this.options.onlyTiff&&i.chunked){let t=!1;for(;r.size>0&&!t&&(i.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:s}=i,n=this.appSegments.some((e=>!this.file.available(e.offset||e.start,e.length||e.size)));if(t=e>s&&!n?!await i.readNextChunk(e):!await i.readNextChunk(s),void 0===(e=this.findAppSegmentsInRange(e,i.byteLength)))return}}}findAppSegmentsInRange(e,t){t-=2;let i,s,n,r,a,o,{file:h,findAll:l,wanted:f,remaining:u,options:d}=this;for(;e<t;e++)if(255===h.getUint8(e))if(i=h.getUint8(e+1),se(i)){if(s=h.getUint16(e+2),n=ne(h,e,s),n&&f.has(n)&&(r=k.get(n),a=r.findPosition(h,e),o=d[n],a.type=n,this.appSegments.push(a),!l&&(r.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=a.chunkNumber<a.chunkCount,this.unfinishedMultiSegment||u.delete(n)):u.delete(n),0===u.size)))break;d.recordUnknownSegments&&(a=te.findPosition(h,e),a.marker=i,this.unknownSegments.push(a)),e+=s+1}else if(ie(i)){if(s=h.getUint16(e+2),218===i&&!1!==d.stopAfterSos)return;d.recordJpegSegments&&this.jpegSegments.push({offset:e,length:s,marker:i}),e+=s+1}return e}mergeMultiSegments(){if(!this.appSegments.some((e=>e.multiSegment)))return;let e=function(e,t){let i,s,n,r=new Map;for(let a=0;a<e.length;a++)i=e[a],s=i[t],r.has(s)?n=r.get(s):r.set(s,n=[]),n.push(i);return Array.from(r)}(this.appSegments,"type");this.mergedAppSegments=e.map((([e,t])=>{let i=k.get(e,this.options);if(i.handleMultiSegments){return{type:e,chunk:i.handleMultiSegments(t)}}return t[0]}))}getSegment(e){return this.appSegments.find((t=>t.type===e))}async getOrFindSegment(e){let t=this.getSegment(e);return void 0===t&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}e(re,"type","jpeg"),S.set("jpeg",re);const ae=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class oe extends te{parseHeader(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,i=new Map){let{pick:s,skip:n}=this.options[t];s=new Set(s);let r=s.size>0,a=0===n.size,o=this.chunk.getUint16(e);e+=2;for(let h=0;h<o;h++){let o=this.chunk.getUint16(e);if(r){if(s.has(o)&&(i.set(o,this.parseTag(e,o,t)),s.delete(o),0===s.size))break}else!a&&n.has(o)||i.set(o,this.parseTag(e,o,t));e+=12}return i}parseTag(e,t,i){let{chunk:s}=this,n=s.getUint16(e+2),r=s.getUint32(e+4),a=ae[n];if(a*r<=4?e+=8:e=s.getUint32(e+8),(n<1||n>13)&&u(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${n}, offset ${e}`),e>s.byteLength&&u(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${n}, offset ${e} is outside of chunk size ${s.byteLength}`),1===n)return s.getUint8Array(e,r);if(2===n)return d(s.getString(e,r));if(7===n)return s.getUint8Array(e,r);if(1===r)return this.parseTagValue(n,e);{let t=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(n))(r),i=a;for(let s=0;s<r;s++)t[s]=this.parseTagValue(n,e),e+=i;return t}}parseTagValue(e,t){let{chunk:i}=this;switch(e){case 1:return i.getUint8(t);case 3:return i.getUint16(t);case 4:return i.getUint32(t);case 5:return i.getUint32(t)/i.getUint32(t+4);case 6:return i.getInt8(t);case 8:return i.getInt16(t);case 9:return i.getInt32(t);case 10:return i.getInt32(t)/i.getInt32(t+4);case 11:return i.getFloat(t);case 12:return i.getDouble(t);case 13:return i.getUint32(t);default:u(`Invalid tiff type ${e}`)}}}class he extends oe{static canHandle(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse("parseExifBlock"),e.gps.enabled&&await this.safeParse("parseGpsBlock"),e.interop.enabled&&await this.safeParse("parseInteropBlock"),e.ifd1.enabled&&await this.safeParse("parseThumbnailBlock"),this.createOutput()}safeParse(e){let t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}findIfd0Offset(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(void 0===this.ifd1Offset){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let i=new Map;return this[t]=i,this.parseTags(e,t,i),i}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&u("Malformed EXIF data"),!e.chunked&&this.ifd0Offset>e.byteLength&&u(`IFD0 offset points to outside of file.\nthis.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,c(this.options));let t=this.parseBlock(this.ifd0Offset,"ifd0");return 0!==t.size?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.exifOffset)return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,c(this.options));let e=this.parseBlock(this.exifOffset,"exif");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let i=e.get(t);i&&1===i.length&&e.set(t,i[0])}async parseGpsBlock(){if(this.gps)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.gpsOffset)return;let e=this.parseBlock(this.gpsOffset,"gps");return e&&e.has(2)&&e.has(4)&&(e.set("latitude",le(...e.get(2),e.get(1))),e.set("longitude",le(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),void 0!==this.interopOffset||this.exif||await this.parseExifBlock(),void 0!==this.interopOffset))return this.parseBlock(this.interopOffset,"interop")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,"ifd1"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),void 0===this.ifd1)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,i,s={};for(t of M)if(e=this[t],!f(e))if(i=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if("ifd1"===t)continue;Object.assign(s,i)}else s[t]=i;return this.makerNote&&(s.makerNote=this.makerNote),this.userComment&&(s.userComment=this.userComment),s}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[i,s]of Object.entries(t))this.assignObjectToOutput(e,i,s)}}function le(e,t,i,s){var n=e+t/60+i/3600;return"S"!==s&&"W"!==s||(n*=-1),n}e(he,"type","tiff"),e(he,"headerLength",10),k.set("tiff",he);var fe=Object.freeze({__proto__:null,default:Q,Exifr:J,fileParsers:S,segmentParsers:k,fileReaders:v,tagKeys:L,tagValues:D,tagRevivers:T,createDictionary:B,extendDictionary:F,fetchUrlAsArrayBuffer:A,readBlobAsArrayBuffer:U,chunkedProps:z,otherSegments:N,segments:V,tiffBlocks:M,segmentsAndBlocks:E,tiffExtractables:R,inheritables:j,allFormatters:G,Options:K,parse:Z});const ue={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1},de=Object.assign({},ue,{firstChunkSize:4e4,gps:[1,2,3,4]});async function ce(e){let t=new J(de);await t.read(e);let i=await t.parse();if(i&&i.gps){let{latitude:e,longitude:t}=i.gps;return{latitude:e,longitude:t}}}const ge=Object.assign({},ue,{tiff:!1,ifd1:!0,mergeOutput:!1});async function pe(e){let t=new J(ge);await t.read(e);let i=await t.extractThumbnail();return i&&o?r.from(i):i}async function me(e){let t=await this.thumbnail(e);if(void 0!==t){let e=new Blob([t]);return URL.createObjectURL(e)}}const ye=Object.assign({},ue,{firstChunkSize:4e4,ifd0:[274]});async function be(e){let t=new J(ye);await t.read(e);let i=await t.parse();if(i&&i.ifd0)return i.ifd0[274]}const we=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let Se=!0,ke=!0;if("object"==typeof navigator){let e=navigator.userAgent;if(e.includes("iPad")||e.includes("iPhone")){let t=e.match(/OS (\d+)_(\d+)/);if(t){let[,e,i]=t,s=Number(e)+.1*Number(i);Se=s<13.4,ke=!1}}else if(e.includes("OS X 10")){let[,t]=e.match(/OS X 10[_.](\d+)/);Se=ke=Number(t)<15}if(e.includes("Chrome/")){let[,t]=e.match(/Chrome\/(\d+)/);Se=ke=Number(t)<81}else if(e.includes("Firefox/")){let[,t]=e.match(/Firefox\/(\d+)/);Se=ke=Number(t)<77}}async function ve(e){let t=await be(e);return Object.assign({canvas:Se,css:ke},we[t])}class Oe extends y{constructor(...t){super(...t),e(this,"ranges",new xe),0!==this.byteLength&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,i){if(0===e&&0===this.byteLength&&i){let e=new DataView(i.buffer||i,i.byteOffset,i.byteLength);this._swapDataView(e)}else{let i=e+t;if(i>this.byteLength){let{dataView:e}=this._extend(i);this._swapDataView(e)}}}_extend(e){let t;t=o?r.allocUnsafe(e):new Uint8Array(e);let i=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:i}}subarray(e,t,i=!1){return t=t||this._lengthToEnd(e),i&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,i=!1){i&&this._tryExtend(t,e.byteLength,e);let s=super.set(e,t);return this.ranges.add(t,s.byteLength),s}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class xe{constructor(){e(this,"list",[])}get length(){return this.list.length}add(e,t,i=0){let s=e+t,n=this.list.filter((t=>Ce(e,t.offset,s)||Ce(e,t.end,s)));if(n.length>0){e=Math.min(e,...n.map((e=>e.offset))),s=Math.max(s,...n.map((e=>e.end))),t=s-e;let i=n.shift();i.offset=e,i.length=t,i.end=s,this.list=this.list.filter((e=>!n.includes(e)))}else this.list.push({offset:e,length:t,end:s})}available(e,t){let i=e+t;return this.list.some((t=>t.offset<=e&&i<=t.end))}}function Ce(e,t,i){return e<=t&&t<=i}class Pe extends Oe{constructor(t,i){super(0),e(this,"chunksRead",0),this.input=t,this.options=i}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,i=await this.readChunk(e,t);return!!i&&i.byteLength===t}async readChunk(e,t){if(this.chunksRead++,0!==(t=this.safeWrapAddress(e,t)))return this._readChunk(e,t)}safeWrapAddress(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return void 0!==this.size&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}v.set("blob",class extends Pe{async readWhole(){this.chunked=!1;let e=await U(this.input);this._swapArrayBuffer(e)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(e,t){let i=t?e+t:void 0,s=this.input.slice(e,i),n=await U(s);return this.set(n,e,!0)}});var Ae=Object.freeze({__proto__:null,default:fe,Exifr:J,fileParsers:S,segmentParsers:k,fileReaders:v,tagKeys:L,tagValues:D,tagRevivers:T,createDictionary:B,extendDictionary:F,fetchUrlAsArrayBuffer:A,readBlobAsArrayBuffer:U,chunkedProps:z,otherSegments:N,segments:V,tiffBlocks:M,segmentsAndBlocks:E,tiffExtractables:R,inheritables:j,allFormatters:G,Options:K,parse:Z,gpsOnlyOptions:de,gps:ce,thumbnailOnlyOptions:ge,thumbnail:pe,thumbnailUrl:me,orientationOnlyOptions:ye,orientation:be,rotations:we,get rotateCanvas(){return Se},get rotateCss(){return ke},rotation:ve});v.set("url",class extends Pe{async readWhole(){this.chunked=!1;let e=await A(this.input);e instanceof ArrayBuffer?this._swapArrayBuffer(e):e instanceof Uint8Array&&this._swapBuffer(e)}async _readChunk(e,t){let i=t?e+t-1:void 0,s=this.options.httpHeaders||{};(e||i)&&(s.range=`bytes=${[e,i].join("-")}`);let n=await O(this.input,{headers:s}),r=await n.arrayBuffer(),a=r.byteLength;if(416!==n.status)return a!==t&&(this.size=e+a),this.set(r,e,!0)}});y.prototype.getUint64=function(e){let t=this.getUint32(e),i=this.getUint32(e+4);return t<1048575?t<<32|i:void 0!==typeof a?(console.warn("Using BigInt because of type 64uint but JS can only handle 53b numbers."),a(t)<<a(32)|a(i)):void u("Trying to read 64b value but JS can only handle 53b numbers.")};class Ue extends ee{parseBoxes(e=0){let t=[];for(;e<this.file.byteLength-4;){let i=this.parseBoxHead(e);if(t.push(i),0===i.length)break;e+=i.length}return t}parseSubBoxes(e){e.boxes=this.parseBoxes(e.start)}findBox(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((e=>e.kind===t))}parseBoxHead(e){let t=this.file.getUint32(e),i=this.file.getString(e+4,4),s=e+8;return 1===t&&(t=this.file.getUint64(e+8),s+=8),{offset:e,length:t,kind:i,start:s}}parseBoxFullHead(e){if(void 0!==e.version)return;let t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}class Ie extends Ue{static canHandle(e,t){if(0!==t)return!1;let i=e.getUint16(2);if(i>50)return!1;let s=16,n=[];for(;s<i;)n.push(e.getString(s,4)),s+=4;return n.includes(this.type)}async parse(){let e=this.file.getUint32(0),t=this.parseBoxHead(e);for(;"meta"!==t.kind;)e+=t.length,await this.file.ensureChunk(e,16),t=this.parseBoxHead(e);await this.file.ensureChunk(t.offset,t.length),this.parseBoxFullHead(t),this.parseSubBoxes(t),this.options.icc.enabled&&await this.findIcc(t),this.options.tiff.enabled&&await this.findExif(t)}async registerSegment(e,t,i){await this.file.ensureChunk(t,i);let s=this.file.subarray(t,i);this.createParser(e,s)}async findIcc(e){let t=this.findBox(e,"iprp");if(void 0===t)return;let i=this.findBox(t,"ipco");if(void 0===i)return;let s=this.findBox(i,"colr");void 0!==s&&await this.registerSegment("icc",s.offset+12,s.length)}async findExif(e){let t=this.findBox(e,"iinf");if(void 0===t)return;let i=this.findBox(e,"iloc");if(void 0===i)return;let s=this.findExifLocIdInIinf(t),n=this.findExtentInIloc(i,s);if(void 0===n)return;let[r,a]=n;await this.file.ensureChunk(r,a);let o=4+this.file.getUint32(r);r+=o,a-=o,await this.registerSegment("tiff",r,a)}findExifLocIdInIinf(e){this.parseBoxFullHead(e);let t,i,s,n,r=e.start,a=this.file.getUint16(r);for(r+=2;a--;){if(t=this.parseBoxHead(r),this.parseBoxFullHead(t),i=t.start,t.version>=2&&(s=3===t.version?4:2,n=this.file.getString(i+s+2,4),"Exif"===n))return this.file.getUintBytes(i,s);r+=t.length}}get8bits(e){let t=this.file.getUint8(e);return[t>>4,15&t]}findExtentInIloc(e,t){this.parseBoxFullHead(e);let i=e.start,[s,n]=this.get8bits(i++),[r,a]=this.get8bits(i++),o=2===e.version?4:2,h=1===e.version||2===e.version?2:0,l=a+s+n,f=2===e.version?4:2,u=this.file.getUintBytes(i,f);for(i+=f;u--;){let e=this.file.getUintBytes(i,o);i+=o+h+2+r;let f=this.file.getUint16(i);if(i+=2,e===t)return f>1&&console.warn("ILOC box has more than one extent but we're only processing one\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file"),[this.file.getUintBytes(i+a,s),this.file.getUintBytes(i+a+s,n)];i+=f*l}}}class Be extends Ie{}e(Be,"type","heic");class Fe extends Ie{}e(Fe,"type","avif"),S.set("heic",Be),S.set("avif",Fe),B(L,["ifd0","ifd1"],[[256,"ImageWidth"],[257,"ImageHeight"],[258,"BitsPerSample"],[259,"Compression"],[262,"PhotometricInterpretation"],[270,"ImageDescription"],[271,"Make"],[272,"Model"],[273,"StripOffsets"],[274,"Orientation"],[277,"SamplesPerPixel"],[278,"RowsPerStrip"],[279,"StripByteCounts"],[282,"XResolution"],[283,"YResolution"],[284,"PlanarConfiguration"],[296,"ResolutionUnit"],[301,"TransferFunction"],[305,"Software"],[306,"ModifyDate"],[315,"Artist"],[316,"HostComputer"],[317,"Predictor"],[318,"WhitePoint"],[319,"PrimaryChromaticities"],[513,"ThumbnailOffset"],[514,"ThumbnailLength"],[529,"YCbCrCoefficients"],[530,"YCbCrSubSampling"],[531,"YCbCrPositioning"],[532,"ReferenceBlackWhite"],[700,"ApplicationNotes"],[33432,"Copyright"],[33723,"IPTC"],[34665,"ExifIFD"],[34675,"ICC"],[34853,"GpsIFD"],[330,"SubIFD"],[40965,"InteropIFD"],[40091,"XPTitle"],[40092,"XPComment"],[40093,"XPAuthor"],[40094,"XPKeywords"],[40095,"XPSubject"]]),B(L,"exif",[[33434,"ExposureTime"],[33437,"FNumber"],[34850,"ExposureProgram"],[34852,"SpectralSensitivity"],[34855,"ISO"],[34858,"TimeZoneOffset"],[34859,"SelfTimerMode"],[34864,"SensitivityType"],[34865,"StandardOutputSensitivity"],[34866,"RecommendedExposureIndex"],[34867,"ISOSpeed"],[34868,"ISOSpeedLatitudeyyy"],[34869,"ISOSpeedLatitudezzz"],[36864,"ExifVersion"],[36867,"DateTimeOriginal"],[36868,"CreateDate"],[36873,"GooglePlusUploadCode"],[36880,"OffsetTime"],[36881,"OffsetTimeOriginal"],[36882,"OffsetTimeDigitized"],[37121,"ComponentsConfiguration"],[37122,"CompressedBitsPerPixel"],[37377,"ShutterSpeedValue"],[37378,"ApertureValue"],[37379,"BrightnessValue"],[37380,"ExposureCompensation"],[37381,"MaxApertureValue"],[37382,"SubjectDistance"],[37383,"MeteringMode"],[37384,"LightSource"],[37385,"Flash"],[37386,"FocalLength"],[37393,"ImageNumber"],[37394,"SecurityClassification"],[37395,"ImageHistory"],[37396,"SubjectArea"],[37500,"MakerNote"],[37510,"UserComment"],[37520,"SubSecTime"],[37521,"SubSecTimeOriginal"],[37522,"SubSecTimeDigitized"],[37888,"AmbientTemperature"],[37889,"Humidity"],[37890,"Pressure"],[37891,"WaterDepth"],[37892,"Acceleration"],[37893,"CameraElevationAngle"],[40960,"FlashpixVersion"],[40961,"ColorSpace"],[40962,"ExifImageWidth"],[40963,"ExifImageHeight"],[40964,"RelatedSoundFile"],[41483,"FlashEnergy"],[41486,"FocalPlaneXResolution"],[41487,"FocalPlaneYResolution"],[41488,"FocalPlaneResolutionUnit"],[41492,"SubjectLocation"],[41493,"ExposureIndex"],[41495,"SensingMethod"],[41728,"FileSource"],[41729,"SceneType"],[41730,"CFAPattern"],[41985,"CustomRendered"],[41986,"ExposureMode"],[41987,"WhiteBalance"],[41988,"DigitalZoomRatio"],[41989,"FocalLengthIn35mmFormat"],[41990,"SceneCaptureType"],[41991,"GainControl"],[41992,"Contrast"],[41993,"Saturation"],[41994,"Sharpness"],[41996,"SubjectDistanceRange"],[42016,"ImageUniqueID"],[42032,"OwnerName"],[42033,"SerialNumber"],[42034,"LensInfo"],[42035,"LensMake"],[42036,"LensModel"],[42037,"LensSerialNumber"],[42080,"CompositeImage"],[42081,"CompositeImageCount"],[42082,"CompositeImageExposureTimes"],[42240,"Gamma"],[59932,"Padding"],[59933,"OffsetSchema"],[65e3,"OwnerName"],[65001,"SerialNumber"],[65002,"Lens"],[65100,"RawFile"],[65101,"Converter"],[65102,"WhiteBalance"],[65105,"Exposure"],[65106,"Shadows"],[65107,"Brightness"],[65108,"Contrast"],[65109,"Saturation"],[65110,"Sharpness"],[65111,"Smoothness"],[65112,"MoireFilter"],[40965,"InteropIFD"]]),B(L,"gps",[[0,"GPSVersionID"],[1,"GPSLatitudeRef"],[2,"GPSLatitude"],[3,"GPSLongitudeRef"],[4,"GPSLongitude"],[5,"GPSAltitudeRef"],[6,"GPSAltitude"],[7,"GPSTimeStamp"],[8,"GPSSatellites"],[9,"GPSStatus"],[10,"GPSMeasureMode"],[11,"GPSDOP"],[12,"GPSSpeedRef"],[13,"GPSSpeed"],[14,"GPSTrackRef"],[15,"GPSTrack"],[16,"GPSImgDirectionRef"],[17,"GPSImgDirection"],[18,"GPSMapDatum"],[19,"GPSDestLatitudeRef"],[20,"GPSDestLatitude"],[21,"GPSDestLongitudeRef"],[22,"GPSDestLongitude"],[23,"GPSDestBearingRef"],[24,"GPSDestBearing"],[25,"GPSDestDistanceRef"],[26,"GPSDestDistance"],[27,"GPSProcessingMethod"],[28,"GPSAreaInformation"],[29,"GPSDateStamp"],[30,"GPSDifferential"],[31,"GPSHPositioningError"]]),B(D,["ifd0","ifd1"],[[274,{1:"Horizontal (normal)",2:"Mirror horizontal",3:"Rotate 180",4:"Mirror vertical",5:"Mirror horizontal and rotate 270 CW",6:"Rotate 90 CW",7:"Mirror horizontal and rotate 90 CW",8:"Rotate 270 CW"}],[296,{1:"None",2:"inches",3:"cm"}]]);let Le=B(D,"exif",[[34850,{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"}],[37121,{0:"-",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}],[37383,{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"}],[37384,{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"}],[37385,{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"}],[41495,{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"}],[41728,{1:"Film Scanner",2:"Reflection Print Scanner",3:"Digital Camera"}],[41729,{1:"Directly photographed"}],[41985,{0:"Normal",1:"Custom",2:"HDR (no original saved)",3:"HDR (original saved)",4:"Original (for HDR)",6:"Panorama",7:"Portrait HDR",8:"Portrait"}],[41986,{0:"Auto",1:"Manual",2:"Auto bracket"}],[41987,{0:"Auto",1:"Manual"}],[41990,{0:"Standard",1:"Landscape",2:"Portrait",3:"Night",4:"Other"}],[41991,{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"}],[41996,{0:"Unknown",1:"Macro",2:"Close",3:"Distant"}],[42080,{0:"Unknown",1:"Not a Composite Image",2:"General Composite Image",3:"Composite Image Captured While Shooting"}]]);const De={1:"No absolute unit of measurement",2:"Inch",3:"Centimeter"};Le.set(37392,De),Le.set(41488,De);const Te={0:"Normal",1:"Low",2:"High"};function ze(e){return"object"==typeof e&&void 0!==e.length?e[0]:e}function Ne(e){let t=Array.from(e).slice(1);return t[1]>15&&(t=t.map((e=>String.fromCharCode(e)))),"0"!==t[2]&&0!==t[2]||t.pop(),t.join(".")}function Ve(e){if("string"==typeof e){var[t,i,s,n,r,a]=e.trim().split(/[-: ]/g).map(Number),o=new Date(t,i-1,s);return Number.isNaN(n)||Number.isNaN(r)||Number.isNaN(a)||(o.setHours(n),o.setMinutes(r),o.setSeconds(a)),Number.isNaN(+o)?e:o}}function Me(e){if("string"==typeof e)return e;let t=[];if(0===e[1]&&0===e[e.length-1])for(let i=0;i<e.length;i+=2)t.push(Ee(e[i+1],e[i]));else for(let i=0;i<e.length;i+=2)t.push(Ee(e[i],e[i+1]));return d(String.fromCodePoint(...t))}function Ee(e,t){return e<<8|t}Le.set(41992,Te),Le.set(41993,Te),Le.set(41994,Te),B(T,["ifd0","ifd1"],[[50827,function(e){return"string"!=typeof e?m(e):e}],[306,Ve],[40091,Me],[40092,Me],[40093,Me],[40094,Me],[40095,Me]]),B(T,"exif",[[40960,Ne],[36864,Ne],[36867,Ve],[36868,Ve],[40962,ze],[40963,ze]]),B(T,"gps",[[0,e=>Array.from(e).join(".")],[7,e=>Array.from(e).join(":")]]);class Re extends te{static canHandle(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&"http://ns.adobe.com/"===e.getString(t+4,"http://ns.adobe.com/".length)}static headerLength(e,t){return"http://ns.adobe.com/xmp/extension/"===e.getString(t+4,"http://ns.adobe.com/xmp/extension/".length)?79:4+"http://ns.adobe.com/xap/1.0/".length+1}static findPosition(e,t){let i=super.findPosition(e,t);return i.multiSegment=i.extended=79===i.headerLength,i.multiSegment?(i.chunkCount=e.getUint8(t+72),i.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&i.chunkNumber++):(i.chunkCount=1/0,i.chunkNumber=-1),i}static handleMultiSegments(e){return e.map((e=>e.chunk.getString())).join("")}normalizeInput(e){return"string"==typeof e?e:y.from(e).getString()}parse(e=this.chunk){if(!this.localOptions.parse)return e;e=function(e){let t={},i={};for(let e of Ye)t[e]=[],i[e]=0;return e.replace(qe,((e,s,n)=>{if("<"===s){let s=++i[n];return t[n].push(s),`${e}#${s}`}return`${e}#${t[n].pop()}`}))}(e);let t=Ge.findAll(e,"rdf","Description");0===t.length&&t.push(new Ge("rdf","Description",void 0,e));let i,s={};for(let e of t)for(let t of e.properties)i=$e(t.ns,s),He(t,i);return function(e){let t;for(let i in e)t=e[i]=h(e[i]),void 0===t&&delete e[i];return h(e)}(s)}assignToOutput(e,t){if(this.localOptions.parse)for(let[i,s]of Object.entries(t))switch(i){case"tiff":this.assignObjectToOutput(e,"ifd0",s);break;case"exif":this.assignObjectToOutput(e,"exif",s);break;case"xmlns":break;default:this.assignObjectToOutput(e,i,s)}else e.xmp=t}}e(Re,"type","xmp"),e(Re,"multiSegment",!0),k.set("xmp",Re);class je{static findAll(e){return Ke(e,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=("[^"]*"|'[^']*')/gm).map(je.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],s=e[3].slice(1,-1);return s=Xe(s),new je(t,i,s)}constructor(e,t,i){this.ns=e,this.name=t,this.value=i}serialize(){return this.value}}class Ge{static findAll(e,t,i){if(void 0!==t||void 0!==i){t=t||"[\\w\\d-]+",i=i||"[\\w\\d-]+";var s=new RegExp(`<(${t}):(${i})(#\\d+)?((\\s+?[\\w\\d-:]+=("[^"]*"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)`,"gm")}else s=/<([\w\d-]+):([\w\d-]+)(#\d+)?((\s+?[\w\d-:]+=("[^"]*"|'[^']*'))*\s*)(\/>|>([\s\S]*?)<\/\1:\2\3>)/gm;return Ke(e,s).map(Ge.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],s=e[4],n=e[8];return new Ge(t,i,s,n)}constructor(e,t,i,s){this.ns=e,this.name=t,this.attrString=i,this.innerXml=s,this.attrs=je.findAll(i),this.children=Ge.findAll(s),this.value=0===this.children.length?Xe(s):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}get isListContainer(){return 1===this.children.length&&this.children[0].isList}get isList(){let{ns:e,name:t}=this;return"rdf"===e&&("Seq"===t||"Bag"===t||"Alt"===t)}get isListItem(){return"rdf"===this.ns&&"li"===this.name}serialize(){if(0===this.properties.length&&void 0===this.value)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return We(this.children.map(_e));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();let e={};for(let t of this.properties)He(t,e);return void 0!==this.value&&(e.value=this.value),h(e)}}function He(e,t){let i=e.serialize();void 0!==i&&(t[e.name]=i)}var _e=e=>e.serialize(),We=e=>1===e.length?e[0]:e,$e=(e,t)=>t[e]?t[e]:t[e]={};function Ke(e,t){let i,s=[];if(!e)return s;for(;null!==(i=t.exec(e));)s.push(i);return s}function Xe(e){if(function(e){return null==e||"null"===e||"undefined"===e||""===e||""===e.trim()}(e))return;let t=Number(e);if(!Number.isNaN(t))return t;let i=e.toLowerCase();return"true"===i||"false"!==i&&e.trim()}const Ye=["rdf:li","rdf:Seq","rdf:Bag","rdf:Alt","rdf:Description"],qe=new RegExp(`(<|\\/)(${Ye.join("|")})`,"g");export default Ae;export{J as Exifr,K as Options,G as allFormatters,z as chunkedProps,B as createDictionary,F as extendDictionary,A as fetchUrlAsArrayBuffer,S as fileParsers,v as fileReaders,ce as gps,de as gpsOnlyOptions,j as inheritables,be as orientation,ye as orientationOnlyOptions,N as otherSegments,Z as parse,U as readBlobAsArrayBuffer,Se as rotateCanvas,ke as rotateCss,ve as rotation,we as rotations,k as segmentParsers,V as segments,E as segmentsAndBlocks,L as tagKeys,T as tagRevivers,D as tagValues,pe as thumbnail,ge as thumbnailOnlyOptions,me as thumbnailUrl,M as tiffBlocks,R as tiffExtractables};
