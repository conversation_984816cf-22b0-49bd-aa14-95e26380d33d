!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define("exifr",["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).exifr={})}(this,(function(e){"use strict";function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}});var n=["prototype","__proto__","caller","arguments","length","name"];Object.getOwnPropertyNames(t).forEach((function(r){-1===n.indexOf(r)&&e[r]!==t[r]&&(e[r]=t[r])})),t&&o(e,t)}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function f(e,t,n){return(f=u()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&o(i,n.prototype),i}).apply(null,arguments)}function c(e){var t="function"==typeof Map?new Map:void 0;return(c=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return f(e,arguments,s(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),o(r,e)})(e)}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?l(e):t}function d(e){var t=u();return function(){var n,r=s(e);if(t){var i=s(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h(this,n)}}function v(e,t,n){return(v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=s(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}var p="undefined"!=typeof self?self:global,g=Object.values||function(e){var t=[];for(var n in e)t.push(e[n]);return t},y=Object.entries||function(e){var t=[];for(var n in e)t.push([n,e[n]]);return t},m=Object.assign||function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach((function(t){for(var n in t)e[n]=t[n]})),e},k=Object.fromEntries||function(e){var t={};return b(e).forEach((function(e){var n=e[0],r=e[1];t[n]=r})),t},b=Array.from||function(e){if(e instanceof w){var t=[];return e.forEach((function(e,n){return t.push([n,e])})),t}return Array.prototype.slice.call(e)};function S(e){return-1!==this.indexOf(e)}Array.prototype.includes||(Array.prototype.includes=S),String.prototype.includes||(String.prototype.includes=S),String.prototype.startsWith||(String.prototype.startsWith=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.substring(t,t+e.length)===e}),String.prototype.endsWith||(String.prototype.endsWith=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.length;return this.substring(t-e.length,t)===e});var A=function(e){var t=[];if(Object.defineProperties(t,{size:{get:function(){return this.length}},has:{value:function(e){return-1!==this.indexOf(e)}},add:{value:function(e){this.has(e)||this.push(e)}},delete:{value:function(e){if(this.has(e)){var t=this.indexOf(e);this.splice(t,1)}}}}),Array.isArray(e))for(var n=0;n<e.length;n++)t.add(e[n]);return t},P=function(e){return new w(e)},w=void 0!==p.Map&&void 0!==p.Map.prototype.keys?p.Map:function(){function e(n){if(t(this,e),this.clear(),n)for(var r=0;r<n.length;r++)this.set(n[r][0],n[r][1])}return r(e,[{key:"clear",value:function(){this._map={},this._keys=[]}},{key:"size",get:function(){return this._keys.length}},{key:"get",value:function(e){return this._map["map_"+e]}},{key:"set",value:function(e,t){return this._map["map_"+e]=t,this._keys.indexOf(e)<0&&this._keys.push(e),this}},{key:"has",value:function(e){return this._keys.indexOf(e)>=0}},{key:"delete",value:function(e){var t=this._keys.indexOf(e);return!(t<0)&&(delete this._map["map_"+e],this._keys.splice(t,1),!0)}},{key:"keys",value:function(){return this._keys.slice(0)}},{key:"values",value:function(){var e=this;return this._keys.map((function(t){return e.get(t)}))}},{key:"entries",value:function(){var e=this;return this._keys.map((function(t){return[t,e.get(t)]}))}},{key:"forEach",value:function(e,t){for(var n=0;n<this._keys.length;n++)e.call(t,this._map["map_"+this._keys[n]],this._keys[n],this)}}]),e}(),O=p.fetch;p.fetch||(O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(n,r){var i=new XMLHttpRequest;if(i.open("get",e,!0),i.responseType="arraybuffer",i.onerror=r,t.headers)for(var a in t.headers)i.setRequestHeader(a,t.headers[a]);i.onload=function(){n({status:i.status,arrayBuffer:function(){return Promise.resolve(i.response)}})},i.send(null)}))});var C="undefined"!=typeof navigator,x=C&&"undefined"==typeof HTMLImageElement,B=!("undefined"==typeof global||"undefined"==typeof process||!process.versions||!process.versions.node),U=p.Buffer,I=p.BigInt,F=!!U,L=function(e){return T(e)?void 0:e},j=function(e){return void 0!==e};function T(e){return void 0===e||(e instanceof w?0===e.size:0===g(e).filter(j).length)}function D(e){var t=new Error(e);throw delete t.stack,t}function _(e){return""===(e=function(e){for(;e.endsWith("\0");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function z(e){var t=function(e){var t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}var E=function(e){return String.fromCharCode.apply(null,e)},R="undefined"!=typeof TextDecoder?new TextDecoder("utf-8"):void 0;function V(e){return R?R.decode(e):F?Buffer.from(e).toString("utf8"):decodeURIComponent(escape(E(e)))}var N=function(){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;if(t(this,e),"boolean"==typeof a&&(this.le=a),Array.isArray(n)&&(n=new Uint8Array(n)),0===n)this.byteOffset=0,this.byteLength=0;else if(n instanceof ArrayBuffer){void 0===i&&(i=n.byteLength-r);var s=new DataView(n,r,i);this._swapDataView(s)}else if(n instanceof Uint8Array||n instanceof DataView||n instanceof e){void 0===i&&(i=n.byteLength-r),(r+=n.byteOffset)+i>n.byteOffset+n.byteLength&&D("Creating view outside of available memory in ArrayBuffer");var o=new DataView(n.buffer,r,i);this._swapDataView(o)}else if("number"==typeof n){var u=new DataView(new ArrayBuffer(n));this._swapDataView(u)}else D("Invalid input argument for BufferView: "+n)}return r(e,[{key:"_swapArrayBuffer",value:function(e){this._swapDataView(new DataView(e))}},{key:"_swapBuffer",value:function(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}},{key:"_swapDataView",value:function(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}},{key:"_lengthToEnd",value:function(e){return this.byteLength-e}},{key:"set",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;t instanceof DataView||t instanceof e?t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t instanceof ArrayBuffer&&(t=new Uint8Array(t)),t instanceof Uint8Array||D("BufferView.set(): Invalid data argument.");var i=this.toUint8();return i.set(t,n),new r(this,n,t.byteLength)}},{key:"subarray",value:function(t,n){return new e(this,t,n=n||this._lengthToEnd(t))}},{key:"toUint8",value:function(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}},{key:"getUint8Array",value:function(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}},{key:"getString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=this.getUint8Array(e,t);return V(n)}},{key:"getLatin1String",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=this.getUint8Array(e,t);return E(n)}},{key:"getUnicodeString",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=[],r=0;r<t&&e+r<this.byteLength;r+=2)n.push(this.getUint16(e+r));return E(n)}},{key:"getInt8",value:function(e){return this.dataView.getInt8(e)}},{key:"getUint8",value:function(e){return this.dataView.getUint8(e)}},{key:"getInt16",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getInt16(e,t)}},{key:"getInt32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getInt32(e,t)}},{key:"getUint16",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getUint16(e,t)}},{key:"getUint32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getUint32(e,t)}},{key:"getFloat32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat32(e,t)}},{key:"getFloat64",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat64(e,t)}},{key:"getFloat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat32(e,t)}},{key:"getDouble",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat64(e,t)}},{key:"getUintBytes",value:function(e,t,n){switch(t){case 1:return this.getUint8(e,n);case 2:return this.getUint16(e,n);case 4:return this.getUint32(e,n);case 8:return this.getUint64&&this.getUint64(e,n)}}},{key:"getUint",value:function(e,t,n){switch(t){case 8:return this.getUint8(e,n);case 16:return this.getUint16(e,n);case 32:return this.getUint32(e,n);case 64:return this.getUint64&&this.getUint64(e,n)}}},{key:"toString",value:function(e){return this.dataView.toString(e,this.constructor.name)}},{key:"ensureChunk",value:function(){}}],[{key:"from",value:function(t,n){return t instanceof this&&t.le===n?t:new e(t,void 0,void 0,n)}}]),e}();function M(e,t){D("".concat(e," '").concat(t,"' was not loaded, try using full build of exifr."))}var G=function(e){a(i,e);var n=d(i);function i(e){var r;return t(this,i),(r=n.call(this)).kind=e,r}return r(i,[{key:"get",value:function(e,t){return this.has(e)||M(this.kind,e),t&&(e in t||function(e,t){D("Unknown ".concat(e," '").concat(t,"'."))}(this.kind,e),t[e].enabled||M(this.kind,e)),v(s(i.prototype),"get",this).call(this,e)}},{key:"keyList",value:function(){return b(this.keys())}}]),i}(c(w)),H=new G("file parser"),W=new G("segment parser"),K=new G("file reader");function X(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var Y=q((function(e,t){return X(t(e),(function(e){return new N(e)}))}));function q(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var J=q((function(e,t,n){var r=new(K.get(n))(e,t);return X(r.read(),(function(){return r}))})),Z=q((function(e,t,n,r){return K.has(n)?J(e,t,n):r?Y(e,r):(D("Parser ".concat(n," is not loaded")),X())})),Q="Invalid input argument";function $(e,t){return(n=e).startsWith("data:")||n.length>1e4?J(e,t,"base64"):B&&e.includes("://")?Z(e,t,"url",ee):B?J(e,t,"fs"):C?Z(e,t,"url",ee):void D(Q);var n}var ee=function(e){return O(e).then((function(e){return e.arrayBuffer()}))},te=function(e){return new Promise((function(t,n){var r=new FileReader;r.onloadend=function(){return t(r.result||new ArrayBuffer)},r.onerror=n,r.readAsArrayBuffer(e)}))};var ne=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"tagKeys",get:function(){return this.allKeys||(this.allKeys=b(this.keys())),this.allKeys}},{key:"tagValues",get:function(){return this.allValues||(this.allValues=b(this.values())),this.allValues}}]),i}(c(w));function re(e,t,n){var r=new ne,i=n;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++){var s=i[a],o=s[0],u=s[1];r.set(o,u)}if(Array.isArray(t)){var f=t;Array.isArray(f)||("function"==typeof f.entries&&(f=f.entries()),f=b(f));for(var c=0;c<f.length;c++){var l=f[c];e.set(l,r)}}else e.set(t,r);return r}function ie(e,t,n){var r,i=e.get(t),a=n;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=b(a));for(var s=0;s<a.length;s++)r=a[s],i.set(r[0],r[1])}var ae=P(),se=P(),oe=P(),ue=37500,fe=37510,ce=33723,le=34675,he=34665,de=34853,ve=40965,pe=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],ge=["jfif","xmp","icc","iptc","ihdr"],ye=["tiff"].concat(ge),me=["ifd0","ifd1","exif","gps","interop"],ke=[].concat(ye,me),be=["makerNote","userComment"],Se=["translateKeys","translateValues","reviveValues","multiSegment"],Ae=[].concat(Se,["sanitize","mergeOutput","silentErrors"]),Pe=function(){function e(){t(this,e)}return r(e,[{key:"translate",get:function(){return this.translateKeys||this.translateValues||this.reviveValues}}]),e}(),we=function(e){a(s,e);var n=d(s);function s(e,r,a,o){var u;if(t(this,s),i(l(u=n.call(this)),"enabled",!1),i(l(u),"skip",A()),i(l(u),"pick",A()),i(l(u),"deps",A()),i(l(u),"translateKeys",!1),i(l(u),"translateValues",!1),i(l(u),"reviveValues",!1),u.key=e,u.enabled=r,u.parse=u.enabled,u.applyInheritables(o),u.canBeFiltered=me.includes(e),u.canBeFiltered&&(u.dict=ae.get(e)),void 0!==a)if(Array.isArray(a))u.parse=u.enabled=!0,u.canBeFiltered&&a.length>0&&u.translateTagSet(a,u.pick);else if("object"==typeof a){if(u.enabled=!0,u.parse=!1!==a.parse,u.canBeFiltered){var f=a.pick,c=a.skip;f&&f.length>0&&u.translateTagSet(f,u.pick),c&&c.length>0&&u.translateTagSet(c,u.skip)}u.applyInheritables(a)}else!0===a||!1===a?u.parse=u.enabled=a:D("Invalid options argument: ".concat(a));return u}return r(s,[{key:"needed",get:function(){return this.enabled||this.deps.size>0}},{key:"applyInheritables",value:function(e){var t,n,r=Se;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++)void 0!==(n=e[t=r[i]])&&(this[t]=n)}},{key:"translateTagSet",value:function(e,t){if(this.dict){var n,r,i=this.dict,a=i.tagKeys,s=i.tagValues,o=e;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=b(o));for(var u=0;u<o.length;u++)"string"==typeof(n=o[u])?(-1===(r=s.indexOf(n))&&(r=a.indexOf(Number(n))),-1!==r&&t.add(Number(a[r]))):t.add(n)}else{var f=e;Array.isArray(f)||("function"==typeof f.entries&&(f=f.entries()),f=b(f));for(var c=0;c<f.length;c++){var l=f[c];t.add(l)}}}},{key:"finalizeFilters",value:function(){!this.enabled&&this.deps.size>0?(this.enabled=!0,Ie(this.pick,this.deps)):this.enabled&&this.pick.size>0&&Ie(this.pick,this.deps)}}]),s}(Pe),Oe={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},Ce=P(),xe=function(e){a(i,e);var n=d(i);function i(e){var r;return t(this,i),r=n.call(this),!0===e?r.setupFromTrue():void 0===e?r.setupFromUndefined():Array.isArray(e)?r.setupFromArray(e):"object"==typeof e?r.setupFromObject(e):D("Invalid options argument ".concat(e)),void 0===r.firstChunkSize&&(r.firstChunkSize=C?r.firstChunkSizeBrowser:r.firstChunkSizeNode),r.mergeOutput&&(r.ifd1.enabled=!1),r.filterNestedSegmentTags(),r.traverseTiffDependencyTree(),r.checkLoadedPlugins(),r}return r(i,[{key:"setupFromUndefined",value:function(){var e,t=pe;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=b(t));for(var n=0;n<t.length;n++)this[e=t[n]]=Oe[e];var r=Ae;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++)this[e=r[i]]=Oe[e];var a=be;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=b(a));for(var s=0;s<a.length;s++)this[e=a[s]]=Oe[e];var o=ke;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=b(o));for(var u=0;u<o.length;u++)this[e=o[u]]=new we(e,Oe[e],void 0,this)}},{key:"setupFromTrue",value:function(){var e,t=pe;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=b(t));for(var n=0;n<t.length;n++)this[e=t[n]]=Oe[e];var r=Ae;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++)this[e=r[i]]=Oe[e];var a=be;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=b(a));for(var s=0;s<a.length;s++)this[e=a[s]]=!0;var o=ke;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=b(o));for(var u=0;u<o.length;u++)this[e=o[u]]=new we(e,!0,void 0,this)}},{key:"setupFromArray",value:function(e){var t,n=pe;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++)this[t=n[r]]=Oe[t];var i=Ae;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++)this[t=i[a]]=Oe[t];var s=be;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=b(s));for(var o=0;o<s.length;o++)this[t=s[o]]=Oe[t];var u=ke;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=b(u));for(var f=0;f<u.length;f++)this[t=u[f]]=new we(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,me)}},{key:"setupFromObject",value:function(e){var t;me.ifd0=me.ifd0||me.image,me.ifd1=me.ifd1||me.thumbnail,m(this,e);var n=pe;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++)this[t=n[r]]=Ue(e[t],Oe[t]);var i=Ae;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++)this[t=i[a]]=Ue(e[t],Oe[t]);var s=be;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=b(s));for(var o=0;o<s.length;o++)this[t=s[o]]=Ue(e[t],Oe[t]);var u=ye;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=b(u));for(var f=0;f<u.length;f++)this[t=u[f]]=new we(t,Oe[t],e[t],this);var c=me;Array.isArray(c)||("function"==typeof c.entries&&(c=c.entries()),c=b(c));for(var l=0;l<c.length;l++)this[t=c[l]]=new we(t,Oe[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,me,ke),!0===e.tiff?this.batchEnableWithBool(me,!0):!1===e.tiff?this.batchEnableWithUserValue(me,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,me):"object"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,me)}},{key:"batchEnableWithBool",value:function(e,t){var n=e;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){this[n[r]].enabled=t}}},{key:"batchEnableWithUserValue",value:function(e,t){var n=e;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r],a=t[i];this[i].enabled=!1!==a&&void 0!==a}}},{key:"setupGlobalFilters",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;if(e&&e.length){var i=r;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++){var s=i[a];this[s].enabled=!1}var o=Be(e,n),u=o;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=b(u));for(var f=0;f<u.length;f++){var c=u[f],l=c[0],h=c[1];Ie(this[l].pick,h),this[l].enabled=!0}}else if(t&&t.length){var d=Be(t,n),v=d;Array.isArray(v)||("function"==typeof v.entries&&(v=v.entries()),v=b(v));for(var p=0;p<v.length;p++){var g=v[p],y=g[0],m=g[1];Ie(this[y].skip,m)}}}},{key:"filterNestedSegmentTags",value:function(){var e=this.ifd0,t=this.exif,n=this.xmp,r=this.iptc,i=this.icc;this.makerNote?t.deps.add(ue):t.skip.add(ue),this.userComment?t.deps.add(fe):t.skip.add(fe),n.enabled||e.skip.add(700),r.enabled||e.skip.add(ce),i.enabled||e.skip.add(le)}},{key:"traverseTiffDependencyTree",value:function(){var e=this,t=this.ifd0,n=this.exif,r=this.gps;this.interop.needed&&(n.deps.add(ve),t.deps.add(ve)),n.needed&&t.deps.add(he),r.needed&&t.deps.add(de),this.tiff.enabled=me.some((function(t){return!0===e[t].enabled}))||this.makerNote||this.userComment;var i=me;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++){this[i[a]].finalizeFilters()}}},{key:"onlyTiff",get:function(){var e=this;return!ge.map((function(t){return e[t].enabled})).some((function(e){return!0===e}))&&this.tiff.enabled}},{key:"checkLoadedPlugins",value:function(){var e=ye;Array.isArray(e)||("function"==typeof e.entries&&(e=e.entries()),e=b(e));for(var t=0;t<e.length;t++){var n=e[t];this[n].enabled&&!W.has(n)&&M("segment parser",n)}}}],[{key:"useCached",value:function(e){var t=Ce.get(e);return void 0!==t||(t=new this(e),Ce.set(e,t)),t}}]),i}(Pe);function Be(e,t){var n,r,i,a=[],s=t;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=b(s));for(var o=0;o<s.length;o++){r=s[o],n=[];var u=ae.get(r);Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=b(u));for(var f=0;f<u.length;f++)i=u[f],(e.includes(i[0])||e.includes(i[1]))&&n.push(i[0]);n.length&&a.push([r,n])}return a}function Ue(e,t){return void 0!==e?e:void 0!==t?t:void 0}function Ie(e,t){var n=t;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r];e.add(i)}}function Fe(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function Le(){}function je(e,t){if(!t)return e&&e.then?e.then(Le):Promise.resolve()}function Te(e,t){var n=e();return n&&n.then?n.then(t):t(n)}i(xe,"default",Oe);var De=function(){function e(n){var r=this;t(this,e),i(this,"parsers",{}),i(this,"output",{}),i(this,"errors",[]),i(this,"pushToErrors",(function(e){return r.errors.push(e)})),this.options=xe.useCached(n)}return r(e,[{key:"read",value:function(e){try{var t=this;return Fe(function(e,t){return"string"==typeof e?$(e,t):C&&!x&&e instanceof HTMLImageElement?$(e.src,t):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new N(e):C&&e instanceof Blob?Z(e,t,"blob",te):void D(Q)}(e,t.options),(function(e){t.file=e}))}catch(e){return Promise.reject(e)}}},{key:"setup",value:function(){if(!this.fileParser){var e=this.file,t=e.getUint16(0),n=H;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],s=i[1];if(s.canHandle(e,t))return this.fileParser=new s(this.options,this.file,this.parsers),e[a]=!0}this.file.close&&this.file.close(),D("Unknown file format")}}},{key:"parse",value:function(){try{var e=this,t=e.output,n=e.errors;return e.setup(),Te((function(){return e.options.silentErrors?Fe(e.executeParsers().catch(e.pushToErrors),(function(){n.push.apply(n,e.fileParser.errors)})):je(e.executeParsers())}),(function(){return e.file.close&&e.file.close(),e.options.silentErrors&&n.length>0&&(t.errors=n),L(t)}))}catch(e){return Promise.reject(e)}}},{key:"executeParsers",value:function(){try{var e=this,t=e.output;return Fe(e.fileParser.parse(),(function(){var n=g(e.parsers).map(function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){return Fe(e.parse(),(function(n){e.assignToOutput(t,n)}))})));return e.options.silentErrors&&(n=n.map((function(t){return t.catch(e.pushToErrors)}))),je(Promise.all(n))}))}catch(e){return Promise.reject(e)}}},{key:"extractThumbnail",value:function(){try{var e=this;e.setup();var t,n=e.options,r=e.file,i=W.get("tiff",n);return Te((function(){if(!r.tiff)return function(e){var t=e();if(t&&t.then)return t.then(Le)}((function(){if(r.jpeg)return Fe(e.fileParser.getOrFindSegment("tiff"),(function(e){t=e}))}));t={start:0,type:"tiff"}}),(function(){if(void 0!==t)return Fe(e.fileParser.ensureSegmentChunk(t),(function(t){return Fe((e.parsers.tiff=new i(t,n,r)).extractThumbnail(),(function(e){return r.close&&r.close(),e}))}))}))}catch(e){return Promise.reject(e)}}}]),e}();var _e=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e,t){var n,r,i,a=new De(t);return n=a.read(e),r=function(){return a.parse()},i?r?r(n):n:(n&&n.then||(n=Promise.resolve(n)),r?n.then(r):n)})),ze=Object.freeze({__proto__:null,parse:_e,Exifr:De,fileParsers:H,segmentParsers:W,fileReaders:K,tagKeys:ae,tagValues:se,tagRevivers:oe,createDictionary:re,extendDictionary:ie,fetchUrlAsArrayBuffer:ee,readBlobAsArrayBuffer:te,chunkedProps:pe,otherSegments:ge,segments:ye,tiffBlocks:me,segmentsAndBlocks:ke,tiffExtractables:be,inheritables:Se,allFormatters:Ae,Options:xe});function Ee(){}var Re=function(){function e(n,r,a){var s=this;t(this,e),i(this,"errors",[]),i(this,"ensureSegmentChunk",function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t,n,r,i=e.start,a=e.size||65536;return t=function(){if(s.file.chunked)return function(e){var t=e();if(t&&t.then)return t.then(Ee)}((function(){if(!s.file.available(i,a))return function(e){if(e&&e.then)return e.then(Ee)}(function(e,t){try{var n=e()}catch(e){return t(e)}return n&&n.then?n.then(void 0,t):n}((function(){return t=s.file.readChunk(i,a),n=function(t){e.chunk=t},r?n?n(t):t:(t&&t.then||(t=Promise.resolve(t)),n?t.then(n):t);var t,n,r}),(function(t){D("Couldn't read segment: ".concat(JSON.stringify(e),". ").concat(t.message))})));e.chunk=s.file.subarray(i,a)}));s.file.byteLength>i+a?e.chunk=s.file.subarray(i,a):void 0===e.size?e.chunk=s.file.subarray(i):D("Segment unreachable: "+JSON.stringify(e))},n=function(){return e.chunk},(r=t())&&r.then?r.then(n):n(r)}))),this.extendOptions&&this.extendOptions(n),this.options=n,this.file=r,this.parsers=a}return r(e,[{key:"injectSegment",value:function(e,t){this.options[e].enabled&&this.createParser(e,t)}},{key:"createParser",value:function(e,t){var n=new(W.get(e))(t,this.options,this.file);return this.parsers[e]=n}},{key:"createParsers",value:function(e){var t=e;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=b(t));for(var n=0;n<t.length;n++){var r=t[n],i=r.type,a=r.chunk,s=this.options[i];if(s&&s.enabled){var o=this.parsers[i];o&&o.append||o||this.createParser(i,a)}}}},{key:"readSegments",value:function(e){try{var t=e.map(this.ensureSegmentChunk);return function(e,t){if(!t)return e&&e.then?e.then(Ee):Promise.resolve()}(Promise.all(t))}catch(e){return Promise.reject(e)}}}]),e}(),Ve=function(){function e(n){var r=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;t(this,e),i(this,"errors",[]),i(this,"raw",P()),i(this,"handleError",(function(e){if(!r.options.silentErrors)throw e;r.errors.push(e.message)})),this.chunk=this.normalizeInput(n),this.file=s,this.type=this.constructor.type,this.globalOptions=this.options=a,this.localOptions=a[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}return r(e,[{key:"normalizeInput",value:function(e){return e instanceof N?e:new N(e)}},{key:"translate",value:function(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}},{key:"output",get:function(){return this.translated?this.translated:this.raw?k(this.raw):void 0}},{key:"translateBlock",value:function(e,t){var n=oe.get(t),r=se.get(t),i=ae.get(t),a=this.options[t],s=a.reviveValues&&!!n,o=a.translateValues&&!!r,u=a.translateKeys&&!!i,f={},c=e;Array.isArray(c)||("function"==typeof c.entries&&(c=c.entries()),c=b(c));for(var l=0;l<c.length;l++){var h=c[l],d=h[0],v=h[1];s&&n.has(d)?v=n.get(d)(v):o&&r.has(d)&&(v=this.translateValue(v,r.get(d))),u&&i.has(d)&&(d=i.get(d)||d),f[d]=v}return f}},{key:"translateValue",value:function(e,t){return t[e]||t.DEFAULT||e}},{key:"assignToOutput",value:function(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}},{key:"assignObjectToOutput",value:function(e,t,n){if(this.globalOptions.mergeOutput)return m(e,n);e[t]?m(e[t],n):e[t]=n}}],[{key:"findPosition",value:function(e,t){var n=e.getUint16(t+2)+2,r="function"==typeof this.headerLength?this.headerLength(e,t,n):this.headerLength,i=t+r,a=n-r;return{offset:t,length:n,headerLength:r,start:i,size:a,end:i+a}}},{key:"parse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=new xe(i({},this.type,t)),r=new this(e,n,e);return r.parse()}}]),e}();function Ne(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}i(Ve,"headerLength",4),i(Ve,"type",void 0),i(Ve,"multiSegment",!1),i(Ve,"canHandle",(function(){return!1}));function Me(){}function Ge(e,t){if(!t)return e&&e.then?e.then(Me):Promise.resolve()}function He(e){var t=e();if(t&&t.then)return t.then(Me)}function We(e,t){var n=e();return n&&n.then?n.then(t):t(n)}function Ke(e,t,n){if(!e.s){if(n instanceof Xe){if(!n.s)return void(n.o=Ke.bind(null,e,t));1&t&&(t=n.s),n=n.v}if(n&&n.then)return void n.then(Ke.bind(null,e,t),Ke.bind(null,e,2));e.s=t,e.v=n;var r=e.o;r&&r(e)}}var Xe=function(){function e(){}return e.prototype.then=function(t,n){var r=new e,i=this.s;if(i){var a=1&i?t:n;if(a){try{Ke(r,1,a(this.v))}catch(e){Ke(r,2,e)}return r}return this}return this.o=function(e){try{var i=e.v;1&e.s?Ke(r,1,t?t(i):i):n?Ke(r,1,n(i)):Ke(r,2,i)}catch(e){Ke(r,2,e)}},r},e}();function Ye(e){return e instanceof Xe&&1&e.s}function qe(e,t,n){for(var r;;){var i=e();if(Ye(i)&&(i=i.v),!i)return a;if(i.then){r=0;break}var a=n();if(a&&a.then){if(!Ye(a)){r=1;break}a=a.s}if(t){var s=t();if(s&&s.then&&!Ye(s)){r=2;break}}}var o=new Xe,u=Ke.bind(null,o,2);return(0===r?i.then(c):1===r?a.then(f):s.then(l)).then(void 0,u),o;function f(r){a=r;do{if(t&&(s=t())&&s.then&&!Ye(s))return void s.then(l).then(void 0,u);if(!(i=e())||Ye(i)&&!i.v)return void Ke(o,1,a);if(i.then)return void i.then(c).then(void 0,u);Ye(a=n())&&(a=a.v)}while(!a||!a.then);a.then(f).then(void 0,u)}function c(e){e?(a=n())&&a.then?a.then(f).then(void 0,u):f(a):Ke(o,1,a)}function l(){(i=e())?i.then?i.then(c).then(void 0,u):c(i):Ke(o,1,a)}}function Je(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function Ze(e){return e>=224&&e<=239}function Qe(e,t,n){var r=W;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++){var a=r[i],s=a[0];if(a[1].canHandle(e,t,n))return s}}var $e=function(e){a(s,e);var n=d(s);function s(){var e;t(this,s);for(var r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];return i(l(e=n.call.apply(n,[this].concat(a))),"appSegments",[]),i(l(e),"jpegSegments",[]),i(l(e),"unknownSegments",[]),e}return r(s,[{key:"parse",value:function(){try{var e=this;return Ne(e.findAppSegments(),(function(){return Ne(e.readSegments(e.appSegments),(function(){e.mergeMultiSegments(),e.createParsers(e.mergedAppSegments||e.appSegments)}))}))}catch(e){return Promise.reject(e)}}},{key:"setupSegmentFinderArgs",value:function(e){var t=this;!0===e?(this.findAll=!0,this.wanted=A(W.keyList())):(e=void 0===e?W.keyList().filter((function(e){return t.options[e].enabled})):e.filter((function(e){return t.options[e].enabled&&W.has(e)})),this.findAll=!1,this.remaining=A(e),this.wanted=A(e)),this.unfinishedMultiSegment=!1}},{key:"findAppSegments",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;try{var n=this;n.setupSegmentFinderArgs(t);var r=n.file,i=n.findAll,a=n.wanted,s=n.remaining;return We((function(){if(!i&&n.file.chunked)return i=b(a).some((function(e){var t=W.get(e),r=n.options[e];return t.multiSegment&&r.multiSegment})),He((function(){if(i)return Ge(n.file.readWhole())}))}),(function(){var t=!1;if(e=n.findAppSegmentsInRange(e,r.byteLength),!n.options.onlyTiff)return function(){if(r.chunked){var i=!1;return qe((function(){return!t&&s.size>0&&!i&&(!!r.canReadNextChunk||!!n.unfinishedMultiSegment)}),void 0,(function(){var a=r.nextChunkOffset,s=n.appSegments.some((function(e){return!n.file.available(e.offset||e.start,e.length||e.size)}));return We((function(){return e>a&&!s?Ne(r.readNextChunk(e),(function(e){i=!e})):Ne(r.readNextChunk(a),(function(e){i=!e}))}),(function(){void 0===(e=n.findAppSegmentsInRange(e,r.byteLength))&&(t=!0)}))}))}}()}))}catch(e){return Promise.reject(e)}}},{key:"findAppSegmentsInRange",value:function(e,t){t-=2;for(var n,r,i,a,s,o,u=this.file,f=this.findAll,c=this.wanted,l=this.remaining,h=this.options;e<t;e++)if(255===u.getUint8(e))if(Ze(n=u.getUint8(e+1))){if(r=u.getUint16(e+2),(i=Qe(u,e,r))&&c.has(i)&&(s=(a=W.get(i)).findPosition(u,e),o=h[i],s.type=i,this.appSegments.push(s),!f&&(a.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=s.chunkNumber<s.chunkCount,this.unfinishedMultiSegment||l.delete(i)):l.delete(i),0===l.size)))break;h.recordUnknownSegments&&((s=Ve.findPosition(u,e)).marker=n,this.unknownSegments.push(s)),e+=r+1}else if(Je(n)){if(r=u.getUint16(e+2),218===n&&!1!==h.stopAfterSos)return;h.recordJpegSegments&&this.jpegSegments.push({offset:e,length:r,marker:n}),e+=r+1}return e}},{key:"mergeMultiSegments",value:function(){var e=this;if(this.appSegments.some((function(e){return e.multiSegment}))){var t=function(e,t){for(var n,r,i,a=P(),s=0;s<e.length;s++)r=(n=e[s])[t],a.has(r)?i=a.get(r):a.set(r,i=[]),i.push(n);return b(a)}(this.appSegments,"type");this.mergedAppSegments=t.map((function(t){var n=t[0],r=t[1],i=W.get(n,e.options);return i.handleMultiSegments?{type:n,chunk:i.handleMultiSegments(r)}:r[0]}))}}},{key:"getSegment",value:function(e){return this.appSegments.find((function(t){return t.type===e}))}},{key:"getOrFindSegment",value:function(e){try{var t=this,n=t.getSegment(e);return We((function(){if(void 0===n)return Ne(t.findAppSegments(0,[e]),(function(){n=t.getSegment(e)}))}),(function(){return n}))}catch(e){return Promise.reject(e)}}}],[{key:"canHandle",value:function(e,t){return 65496===t}}]),s}(Re);function et(){}i($e,"type","jpeg"),H.set("jpeg",$e);function tt(e,t){if(!t)return e&&e.then?e.then(et):Promise.resolve()}function nt(e,t){var n=e();return n&&n.then?n.then(t):t(n)}var rt=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];var it=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){try{var e=this;e.parseHeader();var t=e.options;return nt((function(){if(t.ifd0.enabled)return tt(e.parseIfd0Block())}),(function(){return nt((function(){if(t.exif.enabled)return tt(e.safeParse("parseExifBlock"))}),(function(){return nt((function(){if(t.gps.enabled)return tt(e.safeParse("parseGpsBlock"))}),(function(){return nt((function(){if(t.interop.enabled)return tt(e.safeParse("parseInteropBlock"))}),(function(){return nt((function(){if(t.ifd1.enabled)return tt(e.safeParse("parseThumbnailBlock"))}),(function(){return e.createOutput()}))}))}))}))}))}catch(e){return Promise.reject(e)}}},{key:"safeParse",value:function(e){var t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}},{key:"findIfd0Offset",value:function(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}},{key:"findIfd1Offset",value:function(){if(void 0===this.ifd1Offset){this.findIfd0Offset();var e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}},{key:"parseBlock",value:function(e,t){var n=P();return this[t]=n,this.parseTags(e,t,n),n}},{key:"parseIfd0Block",value:function(){try{var e=this;if(e.ifd0)return;var t=e.file;return e.findIfd0Offset(),e.ifd0Offset<8&&D("Malformed EXIF data"),!t.chunked&&e.ifd0Offset>t.byteLength&&D("IFD0 offset points to outside of file.\nthis.ifd0Offset: ".concat(e.ifd0Offset,", file.byteLength: ").concat(t.byteLength)),nt((function(){if(t.tiff)return tt(t.ensureChunk(e.ifd0Offset,z(e.options)))}),(function(){var t=e.parseBlock(e.ifd0Offset,"ifd0");if(0!==t.size)return e.exifOffset=t.get(he),e.interopOffset=t.get(ve),e.gpsOffset=t.get(de),e.xmp=t.get(700),e.iptc=t.get(ce),e.icc=t.get(le),e.options.sanitize&&(t.delete(he),t.delete(ve),t.delete(de),t.delete(700),t.delete(ce),t.delete(le)),t}))}catch(e){return Promise.reject(e)}}},{key:"parseExifBlock",value:function(){try{var e=this;if(e.exif)return;return nt((function(){if(!e.ifd0)return tt(e.parseIfd0Block())}),(function(){if(void 0!==e.exifOffset)return nt((function(){if(e.file.tiff)return tt(e.file.ensureChunk(e.exifOffset,z(e.options)))}),(function(){var t=e.parseBlock(e.exifOffset,"exif");return e.interopOffset||(e.interopOffset=t.get(ve)),e.makerNote=t.get(ue),e.userComment=t.get(fe),e.options.sanitize&&(t.delete(ve),t.delete(ue),t.delete(fe)),e.unpack(t,41728),e.unpack(t,41729),t}))}))}catch(e){return Promise.reject(e)}}},{key:"unpack",value:function(e,t){var n=e.get(t);n&&1===n.length&&e.set(t,n[0])}},{key:"parseGpsBlock",value:function(){try{var e=this;if(e.gps)return;return nt((function(){if(!e.ifd0)return tt(e.parseIfd0Block())}),(function(){if(void 0!==e.gpsOffset){var t=e.parseBlock(e.gpsOffset,"gps");return t&&t.has(2)&&t.has(4)&&(t.set("latitude",at.apply(void 0,t.get(2).concat([t.get(1)]))),t.set("longitude",at.apply(void 0,t.get(4).concat([t.get(3)])))),t}}))}catch(e){return Promise.reject(e)}}},{key:"parseInteropBlock",value:function(){try{var e=this;if(e.interop)return;return nt((function(){if(!e.ifd0)return tt(e.parseIfd0Block())}),(function(){return nt((function(){if(void 0===e.interopOffset&&!e.exif)return tt(e.parseExifBlock())}),(function(){if(void 0!==e.interopOffset)return e.parseBlock(e.interopOffset,"interop")}))}))}catch(e){return Promise.reject(e)}}},{key:"parseThumbnailBlock",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{var t=this;if(t.ifd1||t.ifd1Parsed)return;if(t.options.mergeOutput&&!e)return;return t.findIfd1Offset(),t.ifd1Offset>0&&(t.parseBlock(t.ifd1Offset,"ifd1"),t.ifd1Parsed=!0),t.ifd1}catch(e){return Promise.reject(e)}}},{key:"extractThumbnail",value:function(){try{var e=this;return e.headerParsed||e.parseHeader(),nt((function(){if(!e.ifd1Parsed)return tt(e.parseThumbnailBlock(!0))}),(function(){if(void 0!==e.ifd1){var t=e.ifd1.get(513),n=e.ifd1.get(514);return e.chunk.getUint8Array(t,n)}}))}catch(e){return Promise.reject(e)}}},{key:"image",get:function(){return this.ifd0}},{key:"thumbnail",get:function(){return this.ifd1}},{key:"createOutput",value:function(){var e,t,n,r={},i=me;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=b(i));for(var a=0;a<i.length;a++)if(!T(e=this[t=i[a]]))if(n=this.canTranslate?this.translateBlock(e,t):k(e),this.options.mergeOutput){if("ifd1"===t)continue;m(r,n)}else r[t]=n;return this.makerNote&&(r.makerNote=this.makerNote),this.userComment&&(r.userComment=this.userComment),r}},{key:"assignToOutput",value:function(e,t){if(this.globalOptions.mergeOutput)m(e,t);else{var n=y(t);Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],s=i[1];this.assignObjectToOutput(e,a,s)}}}}],[{key:"canHandle",value:function(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}}]),i}(function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parseHeader",value:function(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}},{key:"parseTags",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:P(),r=this.options[t],i=r.pick,a=r.skip,s=(i=A(i)).size>0,o=0===a.size,u=this.chunk.getUint16(e);e+=2;for(var f=0;f<u;f++){var c=this.chunk.getUint16(e);if(s){if(i.has(c)&&(n.set(c,this.parseTag(e,c,t)),i.delete(c),0===i.size))break}else!o&&a.has(c)||n.set(c,this.parseTag(e,c,t));e+=12}return n}},{key:"parseTag",value:function(e,t,n){var r=this.chunk,i=r.getUint16(e+2),a=r.getUint32(e+4),s=rt[i];if(s*a<=4?e+=8:e=r.getUint32(e+8),(i<1||i>13)&&D("Invalid TIFF value type. block: ".concat(n.toUpperCase(),", tag: ").concat(t.toString(16),", type: ").concat(i,", offset ").concat(e)),e>r.byteLength&&D("Invalid TIFF value offset. block: ".concat(n.toUpperCase(),", tag: ").concat(t.toString(16),", type: ").concat(i,", offset ").concat(e," is outside of chunk size ").concat(r.byteLength)),1===i)return r.getUint8Array(e,a);if(2===i)return _(r.getString(e,a));if(7===i)return r.getUint8Array(e,a);if(1===a)return this.parseTagValue(i,e);for(var o=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(i))(a),u=s,f=0;f<a;f++)o[f]=this.parseTagValue(i,e),e+=u;return o}},{key:"parseTagValue",value:function(e,t){var n=this.chunk;switch(e){case 1:return n.getUint8(t);case 3:return n.getUint16(t);case 4:return n.getUint32(t);case 5:return n.getUint32(t)/n.getUint32(t+4);case 6:return n.getInt8(t);case 8:return n.getInt16(t);case 9:return n.getInt32(t);case 10:return n.getInt32(t)/n.getInt32(t+4);case 11:return n.getFloat(t);case 12:return n.getDouble(t);case 13:return n.getUint32(t);default:D("Invalid tiff type ".concat(e))}}}]),i}(Ve));function at(e,t,n,r){var i=e+t/60+n/3600;return"S"!==r&&"W"!==r||(i*=-1),i}i(it,"type","tiff"),i(it,"headerLength",10),W.set("tiff",it);var st=Object.freeze({__proto__:null,default:ze,Exifr:De,fileParsers:H,segmentParsers:W,fileReaders:K,tagKeys:ae,tagValues:se,tagRevivers:oe,createDictionary:re,extendDictionary:ie,fetchUrlAsArrayBuffer:ee,readBlobAsArrayBuffer:te,chunkedProps:pe,otherSegments:ge,segments:ye,tiffBlocks:me,segmentsAndBlocks:ke,tiffExtractables:be,inheritables:Se,allFormatters:Ae,Options:xe,parse:_e}),ot={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};function ut(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var ft=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t=new De(ct);return ut(t.read(e),(function(){return ut(t.parse(),(function(e){if(e&&e.gps){var t=e.gps;return{latitude:t.latitude,longitude:t.longitude}}}))}))})),ct=m({},ot,{firstChunkSize:4e4,gps:[1,2,3,4]});function lt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function ht(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var dt=ht((function(e){return lt(this.thumbnail(e),(function(e){if(void 0!==e){var t=new Blob([e]);return URL.createObjectURL(t)}}))})),vt=ht((function(e){var t=new De(pt);return lt(t.read(e),(function(){return lt(t.extractThumbnail(),(function(e){return e&&F?U.from(e):e}))}))})),pt=m({},ot,{tiff:!1,ifd1:!0,mergeOutput:!1});function gt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var yt=function(t){return gt(mt(t),(function(t){return m({canvas:e.rotateCanvas,css:e.rotateCss},bt[t])}))},mt=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t=new De(kt);return gt(t.read(e),(function(){return gt(t.parse(),(function(e){if(e&&e.ifd0)return e.ifd0[274]}))}))})),kt=m({},ot,{firstChunkSize:4e4,ifd0:[274]}),bt=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});if(e.rotateCanvas=!0,e.rotateCss=!0,"object"==typeof navigator){var St=navigator.userAgent;if(St.includes("iPad")||St.includes("iPhone")){var At=St.match(/OS (\d+)_(\d+)/);if(At){var Pt=At[1],wt=At[2],Ot=Number(Pt)+.1*Number(wt);e.rotateCanvas=Ot<13.4,e.rotateCss=!1}}else if(St.includes("OS X 10")){var Ct=St.match(/OS X 10[_.](\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(Ct)<15}if(St.includes("Chrome/")){var xt=St.match(/Chrome\/(\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(xt)<81}else if(St.includes("Firefox/")){var Bt=St.match(/Firefox\/(\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(Bt)<77}}function Ut(){}var It=function(e){a(o,e);var n=d(o);function o(){var e;t(this,o);for(var r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];return i(l(e=n.call.apply(n,[this].concat(a))),"ranges",new Ft),0!==e.byteLength&&e.ranges.add(0,e.byteLength),e}return r(o,[{key:"_tryExtend",value:function(e,t,n){if(0===e&&0===this.byteLength&&n){var r=new DataView(n.buffer||n,n.byteOffset,n.byteLength);this._swapDataView(r)}else{var i=e+t;if(i>this.byteLength){var a=this._extend(i).dataView;this._swapDataView(a)}}}},{key:"_extend",value:function(e){var t;t=F?U.allocUnsafe(e):new Uint8Array(e);var n=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:n}}},{key:"subarray",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t=t||this._lengthToEnd(e),n&&this._tryExtend(e,t),this.ranges.add(e,t),v(s(o.prototype),"subarray",this).call(this,e,t)}},{key:"set",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n&&this._tryExtend(t,e.byteLength,e);var r=v(s(o.prototype),"set",this).call(this,e,t);return this.ranges.add(t,r.byteLength),r}},{key:"ensureChunk",value:function(e,t){try{var n=this;if(!n.chunked)return;if(n.ranges.available(e,t))return;return function(e,t){if(!t)return e&&e.then?e.then(Ut):Promise.resolve()}(n.readChunk(e,t))}catch(e){return Promise.reject(e)}}},{key:"available",value:function(e,t){return this.ranges.available(e,t)}}]),o}(N),Ft=function(){function e(){t(this,e),i(this,"list",[])}return r(e,[{key:"length",get:function(){return this.list.length}},{key:"add",value:function(e,t){var n=e+t,r=this.list.filter((function(t){return Lt(e,t.offset,n)||Lt(e,t.end,n)}));if(r.length>0){e=Math.min.apply(Math,[e].concat(r.map((function(e){return e.offset})))),t=(n=Math.max.apply(Math,[n].concat(r.map((function(e){return e.end})))))-e;var i=r.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((function(e){return!r.includes(e)}))}else this.list.push({offset:e,length:t,end:n})}},{key:"available",value:function(e,t){var n=e+t;return this.list.some((function(t){return t.offset<=e&&n<=t.end}))}}]),e}();function Lt(e,t,n){return e<=t&&t<=n}function jt(){}function Tt(e,t){if(!t)return e&&e.then?e.then(jt):Promise.resolve()}var Dt=function(e){a(s,e);var n=d(s);function s(e,r){var a;return t(this,s),i(l(a=n.call(this,0)),"chunksRead",0),a.input=e,a.options=r,a}return r(s,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,Tt(e.readChunk(e.nextChunkOffset))}catch(e){return Promise.reject(e)}}},{key:"readChunked",value:function(){try{var e=this;return e.chunked=!0,Tt(e.readChunk(0,e.options.firstChunkSize))}catch(e){return Promise.reject(e)}}},{key:"readNextChunk",value:function(e){try{var t=this;if(void 0===e&&(e=t.nextChunkOffset),t.fullyRead)return t.chunksRead++,!1;var n=t.options.chunkSize;return r=t.readChunk(e,n),i=function(e){return!!e&&e.byteLength===n},a?i?i(r):r:(r&&r.then||(r=Promise.resolve(r)),i?r.then(i):r)}catch(e){return Promise.reject(e)}var r,i,a}},{key:"readChunk",value:function(e,t){try{var n=this;if(n.chunksRead++,0===(t=n.safeWrapAddress(e,t)))return;return n._readChunk(e,t)}catch(e){return Promise.reject(e)}}},{key:"safeWrapAddress",value:function(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}},{key:"nextChunkOffset",get:function(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}},{key:"canReadNextChunk",get:function(){return this.chunksRead<this.options.chunkLimit}},{key:"fullyRead",get:function(){return void 0!==this.size&&this.nextChunkOffset===this.size}},{key:"read",value:function(){return this.options.chunked?this.readChunked():this.readWhole()}},{key:"close",value:function(){}}]),s}(It);function _t(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var zt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,_t(te(e.input),(function(t){e._swapArrayBuffer(t)}))}catch(e){return Promise.reject(e)}}},{key:"readChunked",value:function(){return this.chunked=!0,this.size=this.input.size,v(s(i.prototype),"readChunked",this).call(this)}},{key:"_readChunk",value:function(e,t){try{var n=this,r=t?e+t:void 0,i=n.input.slice(e,r);return _t(te(i),(function(t){return n.set(t,e,!0)}))}catch(e){return Promise.reject(e)}}}]),i}(Dt);K.set("blob",zt);var Et=Object.freeze({__proto__:null,default:st,Exifr:De,fileParsers:H,segmentParsers:W,fileReaders:K,tagKeys:ae,tagValues:se,tagRevivers:oe,createDictionary:re,extendDictionary:ie,fetchUrlAsArrayBuffer:ee,readBlobAsArrayBuffer:te,chunkedProps:pe,otherSegments:ge,segments:ye,tiffBlocks:me,segmentsAndBlocks:ke,tiffExtractables:be,inheritables:Se,allFormatters:Ae,Options:xe,parse:_e,gps:ft,gpsOnlyOptions:ct,thumbnailUrl:dt,thumbnail:vt,thumbnailOnlyOptions:pt,rotation:yt,orientation:mt,orientationOnlyOptions:kt,rotations:bt,get rotateCanvas(){return e.rotateCanvas},get rotateCss(){return e.rotateCss}});function Rt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var Vt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,Rt(ee(e.input),(function(t){t instanceof ArrayBuffer?e._swapArrayBuffer(t):t instanceof Uint8Array&&e._swapBuffer(t)}))}catch(e){return Promise.reject(e)}}},{key:"_readChunk",value:function(e,t){try{var n=this,r=t?e+t-1:void 0,i=n.options.httpHeaders||{};return(e||r)&&(i.range="bytes=".concat([e,r].join("-"))),Rt(O(n.input,{headers:i}),(function(r){return Rt(r.arrayBuffer(),(function(i){var a=i.byteLength;if(416!==r.status)return a!==t&&(n.size=e+a),n.set(i,e,!0)}))}))}catch(e){return Promise.reject(e)}}}]),i}(Dt);K.set("url",Vt);function Nt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}N.prototype.getUint64=function(e){var t=this.getUint32(e),n=this.getUint32(e+4);return t<1048575?t<<32|n:void 0!==typeof I?(console.warn("Using BigInt because of type 64uint but JS can only handle 53b numbers."),I(t)<<I(32)|I(n)):void D("Trying to read 64b value but JS can only handle 53b numbers.")};function Mt(e,t,n){if(!e.s){if(n instanceof Gt){if(!n.s)return void(n.o=Mt.bind(null,e,t));1&t&&(t=n.s),n=n.v}if(n&&n.then)return void n.then(Mt.bind(null,e,t),Mt.bind(null,e,2));e.s=t,e.v=n;var r=e.o;r&&r(e)}}var Gt=function(){function e(){}return e.prototype.then=function(t,n){var r=new e,i=this.s;if(i){var a=1&i?t:n;if(a){try{Mt(r,1,a(this.v))}catch(e){Mt(r,2,e)}return r}return this}return this.o=function(e){try{var i=e.v;1&e.s?Mt(r,1,t?t(i):i):n?Mt(r,1,n(i)):Mt(r,2,i)}catch(e){Mt(r,2,e)}},r},e}();function Ht(e){return e instanceof Gt&&1&e.s}function Wt(){}function Kt(e,t){if(!t)return e&&e.then?e.then(Wt):Promise.resolve()}var Xt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){try{var e=this,t=e.file.getUint32(0),n=e.parseBoxHead(t);return r=function(e,t,n){for(var r;;){var i=e();if(Ht(i)&&(i=i.v),!i)return a;if(i.then){r=0;break}var a=n();if(a&&a.then){if(!Ht(a)){r=1;break}a=a.s}if(t){var s=t();if(s&&s.then&&!Ht(s)){r=2;break}}}var o=new Gt,u=Mt.bind(null,o,2);return(0===r?i.then(c):1===r?a.then(f):s.then(l)).then(void 0,u),o;function f(r){a=r;do{if(t&&(s=t())&&s.then&&!Ht(s))return void s.then(l).then(void 0,u);if(!(i=e())||Ht(i)&&!i.v)return void Mt(o,1,a);if(i.then)return void i.then(c).then(void 0,u);Ht(a=n())&&(a=a.v)}while(!a||!a.then);a.then(f).then(void 0,u)}function c(e){e?(a=n())&&a.then?a.then(f).then(void 0,u):f(a):Mt(o,1,a)}function l(){(i=e())?i.then?i.then(c).then(void 0,u):c(i):Mt(o,1,a)}}((function(){return"meta"!==n.kind}),void 0,(function(){return t+=n.length,Nt(e.file.ensureChunk(t,16),(function(){n=e.parseBoxHead(t)}))})),i=function(){return Nt(e.file.ensureChunk(n.offset,n.length),(function(){return e.parseBoxFullHead(n),e.parseSubBoxes(n),t=function(){return function(e){var t=e();if(t&&t.then)return t.then(Wt)}((function(){if(e.options.tiff.enabled)return Kt(e.findExif(n))}))},(r=function(){if(e.options.icc.enabled)return Kt(e.findIcc(n))}())&&r.then?r.then(t):t(r);var t,r}))},r&&r.then?r.then(i):i(r)}catch(e){return Promise.reject(e)}var r,i}},{key:"registerSegment",value:function(e,t,n){try{var r=this;return Nt(r.file.ensureChunk(t,n),(function(){var i=r.file.subarray(t,n);r.createParser(e,i)}))}catch(e){return Promise.reject(e)}}},{key:"findIcc",value:function(e){try{var t=this,n=t.findBox(e,"iprp");if(void 0===n)return;var r=t.findBox(n,"ipco");if(void 0===r)return;var i=t.findBox(r,"colr");if(void 0===i)return;return Kt(t.registerSegment("icc",i.offset+12,i.length))}catch(e){return Promise.reject(e)}}},{key:"findExif",value:function(e){try{var t=this,n=t.findBox(e,"iinf");if(void 0===n)return;var r=t.findBox(e,"iloc");if(void 0===r)return;var i=t.findExifLocIdInIinf(n),a=t.findExtentInIloc(r,i);if(void 0===a)return;var s=a[0],o=a[1];return Nt(t.file.ensureChunk(s,o),(function(){var e=4+t.file.getUint32(s);return s+=e,o-=e,Kt(t.registerSegment("tiff",s,o))}))}catch(e){return Promise.reject(e)}}},{key:"findExifLocIdInIinf",value:function(e){this.parseBoxFullHead(e);var t,n,r,i=e.start,a=this.file.getUint16(i);for(i+=2;a--;){if(t=this.parseBoxHead(i),this.parseBoxFullHead(t),n=t.start,t.version>=2&&(r=3===t.version?4:2,"Exif"===this.file.getString(n+r+2,4)))return this.file.getUintBytes(n,r);i+=t.length}}},{key:"get8bits",value:function(e){var t=this.file.getUint8(e);return[t>>4,15&t]}},{key:"findExtentInIloc",value:function(e,t){this.parseBoxFullHead(e);var n=e.start,r=this.get8bits(n++),i=r[0],a=r[1],s=this.get8bits(n++),o=s[0],u=s[1],f=2===e.version?4:2,c=1===e.version||2===e.version?2:0,l=u+i+a,h=2===e.version?4:2,d=this.file.getUintBytes(n,h);for(n+=h;d--;){var v=this.file.getUintBytes(n,f);n+=f+c+2+o;var p=this.file.getUint16(n);if(n+=2,v===t)return p>1&&console.warn("ILOC box has more than one extent but we're only processing one\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file"),[this.file.getUintBytes(n+u,i),this.file.getUintBytes(n+u+i,a)];n+=p*l}}}],[{key:"canHandle",value:function(e,t){if(0!==t)return!1;var n=e.getUint16(2);if(n>50)return!1;for(var r=16,i=[];r<n;)i.push(e.getString(r,4)),r+=4;return i.includes(this.type)}}]),i}(function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parseBoxes",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=[];e<this.file.byteLength-4;){var n=this.parseBoxHead(e);if(t.push(n),0===n.length)break;e+=n.length}return t}},{key:"parseSubBoxes",value:function(e){e.boxes=this.parseBoxes(e.start)}},{key:"findBox",value:function(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((function(e){return e.kind===t}))}},{key:"parseBoxHead",value:function(e){var t=this.file.getUint32(e),n=this.file.getString(e+4,4),r=e+8;return 1===t&&(t=this.file.getUint64(e+8),r+=8),{offset:e,length:t,kind:n,start:r}}},{key:"parseBoxFullHead",value:function(e){if(void 0===e.version){var t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}}]),i}(Re)),Yt=function(e){a(r,e);var n=d(r);function r(){return t(this,r),n.apply(this,arguments)}return r}(Xt);i(Yt,"type","heic");var qt=function(e){a(r,e);var n=d(r);function r(){return t(this,r),n.apply(this,arguments)}return r}(Xt);i(qt,"type","avif"),H.set("heic",Yt),H.set("avif",qt),re(ae,["ifd0","ifd1"],[[256,"ImageWidth"],[257,"ImageHeight"],[258,"BitsPerSample"],[259,"Compression"],[262,"PhotometricInterpretation"],[270,"ImageDescription"],[271,"Make"],[272,"Model"],[273,"StripOffsets"],[274,"Orientation"],[277,"SamplesPerPixel"],[278,"RowsPerStrip"],[279,"StripByteCounts"],[282,"XResolution"],[283,"YResolution"],[284,"PlanarConfiguration"],[296,"ResolutionUnit"],[301,"TransferFunction"],[305,"Software"],[306,"ModifyDate"],[315,"Artist"],[316,"HostComputer"],[317,"Predictor"],[318,"WhitePoint"],[319,"PrimaryChromaticities"],[513,"ThumbnailOffset"],[514,"ThumbnailLength"],[529,"YCbCrCoefficients"],[530,"YCbCrSubSampling"],[531,"YCbCrPositioning"],[532,"ReferenceBlackWhite"],[700,"ApplicationNotes"],[33432,"Copyright"],[33723,"IPTC"],[34665,"ExifIFD"],[34675,"ICC"],[34853,"GpsIFD"],[330,"SubIFD"],[40965,"InteropIFD"],[40091,"XPTitle"],[40092,"XPComment"],[40093,"XPAuthor"],[40094,"XPKeywords"],[40095,"XPSubject"]]),re(ae,"exif",[[33434,"ExposureTime"],[33437,"FNumber"],[34850,"ExposureProgram"],[34852,"SpectralSensitivity"],[34855,"ISO"],[34858,"TimeZoneOffset"],[34859,"SelfTimerMode"],[34864,"SensitivityType"],[34865,"StandardOutputSensitivity"],[34866,"RecommendedExposureIndex"],[34867,"ISOSpeed"],[34868,"ISOSpeedLatitudeyyy"],[34869,"ISOSpeedLatitudezzz"],[36864,"ExifVersion"],[36867,"DateTimeOriginal"],[36868,"CreateDate"],[36873,"GooglePlusUploadCode"],[36880,"OffsetTime"],[36881,"OffsetTimeOriginal"],[36882,"OffsetTimeDigitized"],[37121,"ComponentsConfiguration"],[37122,"CompressedBitsPerPixel"],[37377,"ShutterSpeedValue"],[37378,"ApertureValue"],[37379,"BrightnessValue"],[37380,"ExposureCompensation"],[37381,"MaxApertureValue"],[37382,"SubjectDistance"],[37383,"MeteringMode"],[37384,"LightSource"],[37385,"Flash"],[37386,"FocalLength"],[37393,"ImageNumber"],[37394,"SecurityClassification"],[37395,"ImageHistory"],[37396,"SubjectArea"],[37500,"MakerNote"],[37510,"UserComment"],[37520,"SubSecTime"],[37521,"SubSecTimeOriginal"],[37522,"SubSecTimeDigitized"],[37888,"AmbientTemperature"],[37889,"Humidity"],[37890,"Pressure"],[37891,"WaterDepth"],[37892,"Acceleration"],[37893,"CameraElevationAngle"],[40960,"FlashpixVersion"],[40961,"ColorSpace"],[40962,"ExifImageWidth"],[40963,"ExifImageHeight"],[40964,"RelatedSoundFile"],[41483,"FlashEnergy"],[41486,"FocalPlaneXResolution"],[41487,"FocalPlaneYResolution"],[41488,"FocalPlaneResolutionUnit"],[41492,"SubjectLocation"],[41493,"ExposureIndex"],[41495,"SensingMethod"],[41728,"FileSource"],[41729,"SceneType"],[41730,"CFAPattern"],[41985,"CustomRendered"],[41986,"ExposureMode"],[41987,"WhiteBalance"],[41988,"DigitalZoomRatio"],[41989,"FocalLengthIn35mmFormat"],[41990,"SceneCaptureType"],[41991,"GainControl"],[41992,"Contrast"],[41993,"Saturation"],[41994,"Sharpness"],[41996,"SubjectDistanceRange"],[42016,"ImageUniqueID"],[42032,"OwnerName"],[42033,"SerialNumber"],[42034,"LensInfo"],[42035,"LensMake"],[42036,"LensModel"],[42037,"LensSerialNumber"],[42080,"CompositeImage"],[42081,"CompositeImageCount"],[42082,"CompositeImageExposureTimes"],[42240,"Gamma"],[59932,"Padding"],[59933,"OffsetSchema"],[65e3,"OwnerName"],[65001,"SerialNumber"],[65002,"Lens"],[65100,"RawFile"],[65101,"Converter"],[65102,"WhiteBalance"],[65105,"Exposure"],[65106,"Shadows"],[65107,"Brightness"],[65108,"Contrast"],[65109,"Saturation"],[65110,"Sharpness"],[65111,"Smoothness"],[65112,"MoireFilter"],[40965,"InteropIFD"]]),re(ae,"gps",[[0,"GPSVersionID"],[1,"GPSLatitudeRef"],[2,"GPSLatitude"],[3,"GPSLongitudeRef"],[4,"GPSLongitude"],[5,"GPSAltitudeRef"],[6,"GPSAltitude"],[7,"GPSTimeStamp"],[8,"GPSSatellites"],[9,"GPSStatus"],[10,"GPSMeasureMode"],[11,"GPSDOP"],[12,"GPSSpeedRef"],[13,"GPSSpeed"],[14,"GPSTrackRef"],[15,"GPSTrack"],[16,"GPSImgDirectionRef"],[17,"GPSImgDirection"],[18,"GPSMapDatum"],[19,"GPSDestLatitudeRef"],[20,"GPSDestLatitude"],[21,"GPSDestLongitudeRef"],[22,"GPSDestLongitude"],[23,"GPSDestBearingRef"],[24,"GPSDestBearing"],[25,"GPSDestDistanceRef"],[26,"GPSDestDistance"],[27,"GPSProcessingMethod"],[28,"GPSAreaInformation"],[29,"GPSDateStamp"],[30,"GPSDifferential"],[31,"GPSHPositioningError"]]),re(se,["ifd0","ifd1"],[[274,{1:"Horizontal (normal)",2:"Mirror horizontal",3:"Rotate 180",4:"Mirror vertical",5:"Mirror horizontal and rotate 270 CW",6:"Rotate 90 CW",7:"Mirror horizontal and rotate 90 CW",8:"Rotate 270 CW"}],[296,{1:"None",2:"inches",3:"cm"}]]);var Jt=re(se,"exif",[[34850,{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"}],[37121,{0:"-",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}],[37383,{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"}],[37384,{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"}],[37385,{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"}],[41495,{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"}],[41728,{1:"Film Scanner",2:"Reflection Print Scanner",3:"Digital Camera"}],[41729,{1:"Directly photographed"}],[41985,{0:"Normal",1:"Custom",2:"HDR (no original saved)",3:"HDR (original saved)",4:"Original (for HDR)",6:"Panorama",7:"Portrait HDR",8:"Portrait"}],[41986,{0:"Auto",1:"Manual",2:"Auto bracket"}],[41987,{0:"Auto",1:"Manual"}],[41990,{0:"Standard",1:"Landscape",2:"Portrait",3:"Night",4:"Other"}],[41991,{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"}],[41996,{0:"Unknown",1:"Macro",2:"Close",3:"Distant"}],[42080,{0:"Unknown",1:"Not a Composite Image",2:"General Composite Image",3:"Composite Image Captured While Shooting"}]]),Zt={1:"No absolute unit of measurement",2:"Inch",3:"Centimeter"};Jt.set(37392,Zt),Jt.set(41488,Zt);var Qt={0:"Normal",1:"Low",2:"High"};function $t(e){return"object"==typeof e&&void 0!==e.length?e[0]:e}function en(e){var t=b(e).slice(1);return t[1]>15&&(t=t.map((function(e){return String.fromCharCode(e)}))),"0"!==t[2]&&0!==t[2]||t.pop(),t.join(".")}function tn(e){if("string"==typeof e){var t=e.trim().split(/[-: ]/g).map(Number),n=t[0],r=t[1],i=t[2],a=t[3],s=t[4],o=t[5],u=new Date(n,r-1,i);return isNaN(a)||isNaN(s)||isNaN(o)||(u.setHours(a),u.setMinutes(s),u.setSeconds(o)),isNaN(+u)?e:u}}function nn(e){if("string"==typeof e)return e;var t=[];if(0===e[1]&&0===e[e.length-1])for(var n=0;n<e.length;n+=2)t.push(rn(e[n+1],e[n]));else for(var r=0;r<e.length;r+=2)t.push(rn(e[r],e[r+1]));return _(String.fromCodePoint.apply(String,t))}function rn(e,t){return e<<8|t}Jt.set(41992,Qt),Jt.set(41993,Qt),Jt.set(41994,Qt),re(oe,["ifd0","ifd1"],[[50827,function(e){return"string"!=typeof e?V(e):e}],[306,tn],[40091,nn],[40092,nn],[40093,nn],[40094,nn],[40095,nn]]),re(oe,"exif",[[40960,en],[36864,en],[36867,tn],[36868,tn],[40962,$t],[40963,$t]]),re(oe,"gps",[[0,function(e){return b(e).join(".")}],[7,function(e){return b(e).join(":")}]]);var an="http://ns.adobe.com/",sn="http://ns.adobe.com/xmp/extension/",on=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"normalizeInput",value:function(e){return"string"==typeof e?e:N.from(e).getString()}},{key:"parse",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.chunk;if(!this.localOptions.parse)return e;e=bn(e);var t=ln.findAll(e,"rdf","Description");0===t.length&&t.push(new ln("rdf","Description",void 0,e));var n={},r=t;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++){var a=r[i],s=a.properties;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=b(s));for(var o=0;o<s.length;o++){var u=s[o];hn(u,pn(u.ns,n))}}return un(n)}},{key:"assignToOutput",value:function(e,t){if(this.localOptions.parse){var n=y(t);Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=b(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],s=i[1];switch(a){case"tiff":this.assignObjectToOutput(e,"ifd0",s);break;case"exif":this.assignObjectToOutput(e,"exif",s);break;case"xmlns":break;default:this.assignObjectToOutput(e,a,s)}}}else e.xmp=t}}],[{key:"canHandle",value:function(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&e.getString(t+4,an.length)===an}},{key:"headerLength",value:function(e,t){return e.getString(t+4,sn.length)===sn?79:4+"http://ns.adobe.com/xap/1.0/".length+1}},{key:"findPosition",value:function(e,t){var n=v(s(i),"findPosition",this).call(this,e,t);return n.multiSegment=n.extended=79===n.headerLength,n.multiSegment?(n.chunkCount=e.getUint8(t+72),n.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&n.chunkNumber++):(n.chunkCount=1/0,n.chunkNumber=-1),n}},{key:"handleMultiSegments",value:function(e){return e.map((function(e){return e.chunk.getString()})).join("")}}]),i}(Ve);function un(e){for(var t in e)void 0===(e[t]=L(e[t]))&&delete e[t];return L(e)}i(on,"type","xmp"),i(on,"multiSegment",!0),W.set("xmp",on);var fn=function(){function e(n,r,i){t(this,e),this.ns=n,this.name=r,this.value=i}return r(e,[{key:"serialize",value:function(){return this.value}}],[{key:"findAll",value:function(t){return gn(t,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=("[^"]*"|'[^']*')/gm).map(e.unpackMatch)}},{key:"unpackMatch",value:function(t){var n=t[1],r=t[2],i=t[3].slice(1,-1);return new e(n,r,i=yn(i))}}]),e}(),cn="[\\w\\d-]+",ln=function(){function e(n,r,i,a){t(this,e),this.ns=n,this.name=r,this.attrString=i,this.innerXml=a,this.attrs=fn.findAll(i),this.children=e.findAll(a),this.value=0===this.children.length?yn(a):void 0,this.properties=[].concat(this.attrs,this.children)}return r(e,[{key:"isPrimitive",get:function(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}},{key:"isListContainer",get:function(){return 1===this.children.length&&this.children[0].isList}},{key:"isList",get:function(){var e=this.ns,t=this.name;return"rdf"===e&&("Seq"===t||"Bag"===t||"Alt"===t)}},{key:"isListItem",get:function(){return"rdf"===this.ns&&"li"===this.name}},{key:"serialize",value:function(){if(0!==this.properties.length||void 0!==this.value){if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return vn(this.children.map(dn));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();var e={},t=this.properties;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=b(t));for(var n=0;n<t.length;n++){hn(t[n],e)}return void 0!==this.value&&(e.value=this.value),L(e)}}}],[{key:"findAll",value:function(t,n,r){if(void 0!==n||void 0!==r){n=n||cn,r=r||cn;var i=new RegExp("<(".concat(n,"):(").concat(r,")(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)"),"gm")}else i=/<([\w\d-]+):([\w\d-]+)(#\d+)?((\s+?[\w\d-:]+=("[^"]*"|'[^']*'))*\s*)(\/>|>([\s\S]*?)<\/\1:\2\3>)/gm;return gn(t,i).map(e.unpackMatch)}},{key:"unpackMatch",value:function(t){return new e(t[1],t[2],t[4],t[8])}}]),e}();function hn(e,t){var n=e.serialize();void 0!==n&&(t[e.name]=n)}var dn=function(e){return e.serialize()},vn=function(e){return 1===e.length?e[0]:e},pn=function(e,t){return t[e]?t[e]:t[e]={}};function gn(e,t){var n,r=[];if(!e)return r;for(;null!==(n=t.exec(e));)r.push(n);return r}function yn(e){if(!function(e){return null==e||"null"===e||"undefined"===e||""===e||""===e.trim()}(e)){var t=Number(e);if(!isNaN(t))return t;var n=e.toLowerCase();return"true"===n||"false"!==n&&e.trim()}}var mn=["rdf:li","rdf:Seq","rdf:Bag","rdf:Alt","rdf:Description"],kn=new RegExp("(<|\\/)(".concat(mn.join("|"),")"),"g");function bn(e){var t={},n={},r=mn;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=b(r));for(var i=0;i<r.length;i++){var a=r[i];t[a]=[],n[a]=0}return e.replace(kn,(function(e,r,i){if("<"===r){var a=++n[i];return t[i].push(a),"".concat(e,"#").concat(a)}var s=t[i].pop();return"".concat(e,"#").concat(s)}))}e.Exifr=De,e.Options=xe,e.allFormatters=Ae,e.chunkedProps=pe,e.createDictionary=re,e.default=Et,e.extendDictionary=ie,e.fetchUrlAsArrayBuffer=ee,e.fileParsers=H,e.fileReaders=K,e.gps=ft,e.gpsOnlyOptions=ct,e.inheritables=Se,e.orientation=mt,e.orientationOnlyOptions=kt,e.otherSegments=ge,e.parse=_e,e.readBlobAsArrayBuffer=te,e.rotation=yt,e.rotations=bt,e.segmentParsers=W,e.segments=ye,e.segmentsAndBlocks=ke,e.tagKeys=ae,e.tagRevivers=oe,e.tagValues=se,e.thumbnail=vt,e.thumbnailOnlyOptions=pt,e.thumbnailUrl=dt,e.tiffBlocks=me,e.tiffExtractables=be,Object.defineProperty(e,"__esModule",{value:!0})}));
