import {tagValues, createDictionary} from '../tags.mjs'


// inspired by & slightly modified from
// https://exiftool.org/TagNames/ICC_Profile.html

const companies = {
	'4d2p': 'Erdt Systems',
	AAMA: 'Aamazing Technologies',
	ACER: 'Acer',
	ACLT: 'Acolyte Color Research',
	ACTI: 'Actix Sytems',
	ADAR: 'Adara Technology',
	ADBE: 'Adobe',
	ADI:  'ADI Systems',
	AGFA: 'Agfa Graphics',
	ALMD: 'Alps Electric',
	ALPS: 'Alps Electric',
	ALWN: 'Alwan Color Expertise',
	AMTI: 'Amiable Technologies',
	AOC:  'AOC International',
	APAG: 'Apago',
	APPL: 'Apple Computer',
	AST:  'AST',
	'AT&T': 'AT&T',
	BAEL: 'BARBIERI electronic',
	BRCO: 'Barco NV',
	BRKP: 'Breakpoint',
	BROT: 'Brother',
	BULL: 'Bull',
	BUS:  'Bus Computer Systems',
	'C-IT': 'C-Itoh',
	CAMR: 'Intel',
	CANO: 'Canon',
	CARR: 'Carroll Touch',
	CASI: 'Casio',
	CBUS: 'Colorbus PL',
	CEL:  'Crossfield',
	CELx: 'Crossfield',
	CGS:  'CGS Publishing Technologies International',
	CHM:  'Rochester Robotics',
	CIGL: 'Colour Imaging Group, London',
	CITI: 'Citizen',
	CL00: 'Candela',
	CLIQ: 'Color IQ',
	CMCO: 'Chromaco',
	CMiX: 'CHROMiX',
	COLO: 'Colorgraphic Communications',
	COMP: 'Compaq',
	COMp: 'Compeq/Focus Technology',
	CONR: 'Conrac Display Products',
	CORD: 'Cordata Technologies',
	CPQ:  'Compaq',
	CPRO: 'ColorPro',
	CRN:  'Cornerstone',
	CTX:  'CTX International',
	CVIS: 'ColorVision',
	CWC:  'Fujitsu Laboratories',
	DARI: 'Darius Technology',
	DATA: 'Dataproducts',
	DCP:  'Dry Creek Photo',
	DCRC: 'Digital Contents Resource Center, Chung-Ang University',
	DELL: 'Dell Computer',
	DIC:  'Dainippon Ink and Chemicals',
	DICO: 'Diconix',
	DIGI: 'Digital',
	'DL&C': 'Digital Light & Color',
	DPLG: 'Doppelganger',
	DS:   'Dainippon Screen',
	DSOL: 'DOOSOL',
	DUPN: 'DuPont',
	EPSO: 'Epson',
	ESKO: 'Esko-Graphics',
	ETRI: 'Electronics and Telecommunications Research Institute',
	EVER: 'Everex Systems',
	EXAC: 'ExactCODE',
	Eizo: 'Eizo',
	FALC: 'Falco Data Products',
	FF:   'Fuji Photo Film',
	FFEI: 'FujiFilm Electronic Imaging',
	FNRD: 'Fnord Software',
	FORA: 'Fora',
	FORE: 'Forefront Technology',
	FP:   'Fujitsu',
	FPA:  'WayTech Development',
	FUJI: 'Fujitsu',
	FX:   'Fuji Xerox',
	GCC:  'GCC Technologies',
	GGSL: 'Global Graphics Software',
	GMB:  'Gretagmacbeth',
	GMG:  'GMG',
	GOLD: 'GoldStar Technology',
	GOOG: 'Google',
	GPRT: 'Giantprint',
	GTMB: 'Gretagmacbeth',
	GVC:  'WayTech Development',
	GW2K: 'Sony',
	HCI:  'HCI',
	HDM:  'Heidelberger Druckmaschinen',
	HERM: 'Hermes',
	HITA: 'Hitachi America',
	HP:   'Hewlett-Packard',
	HTC:  'Hitachi',
	HiTi: 'HiTi Digital',
	IBM:  'IBM',
	IDNT: 'Scitex',
	IEC:  'Hewlett-Packard',
	IIYA: 'Iiyama North America',
	IKEG: 'Ikegami Electronics',
	IMAG: 'Image Systems',
	IMI:  'Ingram Micro',
	INTC: 'Intel',
	INTL: 'N/A (INTL)',
	INTR: 'Intra Electronics',
	IOCO: 'Iocomm International Technology',
	IPS:  'InfoPrint Solutions Company',
	IRIS: 'Scitex',
	ISL:  'Ichikawa Soft Laboratory',
	ITNL: 'N/A (ITNL)',
	IVM:  'IVM',
	IWAT: 'Iwatsu Electric',
	Idnt: 'Scitex',
	Inca: 'Inca Digital Printers',
	Iris: 'Scitex',
	JPEG: 'Joint Photographic Experts Group',
	JSFT: 'Jetsoft Development',
	JVC:  'JVC Information Products',
	KART: 'Scitex',
	KFC:  'KFC Computek Components',
	KLH:  'KLH Computers',
	KMHD: 'Konica Minolta',
	KNCA: 'Konica',
	KODA: 'Kodak',
	KYOC: 'Kyocera',
	Kart: 'Scitex',
	LCAG: 'Leica',
	LCCD: 'Leeds Colour',
	LDAK: 'Left Dakota',
	LEAD: 'Leading Technology',
	LEXM: 'Lexmark International',
	LINK: 'Link Computer',
	LINO: 'Linotronic',
	LITE: 'Lite-On',
	Leaf: 'Leaf',
	Lino: 'Linotronic',
	MAGC: 'Mag Computronic',
	MAGI: 'MAG Innovision',
	MANN: 'Mannesmann',
	MICN: 'Micron Technology',
	MICR: 'Microtek',
	MICV: 'Microvitec',
	MINO: 'Minolta',
	MITS: 'Mitsubishi Electronics America',
	MITs: 'Mitsuba',
	MNLT: 'Minolta',
	MODG: 'Modgraph',
	MONI: 'Monitronix',
	MONS: 'Monaco Systems',
	MORS: 'Morse Technology',
	MOTI: 'Motive Systems',
	MSFT: 'Microsoft',
	MUTO: 'MUTOH INDUSTRIES',
	Mits: 'Mitsubishi Electric',
	NANA: 'NANAO',
	NEC:  'NEC',
	NEXP: 'NexPress Solutions',
	NISS: 'Nissei Sangyo America',
	NKON: 'Nikon',
	NONE: 'none',
	OCE:  'Oce Technologies',
	OCEC: 'OceColor',
	OKI:  'Oki',
	OKID: 'Okidata',
	OKIP: 'Okidata',
	OLIV: 'Olivetti',
	OLYM: 'Olympus',
	ONYX: 'Onyx Graphics',
	OPTI: 'Optiquest',
	PACK: 'Packard Bell',
	PANA: 'Matsushita Electric Industrial',
	PANT: 'Pantone',
	PBN:  'Packard Bell',
	PFU:  'PFU',
	PHIL: 'Philips Consumer Electronics',
	PNTX: 'HOYA',
	POne: 'Phase One A/S',
	PREM: 'Premier Computer Innovations',
	PRIN: 'Princeton Graphic Systems',
	PRIP: 'Princeton Publishing Labs',
	QLUX: 'Hong Kong',
	QMS:  'QMS',
	QPCD: 'QPcard AB',
	QUAD: 'QuadLaser',
	QUME: 'Qume',
	RADI: 'Radius',
	RDDx: 'Integrated Color Solutions',
	RDG:  'Roland DG',
	REDM: 'REDMS Group',
	RELI: 'Relisys',
	RGMS: 'Rolf Gierling Multitools',
	RICO: 'Ricoh',
	RNLD: 'Edmund Ronald',
	ROYA: 'Royal',
	RPC:  'Ricoh Printing Systems',
	RTL:  'Royal Information Electronics',
	SAMP: 'Sampo',
	SAMS: 'Samsung',
	SANT: 'Jaime Santana Pomares',
	SCIT: 'Scitex',
	SCRN: 'Dainippon Screen',
	SDP:  'Scitex',
	SEC:  'Samsung',
	SEIK: 'Seiko Instruments',
	SEIk: 'Seikosha',
	SGUY: 'ScanGuy.com',
	SHAR: 'Sharp Laboratories',
	SICC: 'International Color Consortium',
	SONY: 'Sony',
	SPCL: 'SpectraCal',
	STAR: 'Star',
	STC:  'Sampo Technology',
	Scit: 'Scitex',
	Sdp:  'Scitex',
	Sony: 'Sony',
	TALO: 'Talon Technology',
	TAND: 'Tandy',
	TATU: 'Tatung',
	TAXA: 'TAXAN America',
	TDS:  'Tokyo Denshi Sekei',
	TECO: 'TECO Information Systems',
	TEGR: 'Tegra',
	TEKT: 'Tektronix',
	TI:   'Texas Instruments',
	TMKR: 'TypeMaker',
	TOSB: 'Toshiba',
	TOSH: 'Toshiba',
	TOTK: 'TOTOKU ELECTRIC',
	TRIU: 'Triumph',
	TSBT: 'Toshiba',
	TTX:  'TTX Computer Products',
	TVM:  'TVM Professional Monitor',
	TW:   'TW Casper',
	ULSX: 'Ulead Systems',
	UNIS: 'Unisys',
	UTZF: 'Utz Fehlau & Sohn',
	VARI: 'Varityper',
	VIEW: 'Viewsonic',
	VISL: 'Visual communication',
	VIVO: 'Vivo Mobile Communication',
	WANG: 'Wang',
	WLBR: 'Wilbur Imaging',
	WTG2: 'Ware To Go',
	WYSE: 'WYSE Technology',
	XERX: 'Xerox',
	XRIT: 'X-Rite',
	ZRAN: 'Zoran',
	Zebr: 'Zebra Technologies',
	appl: 'Apple Computer',
	bICC: 'basICColor',
	berg: 'bergdesign',
	ceyd: 'Integrated Color Solutions',
	clsp: 'MacDermid ColorSpan',
	ds:   'Dainippon Screen',
	dupn: 'DuPont',
	ffei: 'FujiFilm Electronic Imaging',
	flux: 'FluxData',
	iris: 'Scitex',
	kart: 'Scitex',
	lcms: 'Little CMS',
	lino: 'Linotronic',
	none: 'none',
	ob4d: 'Erdt Systems',
	obic: 'Medigraph',
	quby: 'Qubyx Sarl',
	scit: 'Scitex',
	scrn: 'Dainippon Screen',
	sdp:  'Scitex',
	siwi: 'SIWI GRAFIKA',
	yxym: 'YxyMaster',
}

// 12 ProfileClass
const devices = {
	scnr: 'Scanner',
	mntr: 'Monitor',
	prtr: 'Printer',
	link: 'Device Link',
	abst: 'Abstract',
	spac: 'Color Space Conversion Profile',
	nmcl: 'Named Color',
	cenc: 'ColorEncodingSpace profile',
	mid:  'MultiplexIdentification profile',
	mlnk: 'MultiplexLink profile',
	mvis: 'MultiplexVisualization profile',
	nkpf: 'Nikon Input Device Profile (NON-STANDARD!)',
}

const tech = {
	amd:    'Active Matrix Display',
	crt:    'Cathode Ray Tube Display',
	kpcd:   'Photo CD',
	pmd:    'Passive Matrix Display',
	dcam:   'Digital Camera',
	dcpj:   'Digital Cinema Projector',
	dmpc:   'Digital Motion Picture Camera',
	dsub:   'Dye Sublimation Printer',
	epho:   'Electrophotographic Printer',
	esta:   'Electrostatic Printer',
	flex:   'Flexography',
	fprn:   'Film Writer',
	fscn:   'Film Scanner',
	grav:   'Gravure',
	ijet:   'Ink Jet Printer',
	imgs:   'Photo Image Setter',
	mpfr:   'Motion Picture Film Recorder',
	mpfs:   'Motion Picture Film Scanner',
	offs:   'Offset Lithography',
	pjtv:   'Projection Television',
	rpho:   'Photographic Paper Printer',
	rscn:   'Reflective Scanner',
	silk:   'Silkscreen',
	twax:   'Thermal Wax Printer',
	vidc:   'Video Camera',
	vidm:   'Video Monitor',
}


createDictionary(tagValues, 'icc', [
	[4,  companies],
	[12, devices],
	[40, Object.assign({}, companies, devices)],
	[48, companies],
	[80, companies],
	[64, {
		0: 'Perceptual',
		1: 'Relative Colorimetric',
		2: 'Saturation',
		3: 'Absolute Colorimetric',
	}],
	['tech', tech],
])