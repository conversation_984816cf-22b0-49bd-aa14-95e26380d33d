import {tagKeys, createDictionary} from '../tags.mjs'


// inspired by
// http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/EXIF.html
// TODO: check and add missing ones from:
// https://metacpan.org/pod/distribution/Image-ExifTool/lib/Image/ExifTool/TagNames.pod#EXIF-Tags
// https://metacpan.org/pod/Image::MetaData::JPEG::TagLists#Exif-tags-for-the-0th-IFD-Exif-private-subdirectory

createDictionary(tagKeys, 'exif', [
	[0x829a, 'ExposureTime'],
	[0x829d, 'FNumber'],
	[0x8822, 'ExposureProgram'],
	[0x8824, 'SpectralSensitivity'],
	[0x8827, 'ISO'],
	[0x882a, 'TimeZoneOffset'],
	[0x882b, 'SelfTimerMode'],
	[0x8830, 'SensitivityType'],
	[0x8831, 'StandardOutputSensitivity'],
	[0x8832, 'RecommendedExposureIndex'],
	[0x8833, 'ISOSpeed'],
	[0x8834, 'ISOSpeedLatitudeyyy'],
	[0x8835, 'ISOSpeedLatitudezzz'],
	[0x9000, 'ExifVersion'],
	[0x9003, 'DateTimeOriginal'],
	[0x9004, 'CreateDate'],
	[0x9009, 'GooglePlusUploadCode'],
	[0x9010, 'OffsetTime'],
	[0x9011, 'OffsetTimeOriginal'],
	[0x9012, 'OffsetTimeDigitized'],
	[0x9101, 'ComponentsConfiguration'],
	[0x9102, 'CompressedBitsPerPixel'],
	[0x9201, 'ShutterSpeedValue'],
	[0x9202, 'ApertureValue'],
	[0x9203, 'BrightnessValue'],
	[0x9204, 'ExposureCompensation'],
	[0x9205, 'MaxApertureValue'],
	[0x9206, 'SubjectDistance'],
	[0x9207, 'MeteringMode'],
	[0x9208, 'LightSource'],
	[0x9209, 'Flash'],
	[0x920a, 'FocalLength'],
	[0x9211, 'ImageNumber'],
	[0x9212, 'SecurityClassification'],
	[0x9213, 'ImageHistory'],
	[0x9214, 'SubjectArea'],
	[0x927c, 'MakerNote'],
	[0x9286, 'UserComment'],
	[0x9290, 'SubSecTime'],
	[0x9291, 'SubSecTimeOriginal'],
	[0x9292, 'SubSecTimeDigitized'],
	[0x9400, 'AmbientTemperature'],
	[0x9401, 'Humidity'],
	[0x9402, 'Pressure'],
	[0x9403, 'WaterDepth'],
	[0x9404, 'Acceleration'],
	[0x9405, 'CameraElevationAngle'],
	[0xa000, 'FlashpixVersion'],
	[0xa001, 'ColorSpace'],
	[0xa002, 'ExifImageWidth'],
	[0xa003, 'ExifImageHeight'],
	[0xa004, 'RelatedSoundFile'],
	[0xa20b, 'FlashEnergy'],
	[0xa20e, 'FocalPlaneXResolution'],
	[0xa20f, 'FocalPlaneYResolution'],
	[0xa210, 'FocalPlaneResolutionUnit'],
	[0xa214, 'SubjectLocation'],
	[0xa215, 'ExposureIndex'],
	[0xa217, 'SensingMethod'],
	[0xa300, 'FileSource'],
	[0xa301, 'SceneType'],
	[0xa302, 'CFAPattern'],
	[0xa401, 'CustomRendered'],
	[0xa402, 'ExposureMode'],
	[0xa403, 'WhiteBalance'],
	[0xa404, 'DigitalZoomRatio'],
	[0xa405, 'FocalLengthIn35mmFormat'],
	[0xa406, 'SceneCaptureType'],
	[0xa407, 'GainControl'],
	[0xa408, 'Contrast'],
	[0xa409, 'Saturation'],
	[0xa40a, 'Sharpness'],
	[0xa40c, 'SubjectDistanceRange'],
	[0xa420, 'ImageUniqueID'],
	[0xa430, 'OwnerName'],
	[0xa431, 'SerialNumber'],
	[0xa432, 'LensInfo'],
	[0xa433, 'LensMake'],
	[0xa434, 'LensModel'],
	[0xa435, 'LensSerialNumber'],
	[0xa460, 'CompositeImage'],
	[0xa461, 'CompositeImageCount'],
	[0xa462, 'CompositeImageExposureTimes'],
	[0xa500, 'Gamma'],
	[0xea1c, 'Padding'],
	[0xea1d, 'OffsetSchema'],
	[0xfde8, 'OwnerName'],
	[0xfde9, 'SerialNumber'],
	[0xfdea, 'Lens'],
	[0xfe4c, 'RawFile'],
	[0xfe4d, 'Converter'],
	[0xfe4e, 'WhiteBalance'],
	[0xfe51, 'Exposure'],
	[0xfe52, 'Shadows'],
	[0xfe53, 'Brightness'],
	[0xfe54, 'Contrast'],
	[0xfe55, 'Saturation'],
	[0xfe56, 'Sharpness'],
	[0xfe57, 'Smoothness'],
	[0xfe58, 'MoireFilter'],
	// not actually assigned to IFD0 but offten found here
	[0xa005, 'InteropIFD'],
])