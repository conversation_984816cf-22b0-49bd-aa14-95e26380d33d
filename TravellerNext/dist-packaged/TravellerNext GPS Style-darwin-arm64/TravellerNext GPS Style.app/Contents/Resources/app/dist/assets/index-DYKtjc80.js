(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const g of document.querySelectorAll('link[rel="modulepreload"]'))h(g);new MutationObserver(g=>{for(const y of g)if(y.type==="childList")for(const x of y.addedNodes)x.tagName==="LINK"&&x.rel==="modulepreload"&&h(x)}).observe(document,{childList:!0,subtree:!0});function u(g){const y={};return g.integrity&&(y.integrity=g.integrity),g.referrerPolicy&&(y.referrerPolicy=g.referrerPolicy),g.crossOrigin==="use-credentials"?y.credentials="include":g.crossOrigin==="anonymous"?y.credentials="omit":y.credentials="same-origin",y}function h(g){if(g.ep)return;g.ep=!0;const y=u(g);fetch(g.href,y)}})();function Fm(p){return p&&p.__esModule&&Object.prototype.hasOwnProperty.call(p,"default")?p.default:p}var af={exports:{}},to={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bm;function Eg(){if(bm)return to;bm=1;var p=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(h,g,y){var x=null;if(y!==void 0&&(x=""+y),g.key!==void 0&&(x=""+g.key),"key"in g){y={};for(var N in g)N!=="key"&&(y[N]=g[N])}else y=g;return g=y.ref,{$$typeof:p,type:h,key:x,ref:g!==void 0?g:null,props:y}}return to.Fragment=r,to.jsx=u,to.jsxs=u,to}var Tm;function Mg(){return Tm||(Tm=1,af.exports=Eg()),af.exports}var O=Mg(),sf={exports:{}},bt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xm;function Og(){if(xm)return bt;xm=1;var p=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),h=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),y=Symbol.for("react.consumer"),x=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),E=Symbol.for("react.memo"),j=Symbol.for("react.lazy"),X=Symbol.iterator;function Y(S){return S===null||typeof S!="object"?null:(S=X&&S[X]||S["@@iterator"],typeof S=="function"?S:null)}var dt={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ht=Object.assign,st={};function xt(S,k,J){this.props=S,this.context=k,this.refs=st,this.updater=J||dt}xt.prototype.isReactComponent={},xt.prototype.setState=function(S,k){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,k,"setState")},xt.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function Kt(){}Kt.prototype=xt.prototype;function Gt(S,k,J){this.props=S,this.context=k,this.refs=st,this.updater=J||dt}var Tt=Gt.prototype=new Kt;Tt.constructor=Gt,ht(Tt,xt.prototype),Tt.isPureReactComponent=!0;var le=Array.isArray,St={H:null,A:null,T:null,S:null,V:null},ne=Object.prototype.hasOwnProperty;function oe(S,k,J,K,et,lt){return J=lt.ref,{$$typeof:p,type:S,key:k,ref:J!==void 0?J:null,props:lt}}function re(S,k){return oe(S.type,k,void 0,void 0,void 0,S.props)}function de(S){return typeof S=="object"&&S!==null&&S.$$typeof===p}function tt(S){var k={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(J){return k[J]})}var q=/\/+/g;function F(S,k){return typeof S=="object"&&S!==null&&S.key!=null?tt(""+S.key):k.toString(36)}function vt(){}function Mt(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(vt,vt):(S.status="pending",S.then(function(k){S.status==="pending"&&(S.status="fulfilled",S.value=k)},function(k){S.status==="pending"&&(S.status="rejected",S.reason=k)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Nt(S,k,J,K,et){var lt=typeof S;(lt==="undefined"||lt==="boolean")&&(S=null);var W=!1;if(S===null)W=!0;else switch(lt){case"bigint":case"string":case"number":W=!0;break;case"object":switch(S.$$typeof){case p:case r:W=!0;break;case j:return W=S._init,Nt(W(S._payload),k,J,K,et)}}if(W)return et=et(S),W=K===""?"."+F(S,0):K,le(et)?(J="",W!=null&&(J=W.replace(q,"$&/")+"/"),Nt(et,k,J,"",function(Ci){return Ci})):et!=null&&(de(et)&&(et=re(et,J+(et.key==null||S&&S.key===et.key?"":(""+et.key).replace(q,"$&/")+"/")+W)),k.push(et)),1;W=0;var Jt=K===""?".":K+":";if(le(S))for(var Rt=0;Rt<S.length;Rt++)K=S[Rt],lt=Jt+F(K,Rt),W+=Nt(K,k,J,lt,et);else if(Rt=Y(S),typeof Rt=="function")for(S=Rt.call(S),Rt=0;!(K=S.next()).done;)K=K.value,lt=Jt+F(K,Rt++),W+=Nt(K,k,J,lt,et);else if(lt==="object"){if(typeof S.then=="function")return Nt(Mt(S),k,J,K,et);throw k=String(S),Error("Objects are not valid as a React child (found: "+(k==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":k)+"). If you meant to render a collection of children, use an array instead.")}return W}function P(S,k,J){if(S==null)return S;var K=[],et=0;return Nt(S,K,"","",function(lt){return k.call(J,lt,et++)}),K}function Q(S){if(S._status===-1){var k=S._result;k=k(),k.then(function(J){(S._status===0||S._status===-1)&&(S._status=1,S._result=J)},function(J){(S._status===0||S._status===-1)&&(S._status=2,S._result=J)}),S._status===-1&&(S._status=0,S._result=k)}if(S._status===1)return S._result.default;throw S._result}var V=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var k=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(k))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function Ot(){}return bt.Children={map:P,forEach:function(S,k,J){P(S,function(){k.apply(this,arguments)},J)},count:function(S){var k=0;return P(S,function(){k++}),k},toArray:function(S){return P(S,function(k){return k})||[]},only:function(S){if(!de(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},bt.Component=xt,bt.Fragment=u,bt.Profiler=g,bt.PureComponent=Gt,bt.StrictMode=h,bt.Suspense=A,bt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=St,bt.__COMPILER_RUNTIME={__proto__:null,c:function(S){return St.H.useMemoCache(S)}},bt.cache=function(S){return function(){return S.apply(null,arguments)}},bt.cloneElement=function(S,k,J){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var K=ht({},S.props),et=S.key,lt=void 0;if(k!=null)for(W in k.ref!==void 0&&(lt=void 0),k.key!==void 0&&(et=""+k.key),k)!ne.call(k,W)||W==="key"||W==="__self"||W==="__source"||W==="ref"&&k.ref===void 0||(K[W]=k[W]);var W=arguments.length-2;if(W===1)K.children=J;else if(1<W){for(var Jt=Array(W),Rt=0;Rt<W;Rt++)Jt[Rt]=arguments[Rt+2];K.children=Jt}return oe(S.type,et,void 0,void 0,lt,K)},bt.createContext=function(S){return S={$$typeof:x,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:y,_context:S},S},bt.createElement=function(S,k,J){var K,et={},lt=null;if(k!=null)for(K in k.key!==void 0&&(lt=""+k.key),k)ne.call(k,K)&&K!=="key"&&K!=="__self"&&K!=="__source"&&(et[K]=k[K]);var W=arguments.length-2;if(W===1)et.children=J;else if(1<W){for(var Jt=Array(W),Rt=0;Rt<W;Rt++)Jt[Rt]=arguments[Rt+2];et.children=Jt}if(S&&S.defaultProps)for(K in W=S.defaultProps,W)et[K]===void 0&&(et[K]=W[K]);return oe(S,lt,void 0,void 0,null,et)},bt.createRef=function(){return{current:null}},bt.forwardRef=function(S){return{$$typeof:N,render:S}},bt.isValidElement=de,bt.lazy=function(S){return{$$typeof:j,_payload:{_status:-1,_result:S},_init:Q}},bt.memo=function(S,k){return{$$typeof:E,type:S,compare:k===void 0?null:k}},bt.startTransition=function(S){var k=St.T,J={};St.T=J;try{var K=S(),et=St.S;et!==null&&et(J,K),typeof K=="object"&&K!==null&&typeof K.then=="function"&&K.then(Ot,V)}catch(lt){V(lt)}finally{St.T=k}},bt.unstable_useCacheRefresh=function(){return St.H.useCacheRefresh()},bt.use=function(S){return St.H.use(S)},bt.useActionState=function(S,k,J){return St.H.useActionState(S,k,J)},bt.useCallback=function(S,k){return St.H.useCallback(S,k)},bt.useContext=function(S){return St.H.useContext(S)},bt.useDebugValue=function(){},bt.useDeferredValue=function(S,k){return St.H.useDeferredValue(S,k)},bt.useEffect=function(S,k,J){var K=St.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return K.useEffect(S,k)},bt.useId=function(){return St.H.useId()},bt.useImperativeHandle=function(S,k,J){return St.H.useImperativeHandle(S,k,J)},bt.useInsertionEffect=function(S,k){return St.H.useInsertionEffect(S,k)},bt.useLayoutEffect=function(S,k){return St.H.useLayoutEffect(S,k)},bt.useMemo=function(S,k){return St.H.useMemo(S,k)},bt.useOptimistic=function(S,k){return St.H.useOptimistic(S,k)},bt.useReducer=function(S,k,J){return St.H.useReducer(S,k,J)},bt.useRef=function(S){return St.H.useRef(S)},bt.useState=function(S){return St.H.useState(S)},bt.useSyncExternalStore=function(S,k,J){return St.H.useSyncExternalStore(S,k,J)},bt.useTransition=function(){return St.H.useTransition()},bt.version="19.1.1",bt}var Cm;function wf(){return Cm||(Cm=1,sf.exports=Og()),sf.exports}var it=wf();const Qr=Fm(it);var lf={exports:{}},eo={},of={exports:{}},rf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wm;function Dg(){return wm||(wm=1,function(p){function r(P,Q){var V=P.length;P.push(Q);t:for(;0<V;){var Ot=V-1>>>1,S=P[Ot];if(0<g(S,Q))P[Ot]=Q,P[V]=S,V=Ot;else break t}}function u(P){return P.length===0?null:P[0]}function h(P){if(P.length===0)return null;var Q=P[0],V=P.pop();if(V!==Q){P[0]=V;t:for(var Ot=0,S=P.length,k=S>>>1;Ot<k;){var J=2*(Ot+1)-1,K=P[J],et=J+1,lt=P[et];if(0>g(K,V))et<S&&0>g(lt,K)?(P[Ot]=lt,P[et]=V,Ot=et):(P[Ot]=K,P[J]=V,Ot=J);else if(et<S&&0>g(lt,V))P[Ot]=lt,P[et]=V,Ot=et;else break t}}return Q}function g(P,Q){var V=P.sortIndex-Q.sortIndex;return V!==0?V:P.id-Q.id}if(p.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var y=performance;p.unstable_now=function(){return y.now()}}else{var x=Date,N=x.now();p.unstable_now=function(){return x.now()-N}}var A=[],E=[],j=1,X=null,Y=3,dt=!1,ht=!1,st=!1,xt=!1,Kt=typeof setTimeout=="function"?setTimeout:null,Gt=typeof clearTimeout=="function"?clearTimeout:null,Tt=typeof setImmediate<"u"?setImmediate:null;function le(P){for(var Q=u(E);Q!==null;){if(Q.callback===null)h(E);else if(Q.startTime<=P)h(E),Q.sortIndex=Q.expirationTime,r(A,Q);else break;Q=u(E)}}function St(P){if(st=!1,le(P),!ht)if(u(A)!==null)ht=!0,ne||(ne=!0,F());else{var Q=u(E);Q!==null&&Nt(St,Q.startTime-P)}}var ne=!1,oe=-1,re=5,de=-1;function tt(){return xt?!0:!(p.unstable_now()-de<re)}function q(){if(xt=!1,ne){var P=p.unstable_now();de=P;var Q=!0;try{t:{ht=!1,st&&(st=!1,Gt(oe),oe=-1),dt=!0;var V=Y;try{e:{for(le(P),X=u(A);X!==null&&!(X.expirationTime>P&&tt());){var Ot=X.callback;if(typeof Ot=="function"){X.callback=null,Y=X.priorityLevel;var S=Ot(X.expirationTime<=P);if(P=p.unstable_now(),typeof S=="function"){X.callback=S,le(P),Q=!0;break e}X===u(A)&&h(A),le(P)}else h(A);X=u(A)}if(X!==null)Q=!0;else{var k=u(E);k!==null&&Nt(St,k.startTime-P),Q=!1}}break t}finally{X=null,Y=V,dt=!1}Q=void 0}}finally{Q?F():ne=!1}}}var F;if(typeof Tt=="function")F=function(){Tt(q)};else if(typeof MessageChannel<"u"){var vt=new MessageChannel,Mt=vt.port2;vt.port1.onmessage=q,F=function(){Mt.postMessage(null)}}else F=function(){Kt(q,0)};function Nt(P,Q){oe=Kt(function(){P(p.unstable_now())},Q)}p.unstable_IdlePriority=5,p.unstable_ImmediatePriority=1,p.unstable_LowPriority=4,p.unstable_NormalPriority=3,p.unstable_Profiling=null,p.unstable_UserBlockingPriority=2,p.unstable_cancelCallback=function(P){P.callback=null},p.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):re=0<P?Math.floor(1e3/P):5},p.unstable_getCurrentPriorityLevel=function(){return Y},p.unstable_next=function(P){switch(Y){case 1:case 2:case 3:var Q=3;break;default:Q=Y}var V=Y;Y=Q;try{return P()}finally{Y=V}},p.unstable_requestPaint=function(){xt=!0},p.unstable_runWithPriority=function(P,Q){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var V=Y;Y=P;try{return Q()}finally{Y=V}},p.unstable_scheduleCallback=function(P,Q,V){var Ot=p.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?Ot+V:Ot):V=Ot,P){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=V+S,P={id:j++,callback:Q,priorityLevel:P,startTime:V,expirationTime:S,sortIndex:-1},V>Ot?(P.sortIndex=V,r(E,P),u(A)===null&&P===u(E)&&(st?(Gt(oe),oe=-1):st=!0,Nt(St,V-Ot))):(P.sortIndex=S,r(A,P),ht||dt||(ht=!0,ne||(ne=!0,F()))),P},p.unstable_shouldYield=tt,p.unstable_wrapCallback=function(P){var Q=Y;return function(){var V=Y;Y=Q;try{return P.apply(this,arguments)}finally{Y=V}}}}(rf)),rf}var Lm;function zg(){return Lm||(Lm=1,of.exports=Dg()),of.exports}var uf={exports:{}},Pe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Am;function Ng(){if(Am)return Pe;Am=1;var p=wf();function r(A){var E="https://react.dev/errors/"+A;if(1<arguments.length){E+="?args[]="+encodeURIComponent(arguments[1]);for(var j=2;j<arguments.length;j++)E+="&args[]="+encodeURIComponent(arguments[j])}return"Minified React error #"+A+"; visit "+E+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var h={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},g=Symbol.for("react.portal");function y(A,E,j){var X=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:g,key:X==null?null:""+X,children:A,containerInfo:E,implementation:j}}var x=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function N(A,E){if(A==="font")return"";if(typeof E=="string")return E==="use-credentials"?E:""}return Pe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=h,Pe.createPortal=function(A,E){var j=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!E||E.nodeType!==1&&E.nodeType!==9&&E.nodeType!==11)throw Error(r(299));return y(A,E,null,j)},Pe.flushSync=function(A){var E=x.T,j=h.p;try{if(x.T=null,h.p=2,A)return A()}finally{x.T=E,h.p=j,h.d.f()}},Pe.preconnect=function(A,E){typeof A=="string"&&(E?(E=E.crossOrigin,E=typeof E=="string"?E==="use-credentials"?E:"":void 0):E=null,h.d.C(A,E))},Pe.prefetchDNS=function(A){typeof A=="string"&&h.d.D(A)},Pe.preinit=function(A,E){if(typeof A=="string"&&E&&typeof E.as=="string"){var j=E.as,X=N(j,E.crossOrigin),Y=typeof E.integrity=="string"?E.integrity:void 0,dt=typeof E.fetchPriority=="string"?E.fetchPriority:void 0;j==="style"?h.d.S(A,typeof E.precedence=="string"?E.precedence:void 0,{crossOrigin:X,integrity:Y,fetchPriority:dt}):j==="script"&&h.d.X(A,{crossOrigin:X,integrity:Y,fetchPriority:dt,nonce:typeof E.nonce=="string"?E.nonce:void 0})}},Pe.preinitModule=function(A,E){if(typeof A=="string")if(typeof E=="object"&&E!==null){if(E.as==null||E.as==="script"){var j=N(E.as,E.crossOrigin);h.d.M(A,{crossOrigin:j,integrity:typeof E.integrity=="string"?E.integrity:void 0,nonce:typeof E.nonce=="string"?E.nonce:void 0})}}else E==null&&h.d.M(A)},Pe.preload=function(A,E){if(typeof A=="string"&&typeof E=="object"&&E!==null&&typeof E.as=="string"){var j=E.as,X=N(j,E.crossOrigin);h.d.L(A,j,{crossOrigin:X,integrity:typeof E.integrity=="string"?E.integrity:void 0,nonce:typeof E.nonce=="string"?E.nonce:void 0,type:typeof E.type=="string"?E.type:void 0,fetchPriority:typeof E.fetchPriority=="string"?E.fetchPriority:void 0,referrerPolicy:typeof E.referrerPolicy=="string"?E.referrerPolicy:void 0,imageSrcSet:typeof E.imageSrcSet=="string"?E.imageSrcSet:void 0,imageSizes:typeof E.imageSizes=="string"?E.imageSizes:void 0,media:typeof E.media=="string"?E.media:void 0})}},Pe.preloadModule=function(A,E){if(typeof A=="string")if(E){var j=N(E.as,E.crossOrigin);h.d.m(A,{as:typeof E.as=="string"&&E.as!=="script"?E.as:void 0,crossOrigin:j,integrity:typeof E.integrity=="string"?E.integrity:void 0})}else h.d.m(A)},Pe.requestFormReset=function(A){h.d.r(A)},Pe.unstable_batchedUpdates=function(A,E){return A(E)},Pe.useFormState=function(A,E,j){return x.H.useFormState(A,E,j)},Pe.useFormStatus=function(){return x.H.useHostTransitionStatus()},Pe.version="19.1.1",Pe}var Em;function Jm(){if(Em)return uf.exports;Em=1;function p(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(p)}catch(r){console.error(r)}}return p(),uf.exports=Ng(),uf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mm;function Rg(){if(Mm)return eo;Mm=1;var p=zg(),r=wf(),u=Jm();function h(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function g(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function y(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function x(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function N(t){if(y(t)!==t)throw Error(h(188))}function A(t){var e=t.alternate;if(!e){if(e=y(t),e===null)throw Error(h(188));return e!==t?null:t}for(var n=t,s=e;;){var o=n.return;if(o===null)break;var c=o.alternate;if(c===null){if(s=o.return,s!==null){n=s;continue}break}if(o.child===c.child){for(c=o.child;c;){if(c===n)return N(o),t;if(c===s)return N(o),e;c=c.sibling}throw Error(h(188))}if(n.return!==s.return)n=o,s=c;else{for(var m=!1,v=o.child;v;){if(v===n){m=!0,n=o,s=c;break}if(v===s){m=!0,s=o,n=c;break}v=v.sibling}if(!m){for(v=c.child;v;){if(v===n){m=!0,n=c,s=o;break}if(v===s){m=!0,s=c,n=o;break}v=v.sibling}if(!m)throw Error(h(189))}}if(n.alternate!==s)throw Error(h(190))}if(n.tag!==3)throw Error(h(188));return n.stateNode.current===n?t:e}function E(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=E(t),e!==null)return e;t=t.sibling}return null}var j=Object.assign,X=Symbol.for("react.element"),Y=Symbol.for("react.transitional.element"),dt=Symbol.for("react.portal"),ht=Symbol.for("react.fragment"),st=Symbol.for("react.strict_mode"),xt=Symbol.for("react.profiler"),Kt=Symbol.for("react.provider"),Gt=Symbol.for("react.consumer"),Tt=Symbol.for("react.context"),le=Symbol.for("react.forward_ref"),St=Symbol.for("react.suspense"),ne=Symbol.for("react.suspense_list"),oe=Symbol.for("react.memo"),re=Symbol.for("react.lazy"),de=Symbol.for("react.activity"),tt=Symbol.for("react.memo_cache_sentinel"),q=Symbol.iterator;function F(t){return t===null||typeof t!="object"?null:(t=q&&t[q]||t["@@iterator"],typeof t=="function"?t:null)}var vt=Symbol.for("react.client.reference");function Mt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===vt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case ht:return"Fragment";case xt:return"Profiler";case st:return"StrictMode";case St:return"Suspense";case ne:return"SuspenseList";case de:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case dt:return"Portal";case Tt:return(t.displayName||"Context")+".Provider";case Gt:return(t._context.displayName||"Context")+".Consumer";case le:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case oe:return e=t.displayName||null,e!==null?e:Mt(t.type)||"Memo";case re:e=t._payload,t=t._init;try{return Mt(t(e))}catch{}}return null}var Nt=Array.isArray,P=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V={pending:!1,data:null,method:null,action:null},Ot=[],S=-1;function k(t){return{current:t}}function J(t){0>S||(t.current=Ot[S],Ot[S]=null,S--)}function K(t,e){S++,Ot[S]=t.current,t.current=e}var et=k(null),lt=k(null),W=k(null),Jt=k(null);function Rt(t,e){switch(K(W,e),K(lt,t),K(et,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Qd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Qd(e),t=Fd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}J(et),K(et,t)}function Ci(){J(et),J(lt),J(W)}function Ta(t){t.memoizedState!==null&&K(Jt,t);var e=et.current,n=Fd(e,t.type);e!==n&&(K(lt,t),K(et,n))}function En(t){lt.current===t&&(J(et),J(lt)),Jt.current===t&&(J(Jt),Ql._currentValue=V)}var Fi=Object.prototype.hasOwnProperty,xa=p.unstable_scheduleCallback,Fs=p.unstable_cancelCallback,ro=p.unstable_shouldYield,uo=p.unstable_requestPaint,Ve=p.unstable_now,is=p.unstable_getCurrentPriorityLevel,co=p.unstable_ImmediatePriority,Js=p.unstable_UserBlockingPriority,Mn=p.unstable_NormalPriority,fo=p.unstable_LowPriority,Ws=p.unstable_IdlePriority,ru=p.log,uu=p.unstable_setDisableYieldValue,Ji=null,Oe=null;function wi(t){if(typeof ru=="function"&&uu(t),Oe&&typeof Oe.setStrictMode=="function")try{Oe.setStrictMode(Ji,t)}catch{}}var Ue=Math.clz32?Math.clz32:cu,ho=Math.log,mo=Math.LN2;function cu(t){return t>>>=0,t===0?32:31-(ho(t)/mo|0)|0}var Ca=256,On=4194304;function Bi(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function ns(t,e,n){var s=t.pendingLanes;if(s===0)return 0;var o=0,c=t.suspendedLanes,m=t.pingedLanes;t=t.warmLanes;var v=s&134217727;return v!==0?(s=v&~c,s!==0?o=Bi(s):(m&=v,m!==0?o=Bi(m):n||(n=v&~t,n!==0&&(o=Bi(n))))):(v=s&~c,v!==0?o=Bi(v):m!==0?o=Bi(m):n||(n=s&~t,n!==0&&(o=Bi(n)))),o===0?0:e!==0&&e!==o&&(e&c)===0&&(c=o&-o,n=e&-e,c>=n||c===32&&(n&4194048)!==0)?e:o}function Li(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function fu(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function po(){var t=Ca;return Ca<<=1,(Ca&4194048)===0&&(Ca=256),t}function $s(){var t=On;return On<<=1,(On&62914560)===0&&(On=4194304),t}function as(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Dn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function hu(t,e,n,s,o,c){var m=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var v=t.entanglements,T=t.expirationTimes,z=t.hiddenUpdates;for(n=m&~n;0<n;){var Z=31-Ue(n),G=1<<Z;v[Z]=0,T[Z]=-1;var R=z[Z];if(R!==null)for(z[Z]=null,Z=0;Z<R.length;Z++){var B=R[Z];B!==null&&(B.lane&=-536870913)}n&=~G}s!==0&&go(t,s,0),c!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=c&~(m&~e))}function go(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var s=31-Ue(e);t.entangledLanes|=e,t.entanglements[s]=t.entanglements[s]|1073741824|n&4194090}function _o(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var s=31-Ue(n),o=1<<s;o&e|t[s]&e&&(t[s]|=e),n&=~o}}function tl(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function el(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function vo(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:pm(t.type))}function il(t,e){var n=Q.p;try{return Q.p=t,e()}finally{Q.p=n}}var Ui=Math.random().toString(36).slice(2),ge="__reactFiber$"+Ui,De="__reactProps$"+Ui,zn="__reactContainer$"+Ui,Ye="__reactEvents$"+Ui,rt="__reactListeners$"+Ui,yo="__reactHandles$"+Ui,nl="__reactResources$"+Ui,Nn="__reactMarker$"+Ui;function ss(t){delete t[ge],delete t[De],delete t[Ye],delete t[rt],delete t[yo]}function ki(t){var e=t[ge];if(e)return e;for(var n=t.parentNode;n;){if(e=n[zn]||n[ge]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=tm(t);t!==null;){if(n=t[ge])return n;t=tm(t)}return e}t=n,n=t.parentNode}return null}function Wi(t){if(t=t[ge]||t[zn]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function di(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(h(33))}function $i(t){var e=t[nl];return e||(e=t[nl]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ue(t){t[Nn]=!0}var So=new Set,bo={};function tn(t,e){en(t,e),en(t+"Capture",e)}function en(t,e){for(bo[t]=e,t=0;t<e.length;t++)So.add(e[t])}var du=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),wa={},To={};function mu(t){return Fi.call(To,t)?!0:Fi.call(wa,t)?!1:du.test(t)?To[t]=!0:(wa[t]=!0,!1)}function ls(t,e,n){if(mu(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var s=e.toLowerCase().slice(0,5);if(s!=="data-"&&s!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function os(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Ai(t,e,n,s){if(s===null)t.removeAttribute(n);else{switch(typeof s){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+s)}}var La,Rn;function nn(t){if(La===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);La=e&&e[1]||"",Rn=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+La+t+Rn}var rs=!1;function an(t,e){if(!t||rs)return"";rs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var s={DetermineComponentFrameRoot:function(){try{if(e){var G=function(){throw Error()};if(Object.defineProperty(G.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(G,[])}catch(B){var R=B}Reflect.construct(t,[],G)}else{try{G.call()}catch(B){R=B}t.call(G.prototype)}}else{try{throw Error()}catch(B){R=B}(G=t())&&typeof G.catch=="function"&&G.catch(function(){})}}catch(B){if(B&&R&&typeof B.stack=="string")return[B.stack,R.stack]}return[null,null]}};s.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(s.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(s.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=s.DetermineComponentFrameRoot(),m=c[0],v=c[1];if(m&&v){var T=m.split(`
`),z=v.split(`
`);for(o=s=0;s<T.length&&!T[s].includes("DetermineComponentFrameRoot");)s++;for(;o<z.length&&!z[o].includes("DetermineComponentFrameRoot");)o++;if(s===T.length||o===z.length)for(s=T.length-1,o=z.length-1;1<=s&&0<=o&&T[s]!==z[o];)o--;for(;1<=s&&0<=o;s--,o--)if(T[s]!==z[o]){if(s!==1||o!==1)do if(s--,o--,0>o||T[s]!==z[o]){var Z=`
`+T[s].replace(" at new "," at ");return t.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",t.displayName)),Z}while(1<=s&&0<=o);break}}}finally{rs=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?nn(n):""}function Dt(t){switch(t.tag){case 26:case 27:case 5:return nn(t.type);case 16:return nn("Lazy");case 13:return nn("Suspense");case 19:return nn("SuspenseList");case 0:case 15:return an(t.type,!1);case 11:return an(t.type.render,!1);case 1:return an(t.type,!0);case 31:return nn("Activity");default:return""}}function qt(t){try{var e="";do e+=Dt(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function xe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function sn(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Pn(t){var e=sn(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),s=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,c=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(m){s=""+m,c.call(this,m)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(m){s=""+m},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Bn(t){t._valueTracker||(t._valueTracker=Pn(t))}function yt(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),s="";return t&&(s=sn(t)?t.checked?"true":"false":t.value),t=s,t!==n?(e.setValue(t),!0):!1}function Vt(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var al=/[\n"\\]/g;function Ce(t){return t.replace(al,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function ze(t,e,n,s,o,c,m,v){t.name="",m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?t.type=m:t.removeAttribute("type"),e!=null?m==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+xe(e)):t.value!==""+xe(e)&&(t.value=""+xe(e)):m!=="submit"&&m!=="reset"||t.removeAttribute("value"),e!=null?Un(t,m,xe(e)):n!=null?Un(t,m,xe(n)):s!=null&&t.removeAttribute("value"),o==null&&c!=null&&(t.defaultChecked=!!c),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?t.name=""+xe(v):t.removeAttribute("name")}function xo(t,e,n,s,o,c,m,v){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;n=n!=null?""+xe(n):"",e=e!=null?""+xe(e):n,v||e===t.value||(t.value=e),t.defaultValue=e}s=s??o,s=typeof s!="function"&&typeof s!="symbol"&&!!s,t.checked=v?t.checked:!!s,t.defaultChecked=!!s,m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"&&(t.name=m)}function Un(t,e,n){e==="number"&&Vt(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function Xe(t,e,n,s){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&s&&(t[n].defaultSelected=!0)}else{for(n=""+xe(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,s&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Wt(t,e,n){if(e!=null&&(e=""+xe(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+xe(n):""}function Zi(t,e,n,s){if(e==null){if(s!=null){if(n!=null)throw Error(h(92));if(Nt(s)){if(1<s.length)throw Error(h(93));s=s[0]}n=s}n==null&&(n=""),e=n}n=xe(e),t.defaultValue=n,s=t.textContent,s===n&&s!==""&&s!==null&&(t.value=s)}function mi(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Aa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function us(t,e,n){var s=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?s?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":s?t.setProperty(e,n):typeof n!="number"||n===0||Aa.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function kn(t,e,n){if(e!=null&&typeof e!="object")throw Error(h(62));if(t=t.style,n!=null){for(var s in n)!n.hasOwnProperty(s)||e!=null&&e.hasOwnProperty(s)||(s.indexOf("--")===0?t.setProperty(s,""):s==="float"?t.cssFloat="":t[s]="");for(var o in e)s=e[o],e.hasOwnProperty(o)&&n[o]!==s&&us(t,o,s)}else for(var c in e)e.hasOwnProperty(c)&&us(t,c,e[c])}function Ea(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var sl=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),cs=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Zn(t){return cs.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ma=null;function Hn(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ln=null,Hi=null;function Co(t){var e=Wi(t);if(e&&(t=e.stateNode)){var n=t[De]||null;t:switch(t=e.stateNode,e.type){case"input":if(ze(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ce(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var s=n[e];if(s!==t&&s.form===t.form){var o=s[De]||null;if(!o)throw Error(h(90));ze(s,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<n.length;e++)s=n[e],s.form===t.form&&yt(s)}break t;case"textarea":Wt(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&Xe(t,!!n.multiple,e,!1)}}}var _t=!1;function ti(t,e,n){if(_t)return t(e,n);_t=!0;try{var s=t(e);return s}finally{if(_t=!1,(ln!==null||Hi!==null)&&(vr(),ln&&(e=ln,t=Hi,Hi=ln=null,Co(e),t)))for(e=0;e<t.length;e++)Co(t[e])}}function Pt(t,e){var n=t.stateNode;if(n===null)return null;var s=n[De]||null;if(s===null)return null;n=s[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(t=t.type,s=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!s;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(h(231,e,typeof n));return n}var pi=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Oa=!1;if(pi)try{var on={};Object.defineProperty(on,"passive",{get:function(){Oa=!0}}),window.addEventListener("test",on,on),window.removeEventListener("test",on,on)}catch{Oa=!1}var gi=null,Ei=null,jn=null;function Gn(){if(jn)return jn;var t,e=Ei,n=e.length,s,o="value"in gi?gi.value:gi.textContent,c=o.length;for(t=0;t<n&&e[t]===o[t];t++);var m=n-t;for(s=1;s<=m&&e[n-s]===o[c-s];s++);return jn=o.slice(t,1<s?1-s:void 0)}function ee(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function _i(){return!0}function ll(){return!1}function we(t){function e(n,s,o,c,m){this._reactName=n,this._targetInst=o,this.type=s,this.nativeEvent=c,this.target=m,this.currentTarget=null;for(var v in t)t.hasOwnProperty(v)&&(n=t[v],this[v]=n?n(c):c[v]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?_i:ll,this.isPropagationStopped=ll,this}return j(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=_i)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=_i)},persist:function(){},isPersistent:_i}),e}var rn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Da=we(rn),un=j({},rn,{view:0,detail:0}),pu=we(un),fs,wt,za,Ne=j({},un,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hs,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==za&&(za&&t.type==="mousemove"?(fs=t.screenX-za.screenX,wt=t.screenY-za.screenY):wt=fs=0,za=t),fs)},movementY:function(t){return"movementY"in t?t.movementY:wt}}),In=we(Ne),wo=j({},Ne,{dataTransfer:0}),gu=we(wo),ol=j({},un,{relatedTarget:0}),rl=we(ol),Lo=j({},rn,{animationName:0,elapsedTime:0,pseudoElement:0}),_u=we(Lo),vu=j({},rn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),ul=we(vu),yu=j({},rn,{data:0}),ei=we(yu),Su={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ao={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ji={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Eo(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=ji[t])?!!e[t]:!1}function hs(){return Eo}var cl=j({},un,{key:function(t){if(t.key){var e=Su[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ee(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Ao[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hs,charCode:function(t){return t.type==="keypress"?ee(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ee(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),bu=we(cl),Mo=j({},Ne,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),fl=we(Mo),Tu=j({},un,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hs}),xu=we(Tu),hl=j({},rn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Cu=we(hl),Oo=j({},Ne,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Do=we(Oo),ds=j({},rn,{newState:0,oldState:0}),cn=we(ds),wu=[9,13,27,32],fn=pi&&"CompositionEvent"in window,_e=null;pi&&"documentMode"in document&&(_e=document.documentMode);var zo=pi&&"TextEvent"in window&&!_e,dl=pi&&(!fn||_e&&8<_e&&11>=_e),No=" ",ms=!1;function ps(t,e){switch(t){case"keyup":return wu.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ro(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var qn=!1;function Po(t,e){switch(t){case"compositionend":return Ro(e);case"keypress":return e.which!==32?null:(ms=!0,No);case"textInput":return t=e.data,t===No&&ms?null:t;default:return null}}function Lu(t,e){if(qn)return t==="compositionend"||!fn&&ps(t,e)?(t=Gn(),jn=Ei=gi=null,qn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return dl&&e.locale!=="ko"?null:e.data;default:return null}}var ii={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hn(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!ii[t.type]:e==="textarea"}function Bo(t,e,n,s){ln?Hi?Hi.push(s):Hi=[s]:ln=s,e=Cr(e,"onChange"),0<e.length&&(n=new Da("onChange","change",null,n,s),t.push({event:n,listeners:e}))}var ke=null,Na=null;function Vn(t){qd(t,0)}function gs(t){var e=di(t);if(yt(e))return t}function Yn(t,e){if(t==="change")return e}var ml=!1;if(pi){var Xn;if(pi){var pl="oninput"in document;if(!pl){var Mi=document.createElement("div");Mi.setAttribute("oninput","return;"),pl=typeof Mi.oninput=="function"}Xn=pl}else Xn=!1;ml=Xn&&(!document.documentMode||9<document.documentMode)}function Ra(){ke&&(ke.detachEvent("onpropertychange",Uo),Na=ke=null)}function Uo(t){if(t.propertyName==="value"&&gs(Na)){var e=[];Bo(e,Na,t,Hn(t)),ti(Vn,e)}}function gl(t,e,n){t==="focusin"?(Ra(),ke=e,Na=n,ke.attachEvent("onpropertychange",Uo)):t==="focusout"&&Ra()}function Au(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return gs(Na)}function Oi(t,e){if(t==="click")return gs(e)}function Eu(t,e){if(t==="input"||t==="change")return gs(e)}function Kn(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ze=typeof Object.is=="function"?Object.is:Kn;function He(t,e){if(Ze(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),s=Object.keys(e);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var o=n[s];if(!Fi.call(e,o)||!Ze(t[o],e[o]))return!1}return!0}function Pa(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function _l(t,e){var n=Pa(t);t=0;for(var s;n;){if(n.nodeType===3){if(s=t+n.textContent.length,t<=e&&s>=e)return{node:n,offset:e-t};t=s}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Pa(n)}}function _s(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?_s(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Ba(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Vt(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Vt(t.document)}return e}function Ua(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var vs=pi&&"documentMode"in document&&11>=document.documentMode,ni=null,Qn=null,dn=null,ys=!1;function ko(t,e,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ys||ni==null||ni!==Vt(s)||(s=ni,"selectionStart"in s&&Ua(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),dn&&He(dn,s)||(dn=s,s=Cr(Qn,"onSelect"),0<s.length&&(e=new Da("onSelect","select",null,e,n),t.push({event:e,listeners:s}),e.target=ni)))}function vi(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Fn={animationend:vi("Animation","AnimationEnd"),animationiteration:vi("Animation","AnimationIteration"),animationstart:vi("Animation","AnimationStart"),transitionrun:vi("Transition","TransitionRun"),transitionstart:vi("Transition","TransitionStart"),transitioncancel:vi("Transition","TransitionCancel"),transitionend:vi("Transition","TransitionEnd")},Ss={},Zo={};pi&&(Zo=document.createElement("div").style,"AnimationEvent"in window||(delete Fn.animationend.animation,delete Fn.animationiteration.animation,delete Fn.animationstart.animation),"TransitionEvent"in window||delete Fn.transitionend.transition);function Gi(t){if(Ss[t])return Ss[t];if(!Fn[t])return t;var e=Fn[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Zo)return Ss[t]=e[n];return t}var Ho=Gi("animationend"),ai=Gi("animationiteration"),ka=Gi("animationstart"),Mu=Gi("transitionrun"),bs=Gi("transitionstart"),Ou=Gi("transitioncancel"),vl=Gi("transitionend"),jo=new Map,mn="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");mn.push("scrollEnd");function si(t,e){jo.set(t,e),tn(e,[t])}var pn=new WeakMap;function je(t,e){if(typeof t=="object"&&t!==null){var n=pn.get(t);return n!==void 0?n:(e={value:t,source:e,stack:qt(e)},pn.set(t,e),e)}return{value:t,source:e,stack:qt(e)}}var Ge=[],Jn=0,li=0;function Za(){for(var t=Jn,e=li=Jn=0;e<t;){var n=Ge[e];Ge[e++]=null;var s=Ge[e];Ge[e++]=null;var o=Ge[e];Ge[e++]=null;var c=Ge[e];if(Ge[e++]=null,s!==null&&o!==null){var m=s.pending;m===null?o.next=o:(o.next=m.next,m.next=o),s.pending=o}c!==0&&ja(n,o,c)}}function Ha(t,e,n,s){Ge[Jn++]=t,Ge[Jn++]=e,Ge[Jn++]=n,Ge[Jn++]=s,li|=s,t.lanes|=s,t=t.alternate,t!==null&&(t.lanes|=s)}function gn(t,e,n,s){return Ha(t,e,n,s),Ii(t)}function Wn(t,e){return Ha(t,null,null,e),Ii(t)}function ja(t,e,n){t.lanes|=n;var s=t.alternate;s!==null&&(s.lanes|=n);for(var o=!1,c=t.return;c!==null;)c.childLanes|=n,s=c.alternate,s!==null&&(s.childLanes|=n),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(o=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,o&&e!==null&&(o=31-Ue(n),t=c.hiddenUpdates,s=t[o],s===null?t[o]=[e]:s.push(e),e.lane=n|536870912),c):null}function Ii(t){if(50<jl)throw jl=0,Ac=null,Error(h(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var _n={};function Go(t,e,n,s){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ie(t,e,n,s){return new Go(t,e,n,s)}function Ts(t){return t=t.prototype,!(!t||!t.isReactComponent)}function yi(t,e){var n=t.alternate;return n===null?(n=Ie(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function yl(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ga(t,e,n,s,o,c){var m=0;if(s=t,typeof t=="function")Ts(t)&&(m=1);else if(typeof t=="string")m=pg(t,n,et.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case de:return t=Ie(31,n,e,o),t.elementType=de,t.lanes=c,t;case ht:return qi(n.children,o,c,e);case st:m=8,o|=24;break;case xt:return t=Ie(12,n,e,o|2),t.elementType=xt,t.lanes=c,t;case St:return t=Ie(13,n,e,o),t.elementType=St,t.lanes=c,t;case ne:return t=Ie(19,n,e,o),t.elementType=ne,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Kt:case Tt:m=10;break t;case Gt:m=9;break t;case le:m=11;break t;case oe:m=14;break t;case re:m=16,s=null;break t}m=29,n=Error(h(130,t===null?"null":typeof t,"")),s=null}return e=Ie(m,n,e,o),e.elementType=t,e.type=s,e.lanes=c,e}function qi(t,e,n,s){return t=Ie(7,t,s,e),t.lanes=n,t}function Sl(t,e,n){return t=Ie(6,t,null,e),t.lanes=n,t}function xs(t,e,n){return e=Ie(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var vn=[],$n=0,i=null,a=0,l=[],f=0,d=null,_=1,b="";function M(t,e){vn[$n++]=a,vn[$n++]=i,i=t,a=e}function U(t,e,n){l[f++]=_,l[f++]=b,l[f++]=d,d=t;var s=_;t=b;var o=32-Ue(s)-1;s&=~(1<<o),n+=1;var c=32-Ue(e)+o;if(30<c){var m=o-o%5;c=(s&(1<<m)-1).toString(32),s>>=m,o-=m,_=1<<32-Ue(e)+o|n<<o|s,b=c+t}else _=1<<c|n<<o|s,b=t}function I(t){t.return!==null&&(M(t,1),U(t,1,0))}function $(t){for(;t===i;)i=vn[--$n],vn[$n]=null,a=vn[--$n],vn[$n]=null;for(;t===d;)d=l[--f],l[f]=null,b=l[--f],l[f]=null,_=l[--f],l[f]=null}var nt=null,ot=null,gt=!1,Yt=null,$t=!1,ve=Error(h(519));function Ke(t){var e=Error(h(418,""));throw ea(je(e,t)),ve}function Io(t){var e=t.stateNode,n=t.type,s=t.memoizedProps;switch(e[ge]=t,e[De]=s,n){case"dialog":Et("cancel",e),Et("close",e);break;case"iframe":case"object":case"embed":Et("load",e);break;case"video":case"audio":for(n=0;n<Il.length;n++)Et(Il[n],e);break;case"source":Et("error",e);break;case"img":case"image":case"link":Et("error",e),Et("load",e);break;case"details":Et("toggle",e);break;case"input":Et("invalid",e),xo(e,s.value,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name,!0),Bn(e);break;case"select":Et("invalid",e);break;case"textarea":Et("invalid",e),Zi(e,s.value,s.defaultValue,s.children),Bn(e)}n=s.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||s.suppressHydrationWarning===!0||Kd(e.textContent,n)?(s.popover!=null&&(Et("beforetoggle",e),Et("toggle",e)),s.onScroll!=null&&Et("scroll",e),s.onScrollEnd!=null&&Et("scrollend",e),s.onClick!=null&&(e.onclick=wr),e=!0):e=!1,e||Ke(t)}function qo(t){for(nt=t.return;nt;)switch(nt.tag){case 5:case 13:$t=!1;return;case 27:case 3:$t=!0;return;default:nt=nt.return}}function Ia(t){if(t!==nt)return!1;if(!gt)return qo(t),gt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||Ic(t.type,t.memoizedProps)),n=!n),n&&ot&&Ke(t),qo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(h(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){ot=Ri(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}ot=null}}else e===27?(e=ot,ga(t.type)?(t=Xc,Xc=null,ot=t):ot=e):ot=nt?Ri(t.stateNode.nextSibling):null;return!0}function ta(){ot=nt=null,gt=!1}function Vo(){var t=Yt;return t!==null&&(Je===null?Je=t:Je.push.apply(Je,t),Yt=null),t}function ea(t){Yt===null?Yt=[t]:Yt.push(t)}var Qt=k(null),Si=null,Di=null;function Vi(t,e,n){K(Qt,e._currentValue),e._currentValue=n}function zi(t){t._currentValue=Qt.current,J(Qt)}function qa(t,e,n){for(;t!==null;){var s=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,s!==null&&(s.childLanes|=e)):s!==null&&(s.childLanes&e)!==e&&(s.childLanes|=e),t===n)break;t=t.return}}function Cs(t,e,n,s){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var c=o.dependencies;if(c!==null){var m=o.child;c=c.firstContext;t:for(;c!==null;){var v=c;c=o;for(var T=0;T<e.length;T++)if(v.context===e[T]){c.lanes|=n,v=c.alternate,v!==null&&(v.lanes|=n),qa(c.return,n,t),s||(m=null);break t}c=v.next}}else if(o.tag===18){if(m=o.return,m===null)throw Error(h(341));m.lanes|=n,c=m.alternate,c!==null&&(c.lanes|=n),qa(m,n,t),m=null}else m=o.child;if(m!==null)m.return=o;else for(m=o;m!==null;){if(m===t){m=null;break}if(o=m.sibling,o!==null){o.return=m.return,m=o;break}m=m.return}o=m}}function Va(t,e,n,s){t=null;for(var o=e,c=!1;o!==null;){if(!c){if((o.flags&524288)!==0)c=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var m=o.alternate;if(m===null)throw Error(h(387));if(m=m.memoizedProps,m!==null){var v=o.type;Ze(o.pendingProps.value,m.value)||(t!==null?t.push(v):t=[v])}}else if(o===Jt.current){if(m=o.alternate,m===null)throw Error(h(387));m.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(Ql):t=[Ql])}o=o.return}t!==null&&Cs(e,t,n,s),e.flags|=262144}function Yo(t){for(t=t.firstContext;t!==null;){if(!Ze(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ya(t){Si=t,Di=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Re(t){return Rf(Si,t)}function Xo(t,e){return Si===null&&Ya(t),Rf(t,e)}function Rf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},Di===null){if(t===null)throw Error(h(308));Di=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Di=Di.next=e;return n}var mp=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,s){t.push(s)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},pp=p.unstable_scheduleCallback,gp=p.unstable_NormalPriority,me={$$typeof:Tt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Du(){return{controller:new mp,data:new Map,refCount:0}}function bl(t){t.refCount--,t.refCount===0&&pp(gp,function(){t.controller.abort()})}var Tl=null,zu=0,ws=0,Ls=null;function _p(t,e){if(Tl===null){var n=Tl=[];zu=0,ws=Rc(),Ls={status:"pending",value:void 0,then:function(s){n.push(s)}}}return zu++,e.then(Pf,Pf),e}function Pf(){if(--zu===0&&Tl!==null){Ls!==null&&(Ls.status="fulfilled");var t=Tl;Tl=null,ws=0,Ls=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function vp(t,e){var n=[],s={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return t.then(function(){s.status="fulfilled",s.value=e;for(var o=0;o<n.length;o++)(0,n[o])(e)},function(o){for(s.status="rejected",s.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),s}var Bf=P.S;P.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&_p(t,e),Bf!==null&&Bf(t,e)};var Xa=k(null);function Nu(){var t=Xa.current;return t!==null?t:Xt.pooledCache}function Ko(t,e){e===null?K(Xa,Xa.current):K(Xa,e.pool)}function Uf(){var t=Nu();return t===null?null:{parent:me._currentValue,pool:t}}var xl=Error(h(460)),kf=Error(h(474)),Qo=Error(h(542)),Ru={then:function(){}};function Zf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Fo(){}function Hf(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Fo,Fo),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Gf(t),t;default:if(typeof e.status=="string")e.then(Fo,Fo);else{if(t=Xt,t!==null&&100<t.shellSuspendCounter)throw Error(h(482));t=e,t.status="pending",t.then(function(s){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=s}},function(s){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=s}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Gf(t),t}throw Cl=e,xl}}var Cl=null;function jf(){if(Cl===null)throw Error(h(459));var t=Cl;return Cl=null,t}function Gf(t){if(t===xl||t===Qo)throw Error(h(483))}var ia=!1;function Pu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Bu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function na(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function aa(t,e,n){var s=t.updateQueue;if(s===null)return null;if(s=s.shared,(Ut&2)!==0){var o=s.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),s.pending=e,e=Ii(t),ja(t,null,n),e}return Ha(t,s,e,n),Ii(t)}function wl(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var s=e.lanes;s&=t.pendingLanes,n|=s,e.lanes=n,_o(t,n)}}function Uu(t,e){var n=t.updateQueue,s=t.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var o=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var m={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?o=c=m:c=c.next=m,n=n.next}while(n!==null);c===null?o=c=e:c=c.next=e}else o=c=e;n={baseState:s.baseState,firstBaseUpdate:o,lastBaseUpdate:c,shared:s.shared,callbacks:s.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var ku=!1;function Ll(){if(ku){var t=Ls;if(t!==null)throw t}}function Al(t,e,n,s){ku=!1;var o=t.updateQueue;ia=!1;var c=o.firstBaseUpdate,m=o.lastBaseUpdate,v=o.shared.pending;if(v!==null){o.shared.pending=null;var T=v,z=T.next;T.next=null,m===null?c=z:m.next=z,m=T;var Z=t.alternate;Z!==null&&(Z=Z.updateQueue,v=Z.lastBaseUpdate,v!==m&&(v===null?Z.firstBaseUpdate=z:v.next=z,Z.lastBaseUpdate=T))}if(c!==null){var G=o.baseState;m=0,Z=z=T=null,v=c;do{var R=v.lane&-536870913,B=R!==v.lane;if(B?(zt&R)===R:(s&R)===R){R!==0&&R===ws&&(ku=!0),Z!==null&&(Z=Z.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});t:{var mt=t,ct=v;R=e;var jt=n;switch(ct.tag){case 1:if(mt=ct.payload,typeof mt=="function"){G=mt.call(jt,G,R);break t}G=mt;break t;case 3:mt.flags=mt.flags&-65537|128;case 0:if(mt=ct.payload,R=typeof mt=="function"?mt.call(jt,G,R):mt,R==null)break t;G=j({},G,R);break t;case 2:ia=!0}}R=v.callback,R!==null&&(t.flags|=64,B&&(t.flags|=8192),B=o.callbacks,B===null?o.callbacks=[R]:B.push(R))}else B={lane:R,tag:v.tag,payload:v.payload,callback:v.callback,next:null},Z===null?(z=Z=B,T=G):Z=Z.next=B,m|=R;if(v=v.next,v===null){if(v=o.shared.pending,v===null)break;B=v,v=B.next,B.next=null,o.lastBaseUpdate=B,o.shared.pending=null}}while(!0);Z===null&&(T=G),o.baseState=T,o.firstBaseUpdate=z,o.lastBaseUpdate=Z,c===null&&(o.shared.lanes=0),ha|=m,t.lanes=m,t.memoizedState=G}}function If(t,e){if(typeof t!="function")throw Error(h(191,t));t.call(e)}function qf(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)If(n[t],e)}var As=k(null),Jo=k(0);function Vf(t,e){t=wn,K(Jo,t),K(As,e),wn=t|e.baseLanes}function Zu(){K(Jo,wn),K(As,As.current)}function Hu(){wn=Jo.current,J(As),J(Jo)}var sa=0,Ct=null,Zt=null,ce=null,Wo=!1,Es=!1,Ka=!1,$o=0,El=0,Ms=null,yp=0;function ae(){throw Error(h(321))}function ju(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Ze(t[n],e[n]))return!1;return!0}function Gu(t,e,n,s,o,c){return sa=c,Ct=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,P.H=t===null||t.memoizedState===null?Eh:Mh,Ka=!1,c=n(s,o),Ka=!1,Es&&(c=Xf(e,n,s,o)),Yf(t),c}function Yf(t){P.H=sr;var e=Zt!==null&&Zt.next!==null;if(sa=0,ce=Zt=Ct=null,Wo=!1,El=0,Ms=null,e)throw Error(h(300));t===null||ye||(t=t.dependencies,t!==null&&Yo(t)&&(ye=!0))}function Xf(t,e,n,s){Ct=t;var o=0;do{if(Es&&(Ms=null),El=0,Es=!1,25<=o)throw Error(h(301));if(o+=1,ce=Zt=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}P.H=Lp,c=e(n,s)}while(Es);return c}function Sp(){var t=P.H,e=t.useState()[0];return e=typeof e.then=="function"?Ml(e):e,t=t.useState()[0],(Zt!==null?Zt.memoizedState:null)!==t&&(Ct.flags|=1024),e}function Iu(){var t=$o!==0;return $o=0,t}function qu(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Vu(t){if(Wo){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Wo=!1}sa=0,ce=Zt=Ct=null,Es=!1,El=$o=0,Ms=null}function Qe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ce===null?Ct.memoizedState=ce=t:ce=ce.next=t,ce}function fe(){if(Zt===null){var t=Ct.alternate;t=t!==null?t.memoizedState:null}else t=Zt.next;var e=ce===null?Ct.memoizedState:ce.next;if(e!==null)ce=e,Zt=t;else{if(t===null)throw Ct.alternate===null?Error(h(467)):Error(h(310));Zt=t,t={memoizedState:Zt.memoizedState,baseState:Zt.baseState,baseQueue:Zt.baseQueue,queue:Zt.queue,next:null},ce===null?Ct.memoizedState=ce=t:ce=ce.next=t}return ce}function Yu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ml(t){var e=El;return El+=1,Ms===null&&(Ms=[]),t=Hf(Ms,t,e),e=Ct,(ce===null?e.memoizedState:ce.next)===null&&(e=e.alternate,P.H=e===null||e.memoizedState===null?Eh:Mh),t}function tr(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Ml(t);if(t.$$typeof===Tt)return Re(t)}throw Error(h(438,String(t)))}function Xu(t){var e=null,n=Ct.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var s=Ct.alternate;s!==null&&(s=s.updateQueue,s!==null&&(s=s.memoCache,s!=null&&(e={data:s.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=Yu(),Ct.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),s=0;s<t;s++)n[s]=tt;return e.index++,n}function yn(t,e){return typeof e=="function"?e(t):e}function er(t){var e=fe();return Ku(e,Zt,t)}function Ku(t,e,n){var s=t.queue;if(s===null)throw Error(h(311));s.lastRenderedReducer=n;var o=t.baseQueue,c=s.pending;if(c!==null){if(o!==null){var m=o.next;o.next=c.next,c.next=m}e.baseQueue=o=c,s.pending=null}if(c=t.baseState,o===null)t.memoizedState=c;else{e=o.next;var v=m=null,T=null,z=e,Z=!1;do{var G=z.lane&-536870913;if(G!==z.lane?(zt&G)===G:(sa&G)===G){var R=z.revertLane;if(R===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),G===ws&&(Z=!0);else if((sa&R)===R){z=z.next,R===ws&&(Z=!0);continue}else G={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},T===null?(v=T=G,m=c):T=T.next=G,Ct.lanes|=R,ha|=R;G=z.action,Ka&&n(c,G),c=z.hasEagerState?z.eagerState:n(c,G)}else R={lane:G,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},T===null?(v=T=R,m=c):T=T.next=R,Ct.lanes|=G,ha|=G;z=z.next}while(z!==null&&z!==e);if(T===null?m=c:T.next=v,!Ze(c,t.memoizedState)&&(ye=!0,Z&&(n=Ls,n!==null)))throw n;t.memoizedState=c,t.baseState=m,t.baseQueue=T,s.lastRenderedState=c}return o===null&&(s.lanes=0),[t.memoizedState,s.dispatch]}function Qu(t){var e=fe(),n=e.queue;if(n===null)throw Error(h(311));n.lastRenderedReducer=t;var s=n.dispatch,o=n.pending,c=e.memoizedState;if(o!==null){n.pending=null;var m=o=o.next;do c=t(c,m.action),m=m.next;while(m!==o);Ze(c,e.memoizedState)||(ye=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),n.lastRenderedState=c}return[c,s]}function Kf(t,e,n){var s=Ct,o=fe(),c=gt;if(c){if(n===void 0)throw Error(h(407));n=n()}else n=e();var m=!Ze((Zt||o).memoizedState,n);m&&(o.memoizedState=n,ye=!0),o=o.queue;var v=Jf.bind(null,s,o,t);if(Ol(2048,8,v,[t]),o.getSnapshot!==e||m||ce!==null&&ce.memoizedState.tag&1){if(s.flags|=2048,Os(9,ir(),Ff.bind(null,s,o,n,e),null),Xt===null)throw Error(h(349));c||(sa&124)!==0||Qf(s,e,n)}return n}function Qf(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=Ct.updateQueue,e===null?(e=Yu(),Ct.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Ff(t,e,n,s){e.value=n,e.getSnapshot=s,Wf(e)&&$f(t)}function Jf(t,e,n){return n(function(){Wf(e)&&$f(t)})}function Wf(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Ze(t,n)}catch{return!0}}function $f(t){var e=Wn(t,2);e!==null&&fi(e,t,2)}function Fu(t){var e=Qe();if(typeof t=="function"){var n=t;if(t=n(),Ka){wi(!0);try{n()}finally{wi(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:t},e}function th(t,e,n,s){return t.baseState=n,Ku(t,Zt,typeof s=="function"?s:yn)}function bp(t,e,n,s,o){if(ar(t))throw Error(h(485));if(t=e.action,t!==null){var c={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(m){c.listeners.push(m)}};P.T!==null?n(!0):c.isTransition=!1,s(c),n=e.pending,n===null?(c.next=e.pending=c,eh(e,c)):(c.next=n.next,e.pending=n.next=c)}}function eh(t,e){var n=e.action,s=e.payload,o=t.state;if(e.isTransition){var c=P.T,m={};P.T=m;try{var v=n(o,s),T=P.S;T!==null&&T(m,v),ih(t,e,v)}catch(z){Ju(t,e,z)}finally{P.T=c}}else try{c=n(o,s),ih(t,e,c)}catch(z){Ju(t,e,z)}}function ih(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(s){nh(t,e,s)},function(s){return Ju(t,e,s)}):nh(t,e,n)}function nh(t,e,n){e.status="fulfilled",e.value=n,ah(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,eh(t,n)))}function Ju(t,e,n){var s=t.pending;if(t.pending=null,s!==null){s=s.next;do e.status="rejected",e.reason=n,ah(e),e=e.next;while(e!==s)}t.action=null}function ah(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function sh(t,e){return e}function lh(t,e){if(gt){var n=Xt.formState;if(n!==null){t:{var s=Ct;if(gt){if(ot){e:{for(var o=ot,c=$t;o.nodeType!==8;){if(!c){o=null;break e}if(o=Ri(o.nextSibling),o===null){o=null;break e}}c=o.data,o=c==="F!"||c==="F"?o:null}if(o){ot=Ri(o.nextSibling),s=o.data==="F!";break t}}Ke(s)}s=!1}s&&(e=n[0])}}return n=Qe(),n.memoizedState=n.baseState=e,s={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sh,lastRenderedState:e},n.queue=s,n=wh.bind(null,Ct,s),s.dispatch=n,s=Fu(!1),c=ic.bind(null,Ct,!1,s.queue),s=Qe(),o={state:e,dispatch:null,action:t,pending:null},s.queue=o,n=bp.bind(null,Ct,o,c,n),o.dispatch=n,s.memoizedState=t,[e,n,!1]}function oh(t){var e=fe();return rh(e,Zt,t)}function rh(t,e,n){if(e=Ku(t,e,sh)[0],t=er(yn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var s=Ml(e)}catch(m){throw m===xl?Qo:m}else s=e;e=fe();var o=e.queue,c=o.dispatch;return n!==e.memoizedState&&(Ct.flags|=2048,Os(9,ir(),Tp.bind(null,o,n),null)),[s,c,t]}function Tp(t,e){t.action=e}function uh(t){var e=fe(),n=Zt;if(n!==null)return rh(e,n,t);fe(),e=e.memoizedState,n=fe();var s=n.queue.dispatch;return n.memoizedState=t,[e,s,!1]}function Os(t,e,n,s){return t={tag:t,create:n,deps:s,inst:e,next:null},e=Ct.updateQueue,e===null&&(e=Yu(),Ct.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(s=n.next,n.next=t,t.next=s,e.lastEffect=t),t}function ir(){return{destroy:void 0,resource:void 0}}function ch(){return fe().memoizedState}function nr(t,e,n,s){var o=Qe();s=s===void 0?null:s,Ct.flags|=t,o.memoizedState=Os(1|e,ir(),n,s)}function Ol(t,e,n,s){var o=fe();s=s===void 0?null:s;var c=o.memoizedState.inst;Zt!==null&&s!==null&&ju(s,Zt.memoizedState.deps)?o.memoizedState=Os(e,c,n,s):(Ct.flags|=t,o.memoizedState=Os(1|e,c,n,s))}function fh(t,e){nr(8390656,8,t,e)}function hh(t,e){Ol(2048,8,t,e)}function dh(t,e){return Ol(4,2,t,e)}function mh(t,e){return Ol(4,4,t,e)}function ph(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function gh(t,e,n){n=n!=null?n.concat([t]):null,Ol(4,4,ph.bind(null,e,t),n)}function Wu(){}function _h(t,e){var n=fe();e=e===void 0?null:e;var s=n.memoizedState;return e!==null&&ju(e,s[1])?s[0]:(n.memoizedState=[t,e],t)}function vh(t,e){var n=fe();e=e===void 0?null:e;var s=n.memoizedState;if(e!==null&&ju(e,s[1]))return s[0];if(s=t(),Ka){wi(!0);try{t()}finally{wi(!1)}}return n.memoizedState=[s,e],s}function $u(t,e,n){return n===void 0||(sa&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=bd(),Ct.lanes|=t,ha|=t,n)}function yh(t,e,n,s){return Ze(n,e)?n:As.current!==null?(t=$u(t,n,s),Ze(t,e)||(ye=!0),t):(sa&42)===0?(ye=!0,t.memoizedState=n):(t=bd(),Ct.lanes|=t,ha|=t,e)}function Sh(t,e,n,s,o){var c=Q.p;Q.p=c!==0&&8>c?c:8;var m=P.T,v={};P.T=v,ic(t,!1,e,n);try{var T=o(),z=P.S;if(z!==null&&z(v,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var Z=vp(T,s);Dl(t,e,Z,ci(t))}else Dl(t,e,s,ci(t))}catch(G){Dl(t,e,{then:function(){},status:"rejected",reason:G},ci())}finally{Q.p=c,P.T=m}}function xp(){}function tc(t,e,n,s){if(t.tag!==5)throw Error(h(476));var o=bh(t).queue;Sh(t,o,e,V,n===null?xp:function(){return Th(t),n(s)})}function bh(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:V,baseState:V,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:V},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Th(t){var e=bh(t).next.queue;Dl(t,e,{},ci())}function ec(){return Re(Ql)}function xh(){return fe().memoizedState}function Ch(){return fe().memoizedState}function Cp(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ci();t=na(n);var s=aa(e,t,n);s!==null&&(fi(s,e,n),wl(s,e,n)),e={cache:Du()},t.payload=e;return}e=e.return}}function wp(t,e,n){var s=ci();n={lane:s,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ar(t)?Lh(e,n):(n=gn(t,e,n,s),n!==null&&(fi(n,t,s),Ah(n,e,s)))}function wh(t,e,n){var s=ci();Dl(t,e,n,s)}function Dl(t,e,n,s){var o={lane:s,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ar(t))Lh(e,o);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var m=e.lastRenderedState,v=c(m,n);if(o.hasEagerState=!0,o.eagerState=v,Ze(v,m))return Ha(t,e,o,0),Xt===null&&Za(),!1}catch{}finally{}if(n=gn(t,e,o,s),n!==null)return fi(n,t,s),Ah(n,e,s),!0}return!1}function ic(t,e,n,s){if(s={lane:2,revertLane:Rc(),action:s,hasEagerState:!1,eagerState:null,next:null},ar(t)){if(e)throw Error(h(479))}else e=gn(t,n,s,2),e!==null&&fi(e,t,2)}function ar(t){var e=t.alternate;return t===Ct||e!==null&&e===Ct}function Lh(t,e){Es=Wo=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Ah(t,e,n){if((n&4194048)!==0){var s=e.lanes;s&=t.pendingLanes,n|=s,e.lanes=n,_o(t,n)}}var sr={readContext:Re,use:tr,useCallback:ae,useContext:ae,useEffect:ae,useImperativeHandle:ae,useLayoutEffect:ae,useInsertionEffect:ae,useMemo:ae,useReducer:ae,useRef:ae,useState:ae,useDebugValue:ae,useDeferredValue:ae,useTransition:ae,useSyncExternalStore:ae,useId:ae,useHostTransitionStatus:ae,useFormState:ae,useActionState:ae,useOptimistic:ae,useMemoCache:ae,useCacheRefresh:ae},Eh={readContext:Re,use:tr,useCallback:function(t,e){return Qe().memoizedState=[t,e===void 0?null:e],t},useContext:Re,useEffect:fh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,nr(4194308,4,ph.bind(null,e,t),n)},useLayoutEffect:function(t,e){return nr(4194308,4,t,e)},useInsertionEffect:function(t,e){nr(4,2,t,e)},useMemo:function(t,e){var n=Qe();e=e===void 0?null:e;var s=t();if(Ka){wi(!0);try{t()}finally{wi(!1)}}return n.memoizedState=[s,e],s},useReducer:function(t,e,n){var s=Qe();if(n!==void 0){var o=n(e);if(Ka){wi(!0);try{n(e)}finally{wi(!1)}}}else o=e;return s.memoizedState=s.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},s.queue=t,t=t.dispatch=wp.bind(null,Ct,t),[s.memoizedState,t]},useRef:function(t){var e=Qe();return t={current:t},e.memoizedState=t},useState:function(t){t=Fu(t);var e=t.queue,n=wh.bind(null,Ct,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Wu,useDeferredValue:function(t,e){var n=Qe();return $u(n,t,e)},useTransition:function(){var t=Fu(!1);return t=Sh.bind(null,Ct,t.queue,!0,!1),Qe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var s=Ct,o=Qe();if(gt){if(n===void 0)throw Error(h(407));n=n()}else{if(n=e(),Xt===null)throw Error(h(349));(zt&124)!==0||Qf(s,e,n)}o.memoizedState=n;var c={value:n,getSnapshot:e};return o.queue=c,fh(Jf.bind(null,s,c,t),[t]),s.flags|=2048,Os(9,ir(),Ff.bind(null,s,c,n,e),null),n},useId:function(){var t=Qe(),e=Xt.identifierPrefix;if(gt){var n=b,s=_;n=(s&~(1<<32-Ue(s)-1)).toString(32)+n,e="«"+e+"R"+n,n=$o++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=yp++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:ec,useFormState:lh,useActionState:lh,useOptimistic:function(t){var e=Qe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=ic.bind(null,Ct,!0,n),n.dispatch=e,[t,e]},useMemoCache:Xu,useCacheRefresh:function(){return Qe().memoizedState=Cp.bind(null,Ct)}},Mh={readContext:Re,use:tr,useCallback:_h,useContext:Re,useEffect:hh,useImperativeHandle:gh,useInsertionEffect:dh,useLayoutEffect:mh,useMemo:vh,useReducer:er,useRef:ch,useState:function(){return er(yn)},useDebugValue:Wu,useDeferredValue:function(t,e){var n=fe();return yh(n,Zt.memoizedState,t,e)},useTransition:function(){var t=er(yn)[0],e=fe().memoizedState;return[typeof t=="boolean"?t:Ml(t),e]},useSyncExternalStore:Kf,useId:xh,useHostTransitionStatus:ec,useFormState:oh,useActionState:oh,useOptimistic:function(t,e){var n=fe();return th(n,Zt,t,e)},useMemoCache:Xu,useCacheRefresh:Ch},Lp={readContext:Re,use:tr,useCallback:_h,useContext:Re,useEffect:hh,useImperativeHandle:gh,useInsertionEffect:dh,useLayoutEffect:mh,useMemo:vh,useReducer:Qu,useRef:ch,useState:function(){return Qu(yn)},useDebugValue:Wu,useDeferredValue:function(t,e){var n=fe();return Zt===null?$u(n,t,e):yh(n,Zt.memoizedState,t,e)},useTransition:function(){var t=Qu(yn)[0],e=fe().memoizedState;return[typeof t=="boolean"?t:Ml(t),e]},useSyncExternalStore:Kf,useId:xh,useHostTransitionStatus:ec,useFormState:uh,useActionState:uh,useOptimistic:function(t,e){var n=fe();return Zt!==null?th(n,Zt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Xu,useCacheRefresh:Ch},Ds=null,zl=0;function lr(t){var e=zl;return zl+=1,Ds===null&&(Ds=[]),Hf(Ds,t,e)}function Nl(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function or(t,e){throw e.$$typeof===X?Error(h(525)):(t=Object.prototype.toString.call(e),Error(h(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Oh(t){var e=t._init;return e(t._payload)}function Dh(t){function e(w,C){if(t){var D=w.deletions;D===null?(w.deletions=[C],w.flags|=16):D.push(C)}}function n(w,C){if(!t)return null;for(;C!==null;)e(w,C),C=C.sibling;return null}function s(w){for(var C=new Map;w!==null;)w.key!==null?C.set(w.key,w):C.set(w.index,w),w=w.sibling;return C}function o(w,C){return w=yi(w,C),w.index=0,w.sibling=null,w}function c(w,C,D){return w.index=D,t?(D=w.alternate,D!==null?(D=D.index,D<C?(w.flags|=67108866,C):D):(w.flags|=67108866,C)):(w.flags|=1048576,C)}function m(w){return t&&w.alternate===null&&(w.flags|=67108866),w}function v(w,C,D,H){return C===null||C.tag!==6?(C=Sl(D,w.mode,H),C.return=w,C):(C=o(C,D),C.return=w,C)}function T(w,C,D,H){var at=D.type;return at===ht?Z(w,C,D.props.children,H,D.key):C!==null&&(C.elementType===at||typeof at=="object"&&at!==null&&at.$$typeof===re&&Oh(at)===C.type)?(C=o(C,D.props),Nl(C,D),C.return=w,C):(C=Ga(D.type,D.key,D.props,null,w.mode,H),Nl(C,D),C.return=w,C)}function z(w,C,D,H){return C===null||C.tag!==4||C.stateNode.containerInfo!==D.containerInfo||C.stateNode.implementation!==D.implementation?(C=xs(D,w.mode,H),C.return=w,C):(C=o(C,D.children||[]),C.return=w,C)}function Z(w,C,D,H,at){return C===null||C.tag!==7?(C=qi(D,w.mode,H,at),C.return=w,C):(C=o(C,D),C.return=w,C)}function G(w,C,D){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return C=Sl(""+C,w.mode,D),C.return=w,C;if(typeof C=="object"&&C!==null){switch(C.$$typeof){case Y:return D=Ga(C.type,C.key,C.props,null,w.mode,D),Nl(D,C),D.return=w,D;case dt:return C=xs(C,w.mode,D),C.return=w,C;case re:var H=C._init;return C=H(C._payload),G(w,C,D)}if(Nt(C)||F(C))return C=qi(C,w.mode,D,null),C.return=w,C;if(typeof C.then=="function")return G(w,lr(C),D);if(C.$$typeof===Tt)return G(w,Xo(w,C),D);or(w,C)}return null}function R(w,C,D,H){var at=C!==null?C.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return at!==null?null:v(w,C,""+D,H);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case Y:return D.key===at?T(w,C,D,H):null;case dt:return D.key===at?z(w,C,D,H):null;case re:return at=D._init,D=at(D._payload),R(w,C,D,H)}if(Nt(D)||F(D))return at!==null?null:Z(w,C,D,H,null);if(typeof D.then=="function")return R(w,C,lr(D),H);if(D.$$typeof===Tt)return R(w,C,Xo(w,D),H);or(w,D)}return null}function B(w,C,D,H,at){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return w=w.get(D)||null,v(C,w,""+H,at);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case Y:return w=w.get(H.key===null?D:H.key)||null,T(C,w,H,at);case dt:return w=w.get(H.key===null?D:H.key)||null,z(C,w,H,at);case re:var Lt=H._init;return H=Lt(H._payload),B(w,C,D,H,at)}if(Nt(H)||F(H))return w=w.get(D)||null,Z(C,w,H,at,null);if(typeof H.then=="function")return B(w,C,D,lr(H),at);if(H.$$typeof===Tt)return B(w,C,D,Xo(C,H),at);or(C,H)}return null}function mt(w,C,D,H){for(var at=null,Lt=null,ut=C,ft=C=0,be=null;ut!==null&&ft<D.length;ft++){ut.index>ft?(be=ut,ut=null):be=ut.sibling;var Bt=R(w,ut,D[ft],H);if(Bt===null){ut===null&&(ut=be);break}t&&ut&&Bt.alternate===null&&e(w,ut),C=c(Bt,C,ft),Lt===null?at=Bt:Lt.sibling=Bt,Lt=Bt,ut=be}if(ft===D.length)return n(w,ut),gt&&M(w,ft),at;if(ut===null){for(;ft<D.length;ft++)ut=G(w,D[ft],H),ut!==null&&(C=c(ut,C,ft),Lt===null?at=ut:Lt.sibling=ut,Lt=ut);return gt&&M(w,ft),at}for(ut=s(ut);ft<D.length;ft++)be=B(ut,w,ft,D[ft],H),be!==null&&(t&&be.alternate!==null&&ut.delete(be.key===null?ft:be.key),C=c(be,C,ft),Lt===null?at=be:Lt.sibling=be,Lt=be);return t&&ut.forEach(function(ba){return e(w,ba)}),gt&&M(w,ft),at}function ct(w,C,D,H){if(D==null)throw Error(h(151));for(var at=null,Lt=null,ut=C,ft=C=0,be=null,Bt=D.next();ut!==null&&!Bt.done;ft++,Bt=D.next()){ut.index>ft?(be=ut,ut=null):be=ut.sibling;var ba=R(w,ut,Bt.value,H);if(ba===null){ut===null&&(ut=be);break}t&&ut&&ba.alternate===null&&e(w,ut),C=c(ba,C,ft),Lt===null?at=ba:Lt.sibling=ba,Lt=ba,ut=be}if(Bt.done)return n(w,ut),gt&&M(w,ft),at;if(ut===null){for(;!Bt.done;ft++,Bt=D.next())Bt=G(w,Bt.value,H),Bt!==null&&(C=c(Bt,C,ft),Lt===null?at=Bt:Lt.sibling=Bt,Lt=Bt);return gt&&M(w,ft),at}for(ut=s(ut);!Bt.done;ft++,Bt=D.next())Bt=B(ut,w,ft,Bt.value,H),Bt!==null&&(t&&Bt.alternate!==null&&ut.delete(Bt.key===null?ft:Bt.key),C=c(Bt,C,ft),Lt===null?at=Bt:Lt.sibling=Bt,Lt=Bt);return t&&ut.forEach(function(Ag){return e(w,Ag)}),gt&&M(w,ft),at}function jt(w,C,D,H){if(typeof D=="object"&&D!==null&&D.type===ht&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case Y:t:{for(var at=D.key;C!==null;){if(C.key===at){if(at=D.type,at===ht){if(C.tag===7){n(w,C.sibling),H=o(C,D.props.children),H.return=w,w=H;break t}}else if(C.elementType===at||typeof at=="object"&&at!==null&&at.$$typeof===re&&Oh(at)===C.type){n(w,C.sibling),H=o(C,D.props),Nl(H,D),H.return=w,w=H;break t}n(w,C);break}else e(w,C);C=C.sibling}D.type===ht?(H=qi(D.props.children,w.mode,H,D.key),H.return=w,w=H):(H=Ga(D.type,D.key,D.props,null,w.mode,H),Nl(H,D),H.return=w,w=H)}return m(w);case dt:t:{for(at=D.key;C!==null;){if(C.key===at)if(C.tag===4&&C.stateNode.containerInfo===D.containerInfo&&C.stateNode.implementation===D.implementation){n(w,C.sibling),H=o(C,D.children||[]),H.return=w,w=H;break t}else{n(w,C);break}else e(w,C);C=C.sibling}H=xs(D,w.mode,H),H.return=w,w=H}return m(w);case re:return at=D._init,D=at(D._payload),jt(w,C,D,H)}if(Nt(D))return mt(w,C,D,H);if(F(D)){if(at=F(D),typeof at!="function")throw Error(h(150));return D=at.call(D),ct(w,C,D,H)}if(typeof D.then=="function")return jt(w,C,lr(D),H);if(D.$$typeof===Tt)return jt(w,C,Xo(w,D),H);or(w,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,C!==null&&C.tag===6?(n(w,C.sibling),H=o(C,D),H.return=w,w=H):(n(w,C),H=Sl(D,w.mode,H),H.return=w,w=H),m(w)):n(w,C)}return function(w,C,D,H){try{zl=0;var at=jt(w,C,D,H);return Ds=null,at}catch(ut){if(ut===xl||ut===Qo)throw ut;var Lt=Ie(29,ut,null,w.mode);return Lt.lanes=H,Lt.return=w,Lt}finally{}}}var zs=Dh(!0),zh=Dh(!1),bi=k(null),Yi=null;function la(t){var e=t.alternate;K(pe,pe.current&1),K(bi,t),Yi===null&&(e===null||As.current!==null||e.memoizedState!==null)&&(Yi=t)}function Nh(t){if(t.tag===22){if(K(pe,pe.current),K(bi,t),Yi===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Yi=t)}}else oa()}function oa(){K(pe,pe.current),K(bi,bi.current)}function Sn(t){J(bi),Yi===t&&(Yi=null),J(pe)}var pe=k(0);function rr(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Yc(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function nc(t,e,n,s){e=t.memoizedState,n=n(s,e),n=n==null?e:j({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var ac={enqueueSetState:function(t,e,n){t=t._reactInternals;var s=ci(),o=na(s);o.payload=e,n!=null&&(o.callback=n),e=aa(t,o,s),e!==null&&(fi(e,t,s),wl(e,t,s))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var s=ci(),o=na(s);o.tag=1,o.payload=e,n!=null&&(o.callback=n),e=aa(t,o,s),e!==null&&(fi(e,t,s),wl(e,t,s))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ci(),s=na(n);s.tag=2,e!=null&&(s.callback=e),e=aa(t,s,n),e!==null&&(fi(e,t,n),wl(e,t,n))}};function Rh(t,e,n,s,o,c,m){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(s,c,m):e.prototype&&e.prototype.isPureReactComponent?!He(n,s)||!He(o,c):!0}function Ph(t,e,n,s){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,s),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,s),e.state!==t&&ac.enqueueReplaceState(e,e.state,null)}function Qa(t,e){var n=e;if("ref"in e){n={};for(var s in e)s!=="ref"&&(n[s]=e[s])}if(t=t.defaultProps){n===e&&(n=j({},n));for(var o in t)n[o]===void 0&&(n[o]=t[o])}return n}var ur=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Bh(t){ur(t)}function Uh(t){console.error(t)}function kh(t){ur(t)}function cr(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(s){setTimeout(function(){throw s})}}function Zh(t,e,n){try{var s=t.onCaughtError;s(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function sc(t,e,n){return n=na(n),n.tag=3,n.payload={element:null},n.callback=function(){cr(t,e)},n}function Hh(t){return t=na(t),t.tag=3,t}function jh(t,e,n,s){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var c=s.value;t.payload=function(){return o(c)},t.callback=function(){Zh(e,n,s)}}var m=n.stateNode;m!==null&&typeof m.componentDidCatch=="function"&&(t.callback=function(){Zh(e,n,s),typeof o!="function"&&(da===null?da=new Set([this]):da.add(this));var v=s.stack;this.componentDidCatch(s.value,{componentStack:v!==null?v:""})})}function Ap(t,e,n,s,o){if(n.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){if(e=n.alternate,e!==null&&Va(e,n,o,!0),n=bi.current,n!==null){switch(n.tag){case 13:return Yi===null?Mc():n.alternate===null&&ie===0&&(ie=3),n.flags&=-257,n.flags|=65536,n.lanes=o,s===Ru?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([s]):e.add(s),Dc(t,s,o)),!1;case 22:return n.flags|=65536,s===Ru?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([s])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([s]):n.add(s)),Dc(t,s,o)),!1}throw Error(h(435,n.tag))}return Dc(t,s,o),Mc(),!1}if(gt)return e=bi.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,s!==ve&&(t=Error(h(422),{cause:s}),ea(je(t,n)))):(s!==ve&&(e=Error(h(423),{cause:s}),ea(je(e,n))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,s=je(s,n),o=sc(t.stateNode,s,o),Uu(t,o),ie!==4&&(ie=2)),!1;var c=Error(h(520),{cause:s});if(c=je(c,n),Hl===null?Hl=[c]:Hl.push(c),ie!==4&&(ie=2),e===null)return!0;s=je(s,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=o&-o,n.lanes|=t,t=sc(n.stateNode,s,t),Uu(n,t),!1;case 1:if(e=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(da===null||!da.has(c))))return n.flags|=65536,o&=-o,n.lanes|=o,o=Hh(o),jh(o,t,n,s),Uu(n,o),!1}n=n.return}while(n!==null);return!1}var Gh=Error(h(461)),ye=!1;function Le(t,e,n,s){e.child=t===null?zh(e,null,n,s):zs(e,t.child,n,s)}function Ih(t,e,n,s,o){n=n.render;var c=e.ref;if("ref"in s){var m={};for(var v in s)v!=="ref"&&(m[v]=s[v])}else m=s;return Ya(e),s=Gu(t,e,n,m,c,o),v=Iu(),t!==null&&!ye?(qu(t,e,o),bn(t,e,o)):(gt&&v&&I(e),e.flags|=1,Le(t,e,s,o),e.child)}function qh(t,e,n,s,o){if(t===null){var c=n.type;return typeof c=="function"&&!Ts(c)&&c.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=c,Vh(t,e,c,s,o)):(t=Ga(n.type,null,s,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!dc(t,o)){var m=c.memoizedProps;if(n=n.compare,n=n!==null?n:He,n(m,s)&&t.ref===e.ref)return bn(t,e,o)}return e.flags|=1,t=yi(c,s),t.ref=e.ref,t.return=e,e.child=t}function Vh(t,e,n,s,o){if(t!==null){var c=t.memoizedProps;if(He(c,s)&&t.ref===e.ref)if(ye=!1,e.pendingProps=s=c,dc(t,o))(t.flags&131072)!==0&&(ye=!0);else return e.lanes=t.lanes,bn(t,e,o)}return lc(t,e,n,s,o)}function Yh(t,e,n){var s=e.pendingProps,o=s.children,c=t!==null?t.memoizedState:null;if(s.mode==="hidden"){if((e.flags&128)!==0){if(s=c!==null?c.baseLanes|n:n,t!==null){for(o=e.child=t.child,c=0;o!==null;)c=c|o.lanes|o.childLanes,o=o.sibling;e.childLanes=c&~s}else e.childLanes=0,e.child=null;return Xh(t,e,s,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Ko(e,c!==null?c.cachePool:null),c!==null?Vf(e,c):Zu(),Nh(e);else return e.lanes=e.childLanes=536870912,Xh(t,e,c!==null?c.baseLanes|n:n,n)}else c!==null?(Ko(e,c.cachePool),Vf(e,c),oa(),e.memoizedState=null):(t!==null&&Ko(e,null),Zu(),oa());return Le(t,e,o,n),e.child}function Xh(t,e,n,s){var o=Nu();return o=o===null?null:{parent:me._currentValue,pool:o},e.memoizedState={baseLanes:n,cachePool:o},t!==null&&Ko(e,null),Zu(),Nh(e),t!==null&&Va(t,e,s,!0),null}function fr(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(h(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function lc(t,e,n,s,o){return Ya(e),n=Gu(t,e,n,s,void 0,o),s=Iu(),t!==null&&!ye?(qu(t,e,o),bn(t,e,o)):(gt&&s&&I(e),e.flags|=1,Le(t,e,n,o),e.child)}function Kh(t,e,n,s,o,c){return Ya(e),e.updateQueue=null,n=Xf(e,s,n,o),Yf(t),s=Iu(),t!==null&&!ye?(qu(t,e,c),bn(t,e,c)):(gt&&s&&I(e),e.flags|=1,Le(t,e,n,c),e.child)}function Qh(t,e,n,s,o){if(Ya(e),e.stateNode===null){var c=_n,m=n.contextType;typeof m=="object"&&m!==null&&(c=Re(m)),c=new n(s,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=ac,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=s,c.state=e.memoizedState,c.refs={},Pu(e),m=n.contextType,c.context=typeof m=="object"&&m!==null?Re(m):_n,c.state=e.memoizedState,m=n.getDerivedStateFromProps,typeof m=="function"&&(nc(e,n,m,s),c.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(m=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),m!==c.state&&ac.enqueueReplaceState(c,c.state,null),Al(e,s,c,o),Ll(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),s=!0}else if(t===null){c=e.stateNode;var v=e.memoizedProps,T=Qa(n,v);c.props=T;var z=c.context,Z=n.contextType;m=_n,typeof Z=="object"&&Z!==null&&(m=Re(Z));var G=n.getDerivedStateFromProps;Z=typeof G=="function"||typeof c.getSnapshotBeforeUpdate=="function",v=e.pendingProps!==v,Z||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(v||z!==m)&&Ph(e,c,s,m),ia=!1;var R=e.memoizedState;c.state=R,Al(e,s,c,o),Ll(),z=e.memoizedState,v||R!==z||ia?(typeof G=="function"&&(nc(e,n,G,s),z=e.memoizedState),(T=ia||Rh(e,n,T,s,R,z,m))?(Z||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=s,e.memoizedState=z),c.props=s,c.state=z,c.context=m,s=T):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),s=!1)}else{c=e.stateNode,Bu(t,e),m=e.memoizedProps,Z=Qa(n,m),c.props=Z,G=e.pendingProps,R=c.context,z=n.contextType,T=_n,typeof z=="object"&&z!==null&&(T=Re(z)),v=n.getDerivedStateFromProps,(z=typeof v=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(m!==G||R!==T)&&Ph(e,c,s,T),ia=!1,R=e.memoizedState,c.state=R,Al(e,s,c,o),Ll();var B=e.memoizedState;m!==G||R!==B||ia||t!==null&&t.dependencies!==null&&Yo(t.dependencies)?(typeof v=="function"&&(nc(e,n,v,s),B=e.memoizedState),(Z=ia||Rh(e,n,Z,s,R,B,T)||t!==null&&t.dependencies!==null&&Yo(t.dependencies))?(z||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(s,B,T),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(s,B,T)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||m===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),e.memoizedProps=s,e.memoizedState=B),c.props=s,c.state=B,c.context=T,s=Z):(typeof c.componentDidUpdate!="function"||m===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),s=!1)}return c=s,fr(t,e),s=(e.flags&128)!==0,c||s?(c=e.stateNode,n=s&&typeof n.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&s?(e.child=zs(e,t.child,null,o),e.child=zs(e,null,n,o)):Le(t,e,n,o),e.memoizedState=c.state,t=e.child):t=bn(t,e,o),t}function Fh(t,e,n,s){return ta(),e.flags|=256,Le(t,e,n,s),e.child}var oc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function rc(t){return{baseLanes:t,cachePool:Uf()}}function uc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ti),t}function Jh(t,e,n){var s=e.pendingProps,o=!1,c=(e.flags&128)!==0,m;if((m=c)||(m=t!==null&&t.memoizedState===null?!1:(pe.current&2)!==0),m&&(o=!0,e.flags&=-129),m=(e.flags&32)!==0,e.flags&=-33,t===null){if(gt){if(o?la(e):oa(),gt){var v=ot,T;if(T=v){t:{for(T=v,v=$t;T.nodeType!==8;){if(!v){v=null;break t}if(T=Ri(T.nextSibling),T===null){v=null;break t}}v=T}v!==null?(e.memoizedState={dehydrated:v,treeContext:d!==null?{id:_,overflow:b}:null,retryLane:536870912,hydrationErrors:null},T=Ie(18,null,null,0),T.stateNode=v,T.return=e,e.child=T,nt=e,ot=null,T=!0):T=!1}T||Ke(e)}if(v=e.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return Yc(v)?e.lanes=32:e.lanes=536870912,null;Sn(e)}return v=s.children,s=s.fallback,o?(oa(),o=e.mode,v=hr({mode:"hidden",children:v},o),s=qi(s,o,n,null),v.return=e,s.return=e,v.sibling=s,e.child=v,o=e.child,o.memoizedState=rc(n),o.childLanes=uc(t,m,n),e.memoizedState=oc,s):(la(e),cc(e,v))}if(T=t.memoizedState,T!==null&&(v=T.dehydrated,v!==null)){if(c)e.flags&256?(la(e),e.flags&=-257,e=fc(t,e,n)):e.memoizedState!==null?(oa(),e.child=t.child,e.flags|=128,e=null):(oa(),o=s.fallback,v=e.mode,s=hr({mode:"visible",children:s.children},v),o=qi(o,v,n,null),o.flags|=2,s.return=e,o.return=e,s.sibling=o,e.child=s,zs(e,t.child,null,n),s=e.child,s.memoizedState=rc(n),s.childLanes=uc(t,m,n),e.memoizedState=oc,e=o);else if(la(e),Yc(v)){if(m=v.nextSibling&&v.nextSibling.dataset,m)var z=m.dgst;m=z,s=Error(h(419)),s.stack="",s.digest=m,ea({value:s,source:null,stack:null}),e=fc(t,e,n)}else if(ye||Va(t,e,n,!1),m=(n&t.childLanes)!==0,ye||m){if(m=Xt,m!==null&&(s=n&-n,s=(s&42)!==0?1:tl(s),s=(s&(m.suspendedLanes|n))!==0?0:s,s!==0&&s!==T.retryLane))throw T.retryLane=s,Wn(t,s),fi(m,t,s),Gh;v.data==="$?"||Mc(),e=fc(t,e,n)}else v.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=T.treeContext,ot=Ri(v.nextSibling),nt=e,gt=!0,Yt=null,$t=!1,t!==null&&(l[f++]=_,l[f++]=b,l[f++]=d,_=t.id,b=t.overflow,d=e),e=cc(e,s.children),e.flags|=4096);return e}return o?(oa(),o=s.fallback,v=e.mode,T=t.child,z=T.sibling,s=yi(T,{mode:"hidden",children:s.children}),s.subtreeFlags=T.subtreeFlags&65011712,z!==null?o=yi(z,o):(o=qi(o,v,n,null),o.flags|=2),o.return=e,s.return=e,s.sibling=o,e.child=s,s=o,o=e.child,v=t.child.memoizedState,v===null?v=rc(n):(T=v.cachePool,T!==null?(z=me._currentValue,T=T.parent!==z?{parent:z,pool:z}:T):T=Uf(),v={baseLanes:v.baseLanes|n,cachePool:T}),o.memoizedState=v,o.childLanes=uc(t,m,n),e.memoizedState=oc,s):(la(e),n=t.child,t=n.sibling,n=yi(n,{mode:"visible",children:s.children}),n.return=e,n.sibling=null,t!==null&&(m=e.deletions,m===null?(e.deletions=[t],e.flags|=16):m.push(t)),e.child=n,e.memoizedState=null,n)}function cc(t,e){return e=hr({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function hr(t,e){return t=Ie(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function fc(t,e,n){return zs(e,t.child,null,n),t=cc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Wh(t,e,n){t.lanes|=e;var s=t.alternate;s!==null&&(s.lanes|=e),qa(t.return,e,n)}function hc(t,e,n,s,o){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:o}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=s,c.tail=n,c.tailMode=o)}function $h(t,e,n){var s=e.pendingProps,o=s.revealOrder,c=s.tail;if(Le(t,e,s.children,n),s=pe.current,(s&2)!==0)s=s&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Wh(t,n,e);else if(t.tag===19)Wh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}s&=1}switch(K(pe,s),o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&rr(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),hc(e,!1,o,n,c);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&rr(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}hc(e,!0,n,null,c);break;case"together":hc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function bn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),ha|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Va(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(h(153));if(e.child!==null){for(t=e.child,n=yi(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=yi(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function dc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Yo(t)))}function Ep(t,e,n){switch(e.tag){case 3:Rt(e,e.stateNode.containerInfo),Vi(e,me,t.memoizedState.cache),ta();break;case 27:case 5:Ta(e);break;case 4:Rt(e,e.stateNode.containerInfo);break;case 10:Vi(e,e.type,e.memoizedProps.value);break;case 13:var s=e.memoizedState;if(s!==null)return s.dehydrated!==null?(la(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Jh(t,e,n):(la(e),t=bn(t,e,n),t!==null?t.sibling:null);la(e);break;case 19:var o=(t.flags&128)!==0;if(s=(n&e.childLanes)!==0,s||(Va(t,e,n,!1),s=(n&e.childLanes)!==0),o){if(s)return $h(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),K(pe,pe.current),s)break;return null;case 22:case 23:return e.lanes=0,Yh(t,e,n);case 24:Vi(e,me,t.memoizedState.cache)}return bn(t,e,n)}function td(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)ye=!0;else{if(!dc(t,n)&&(e.flags&128)===0)return ye=!1,Ep(t,e,n);ye=(t.flags&131072)!==0}else ye=!1,gt&&(e.flags&1048576)!==0&&U(e,a,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var s=e.elementType,o=s._init;if(s=o(s._payload),e.type=s,typeof s=="function")Ts(s)?(t=Qa(s,t),e.tag=1,e=Qh(null,e,s,t,n)):(e.tag=0,e=lc(null,e,s,t,n));else{if(s!=null){if(o=s.$$typeof,o===le){e.tag=11,e=Ih(null,e,s,t,n);break t}else if(o===oe){e.tag=14,e=qh(null,e,s,t,n);break t}}throw e=Mt(s)||s,Error(h(306,e,""))}}return e;case 0:return lc(t,e,e.type,e.pendingProps,n);case 1:return s=e.type,o=Qa(s,e.pendingProps),Qh(t,e,s,o,n);case 3:t:{if(Rt(e,e.stateNode.containerInfo),t===null)throw Error(h(387));s=e.pendingProps;var c=e.memoizedState;o=c.element,Bu(t,e),Al(e,s,null,n);var m=e.memoizedState;if(s=m.cache,Vi(e,me,s),s!==c.cache&&Cs(e,[me],n,!0),Ll(),s=m.element,c.isDehydrated)if(c={element:s,isDehydrated:!1,cache:m.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=Fh(t,e,s,n);break t}else if(s!==o){o=je(Error(h(424)),e),ea(o),e=Fh(t,e,s,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(ot=Ri(t.firstChild),nt=e,gt=!0,Yt=null,$t=!0,n=zh(e,null,s,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ta(),s===o){e=bn(t,e,n);break t}Le(t,e,s,n)}e=e.child}return e;case 26:return fr(t,e),t===null?(n=am(e.type,null,e.pendingProps,null))?e.memoizedState=n:gt||(n=e.type,t=e.pendingProps,s=Lr(W.current).createElement(n),s[ge]=e,s[De]=t,Ee(s,n,t),ue(s),e.stateNode=s):e.memoizedState=am(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Ta(e),t===null&&gt&&(s=e.stateNode=em(e.type,e.pendingProps,W.current),nt=e,$t=!0,o=ot,ga(e.type)?(Xc=o,ot=Ri(s.firstChild)):ot=o),Le(t,e,e.pendingProps.children,n),fr(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&gt&&((o=s=ot)&&(s=ig(s,e.type,e.pendingProps,$t),s!==null?(e.stateNode=s,nt=e,ot=Ri(s.firstChild),$t=!1,o=!0):o=!1),o||Ke(e)),Ta(e),o=e.type,c=e.pendingProps,m=t!==null?t.memoizedProps:null,s=c.children,Ic(o,c)?s=null:m!==null&&Ic(o,m)&&(e.flags|=32),e.memoizedState!==null&&(o=Gu(t,e,Sp,null,null,n),Ql._currentValue=o),fr(t,e),Le(t,e,s,n),e.child;case 6:return t===null&&gt&&((t=n=ot)&&(n=ng(n,e.pendingProps,$t),n!==null?(e.stateNode=n,nt=e,ot=null,t=!0):t=!1),t||Ke(e)),null;case 13:return Jh(t,e,n);case 4:return Rt(e,e.stateNode.containerInfo),s=e.pendingProps,t===null?e.child=zs(e,null,s,n):Le(t,e,s,n),e.child;case 11:return Ih(t,e,e.type,e.pendingProps,n);case 7:return Le(t,e,e.pendingProps,n),e.child;case 8:return Le(t,e,e.pendingProps.children,n),e.child;case 12:return Le(t,e,e.pendingProps.children,n),e.child;case 10:return s=e.pendingProps,Vi(e,e.type,s.value),Le(t,e,s.children,n),e.child;case 9:return o=e.type._context,s=e.pendingProps.children,Ya(e),o=Re(o),s=s(o),e.flags|=1,Le(t,e,s,n),e.child;case 14:return qh(t,e,e.type,e.pendingProps,n);case 15:return Vh(t,e,e.type,e.pendingProps,n);case 19:return $h(t,e,n);case 31:return s=e.pendingProps,n=e.mode,s={mode:s.mode,children:s.children},t===null?(n=hr(s,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=yi(t.child,s),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Yh(t,e,n);case 24:return Ya(e),s=Re(me),t===null?(o=Nu(),o===null&&(o=Xt,c=Du(),o.pooledCache=c,c.refCount++,c!==null&&(o.pooledCacheLanes|=n),o=c),e.memoizedState={parent:s,cache:o},Pu(e),Vi(e,me,o)):((t.lanes&n)!==0&&(Bu(t,e),Al(e,null,null,n),Ll()),o=t.memoizedState,c=e.memoizedState,o.parent!==s?(o={parent:s,cache:s},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),Vi(e,me,s)):(s=c.cache,Vi(e,me,s),s!==o.cache&&Cs(e,[me],n,!0))),Le(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(h(156,e.tag))}function Tn(t){t.flags|=4}function ed(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!um(e)){if(e=bi.current,e!==null&&((zt&4194048)===zt?Yi!==null:(zt&62914560)!==zt&&(zt&536870912)===0||e!==Yi))throw Cl=Ru,kf;t.flags|=8192}}function dr(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?$s():536870912,t.lanes|=e,Bs|=e)}function Rl(t,e){if(!gt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:s.sibling=null}}function te(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,s=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,s|=o.subtreeFlags&65011712,s|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,s|=o.subtreeFlags,s|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=s,t.childLanes=n,e}function Mp(t,e,n){var s=e.pendingProps;switch($(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return te(e),null;case 1:return te(e),null;case 3:return n=e.stateNode,s=null,t!==null&&(s=t.memoizedState.cache),e.memoizedState.cache!==s&&(e.flags|=2048),zi(me),Ci(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Ia(e)?Tn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Vo())),te(e),null;case 26:return n=e.memoizedState,t===null?(Tn(e),n!==null?(te(e),ed(e,n)):(te(e),e.flags&=-16777217)):n?n!==t.memoizedState?(Tn(e),te(e),ed(e,n)):(te(e),e.flags&=-16777217):(t.memoizedProps!==s&&Tn(e),te(e),e.flags&=-16777217),null;case 27:En(e),n=W.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==s&&Tn(e);else{if(!s){if(e.stateNode===null)throw Error(h(166));return te(e),null}t=et.current,Ia(e)?Io(e):(t=em(o,s,n),e.stateNode=t,Tn(e))}return te(e),null;case 5:if(En(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==s&&Tn(e);else{if(!s){if(e.stateNode===null)throw Error(h(166));return te(e),null}if(t=et.current,Ia(e))Io(e);else{switch(o=Lr(W.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof s.is=="string"?o.createElement("select",{is:s.is}):o.createElement("select"),s.multiple?t.multiple=!0:s.size&&(t.size=s.size);break;default:t=typeof s.is=="string"?o.createElement(n,{is:s.is}):o.createElement(n)}}t[ge]=e,t[De]=s;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(Ee(t,n,s),n){case"button":case"input":case"select":case"textarea":t=!!s.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Tn(e)}}return te(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==s&&Tn(e);else{if(typeof s!="string"&&e.stateNode===null)throw Error(h(166));if(t=W.current,Ia(e)){if(t=e.stateNode,n=e.memoizedProps,s=null,o=nt,o!==null)switch(o.tag){case 27:case 5:s=o.memoizedProps}t[ge]=e,t=!!(t.nodeValue===n||s!==null&&s.suppressHydrationWarning===!0||Kd(t.nodeValue,n)),t||Ke(e)}else t=Lr(t).createTextNode(s),t[ge]=e,e.stateNode=t}return te(e),null;case 13:if(s=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ia(e),s!==null&&s.dehydrated!==null){if(t===null){if(!o)throw Error(h(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(h(317));o[ge]=e}else ta(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;te(e),o=!1}else o=Vo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(Sn(e),e):(Sn(e),null)}if(Sn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=s!==null,t=t!==null&&t.memoizedState!==null,n){s=e.child,o=null,s.alternate!==null&&s.alternate.memoizedState!==null&&s.alternate.memoizedState.cachePool!==null&&(o=s.alternate.memoizedState.cachePool.pool);var c=null;s.memoizedState!==null&&s.memoizedState.cachePool!==null&&(c=s.memoizedState.cachePool.pool),c!==o&&(s.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),dr(e,e.updateQueue),te(e),null;case 4:return Ci(),t===null&&kc(e.stateNode.containerInfo),te(e),null;case 10:return zi(e.type),te(e),null;case 19:if(J(pe),o=e.memoizedState,o===null)return te(e),null;if(s=(e.flags&128)!==0,c=o.rendering,c===null)if(s)Rl(o,!1);else{if(ie!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=rr(t),c!==null){for(e.flags|=128,Rl(o,!1),t=c.updateQueue,e.updateQueue=t,dr(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)yl(n,t),n=n.sibling;return K(pe,pe.current&1|2),e.child}t=t.sibling}o.tail!==null&&Ve()>gr&&(e.flags|=128,s=!0,Rl(o,!1),e.lanes=4194304)}else{if(!s)if(t=rr(c),t!==null){if(e.flags|=128,s=!0,t=t.updateQueue,e.updateQueue=t,dr(e,t),Rl(o,!0),o.tail===null&&o.tailMode==="hidden"&&!c.alternate&&!gt)return te(e),null}else 2*Ve()-o.renderingStartTime>gr&&n!==536870912&&(e.flags|=128,s=!0,Rl(o,!1),e.lanes=4194304);o.isBackwards?(c.sibling=e.child,e.child=c):(t=o.last,t!==null?t.sibling=c:e.child=c,o.last=c)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=Ve(),e.sibling=null,t=pe.current,K(pe,s?t&1|2:t&1),e):(te(e),null);case 22:case 23:return Sn(e),Hu(),s=e.memoizedState!==null,t!==null?t.memoizedState!==null!==s&&(e.flags|=8192):s&&(e.flags|=8192),s?(n&536870912)!==0&&(e.flags&128)===0&&(te(e),e.subtreeFlags&6&&(e.flags|=8192)):te(e),n=e.updateQueue,n!==null&&dr(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),s=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(s=e.memoizedState.cachePool.pool),s!==n&&(e.flags|=2048),t!==null&&J(Xa),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),zi(me),te(e),null;case 25:return null;case 30:return null}throw Error(h(156,e.tag))}function Op(t,e){switch($(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return zi(me),Ci(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return En(e),null;case 13:if(Sn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(h(340));ta()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return J(pe),null;case 4:return Ci(),null;case 10:return zi(e.type),null;case 22:case 23:return Sn(e),Hu(),t!==null&&J(Xa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return zi(me),null;case 25:return null;default:return null}}function id(t,e){switch($(e),e.tag){case 3:zi(me),Ci();break;case 26:case 27:case 5:En(e);break;case 4:Ci();break;case 13:Sn(e);break;case 19:J(pe);break;case 10:zi(e.type);break;case 22:case 23:Sn(e),Hu(),t!==null&&J(Xa);break;case 24:zi(me)}}function Pl(t,e){try{var n=e.updateQueue,s=n!==null?n.lastEffect:null;if(s!==null){var o=s.next;n=o;do{if((n.tag&t)===t){s=void 0;var c=n.create,m=n.inst;s=c(),m.destroy=s}n=n.next}while(n!==o)}}catch(v){It(e,e.return,v)}}function ra(t,e,n){try{var s=e.updateQueue,o=s!==null?s.lastEffect:null;if(o!==null){var c=o.next;s=c;do{if((s.tag&t)===t){var m=s.inst,v=m.destroy;if(v!==void 0){m.destroy=void 0,o=e;var T=n,z=v;try{z()}catch(Z){It(o,T,Z)}}}s=s.next}while(s!==c)}}catch(Z){It(e,e.return,Z)}}function nd(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{qf(e,n)}catch(s){It(t,t.return,s)}}}function ad(t,e,n){n.props=Qa(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(s){It(t,e,s)}}function Bl(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var s=t.stateNode;break;case 30:s=t.stateNode;break;default:s=t.stateNode}typeof n=="function"?t.refCleanup=n(s):n.current=s}}catch(o){It(t,e,o)}}function Xi(t,e){var n=t.ref,s=t.refCleanup;if(n!==null)if(typeof s=="function")try{s()}catch(o){It(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){It(t,e,o)}else n.current=null}function sd(t){var e=t.type,n=t.memoizedProps,s=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&s.focus();break t;case"img":n.src?s.src=n.src:n.srcSet&&(s.srcset=n.srcSet)}}catch(o){It(t,t.return,o)}}function mc(t,e,n){try{var s=t.stateNode;Jp(s,t.type,n,e),s[De]=e}catch(o){It(t,t.return,o)}}function ld(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ga(t.type)||t.tag===4}function pc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||ld(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ga(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function gc(t,e,n){var s=t.tag;if(s===5||s===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=wr));else if(s!==4&&(s===27&&ga(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(gc(t,e,n),t=t.sibling;t!==null;)gc(t,e,n),t=t.sibling}function mr(t,e,n){var s=t.tag;if(s===5||s===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(s!==4&&(s===27&&ga(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(mr(t,e,n),t=t.sibling;t!==null;)mr(t,e,n),t=t.sibling}function od(t){var e=t.stateNode,n=t.memoizedProps;try{for(var s=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);Ee(e,s,n),e[ge]=t,e[De]=n}catch(c){It(t,t.return,c)}}var xn=!1,se=!1,_c=!1,rd=typeof WeakSet=="function"?WeakSet:Set,Se=null;function Dp(t,e){if(t=t.containerInfo,jc=zr,t=Ba(t),Ua(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var o=s.anchorOffset,c=s.focusNode;s=s.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break t}var m=0,v=-1,T=-1,z=0,Z=0,G=t,R=null;e:for(;;){for(var B;G!==n||o!==0&&G.nodeType!==3||(v=m+o),G!==c||s!==0&&G.nodeType!==3||(T=m+s),G.nodeType===3&&(m+=G.nodeValue.length),(B=G.firstChild)!==null;)R=G,G=B;for(;;){if(G===t)break e;if(R===n&&++z===o&&(v=m),R===c&&++Z===s&&(T=m),(B=G.nextSibling)!==null)break;G=R,R=G.parentNode}G=B}n=v===-1||T===-1?null:{start:v,end:T}}else n=null}n=n||{start:0,end:0}}else n=null;for(Gc={focusedElem:t,selectionRange:n},zr=!1,Se=e;Se!==null;)if(e=Se,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Se=t;else for(;Se!==null;){switch(e=Se,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,n=e,o=c.memoizedProps,c=c.memoizedState,s=n.stateNode;try{var mt=Qa(n.type,o,n.elementType===n.type);t=s.getSnapshotBeforeUpdate(mt,c),s.__reactInternalSnapshotBeforeUpdate=t}catch(ct){It(n,n.return,ct)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)Vc(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Vc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(h(163))}if(t=e.sibling,t!==null){t.return=e.return,Se=t;break}Se=e.return}}function ud(t,e,n){var s=n.flags;switch(n.tag){case 0:case 11:case 15:ua(t,n),s&4&&Pl(5,n);break;case 1:if(ua(t,n),s&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(m){It(n,n.return,m)}else{var o=Qa(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(m){It(n,n.return,m)}}s&64&&nd(n),s&512&&Bl(n,n.return);break;case 3:if(ua(t,n),s&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{qf(t,e)}catch(m){It(n,n.return,m)}}break;case 27:e===null&&s&4&&od(n);case 26:case 5:ua(t,n),e===null&&s&4&&sd(n),s&512&&Bl(n,n.return);break;case 12:ua(t,n);break;case 13:ua(t,n),s&4&&hd(t,n),s&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=Hp.bind(null,n),ag(t,n))));break;case 22:if(s=n.memoizedState!==null||xn,!s){e=e!==null&&e.memoizedState!==null||se,o=xn;var c=se;xn=s,(se=e)&&!c?ca(t,n,(n.subtreeFlags&8772)!==0):ua(t,n),xn=o,se=c}break;case 30:break;default:ua(t,n)}}function cd(t){var e=t.alternate;e!==null&&(t.alternate=null,cd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ss(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ft=null,Fe=!1;function Cn(t,e,n){for(n=n.child;n!==null;)fd(t,e,n),n=n.sibling}function fd(t,e,n){if(Oe&&typeof Oe.onCommitFiberUnmount=="function")try{Oe.onCommitFiberUnmount(Ji,n)}catch{}switch(n.tag){case 26:se||Xi(n,e),Cn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:se||Xi(n,e);var s=Ft,o=Fe;ga(n.type)&&(Ft=n.stateNode,Fe=!1),Cn(t,e,n),Vl(n.stateNode),Ft=s,Fe=o;break;case 5:se||Xi(n,e);case 6:if(s=Ft,o=Fe,Ft=null,Cn(t,e,n),Ft=s,Fe=o,Ft!==null)if(Fe)try{(Ft.nodeType===9?Ft.body:Ft.nodeName==="HTML"?Ft.ownerDocument.body:Ft).removeChild(n.stateNode)}catch(c){It(n,e,c)}else try{Ft.removeChild(n.stateNode)}catch(c){It(n,e,c)}break;case 18:Ft!==null&&(Fe?(t=Ft,$d(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),$l(t)):$d(Ft,n.stateNode));break;case 4:s=Ft,o=Fe,Ft=n.stateNode.containerInfo,Fe=!0,Cn(t,e,n),Ft=s,Fe=o;break;case 0:case 11:case 14:case 15:se||ra(2,n,e),se||ra(4,n,e),Cn(t,e,n);break;case 1:se||(Xi(n,e),s=n.stateNode,typeof s.componentWillUnmount=="function"&&ad(n,e,s)),Cn(t,e,n);break;case 21:Cn(t,e,n);break;case 22:se=(s=se)||n.memoizedState!==null,Cn(t,e,n),se=s;break;default:Cn(t,e,n)}}function hd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{$l(t)}catch(n){It(e,e.return,n)}}function zp(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new rd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new rd),e;default:throw Error(h(435,t.tag))}}function vc(t,e){var n=zp(t);e.forEach(function(s){var o=jp.bind(null,t,s);n.has(s)||(n.add(s),s.then(o,o))})}function oi(t,e){var n=e.deletions;if(n!==null)for(var s=0;s<n.length;s++){var o=n[s],c=t,m=e,v=m;t:for(;v!==null;){switch(v.tag){case 27:if(ga(v.type)){Ft=v.stateNode,Fe=!1;break t}break;case 5:Ft=v.stateNode,Fe=!1;break t;case 3:case 4:Ft=v.stateNode.containerInfo,Fe=!0;break t}v=v.return}if(Ft===null)throw Error(h(160));fd(c,m,o),Ft=null,Fe=!1,c=o.alternate,c!==null&&(c.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)dd(e,t),e=e.sibling}var Ni=null;function dd(t,e){var n=t.alternate,s=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:oi(e,t),ri(t),s&4&&(ra(3,t,t.return),Pl(3,t),ra(5,t,t.return));break;case 1:oi(e,t),ri(t),s&512&&(se||n===null||Xi(n,n.return)),s&64&&xn&&(t=t.updateQueue,t!==null&&(s=t.callbacks,s!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?s:n.concat(s))));break;case 26:var o=Ni;if(oi(e,t),ri(t),s&512&&(se||n===null||Xi(n,n.return)),s&4){var c=n!==null?n.memoizedState:null;if(s=t.memoizedState,n===null)if(s===null)if(t.stateNode===null){t:{s=t.type,n=t.memoizedProps,o=o.ownerDocument||o;e:switch(s){case"title":c=o.getElementsByTagName("title")[0],(!c||c[Nn]||c[ge]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=o.createElement(s),o.head.insertBefore(c,o.querySelector("head > title"))),Ee(c,s,n),c[ge]=t,ue(c),s=c;break t;case"link":var m=om("link","href",o).get(s+(n.href||""));if(m){for(var v=0;v<m.length;v++)if(c=m[v],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){m.splice(v,1);break e}}c=o.createElement(s),Ee(c,s,n),o.head.appendChild(c);break;case"meta":if(m=om("meta","content",o).get(s+(n.content||""))){for(v=0;v<m.length;v++)if(c=m[v],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){m.splice(v,1);break e}}c=o.createElement(s),Ee(c,s,n),o.head.appendChild(c);break;default:throw Error(h(468,s))}c[ge]=t,ue(c),s=c}t.stateNode=s}else rm(o,t.type,t.stateNode);else t.stateNode=lm(o,s,t.memoizedProps);else c!==s?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,s===null?rm(o,t.type,t.stateNode):lm(o,s,t.memoizedProps)):s===null&&t.stateNode!==null&&mc(t,t.memoizedProps,n.memoizedProps)}break;case 27:oi(e,t),ri(t),s&512&&(se||n===null||Xi(n,n.return)),n!==null&&s&4&&mc(t,t.memoizedProps,n.memoizedProps);break;case 5:if(oi(e,t),ri(t),s&512&&(se||n===null||Xi(n,n.return)),t.flags&32){o=t.stateNode;try{mi(o,"")}catch(B){It(t,t.return,B)}}s&4&&t.stateNode!=null&&(o=t.memoizedProps,mc(t,o,n!==null?n.memoizedProps:o)),s&1024&&(_c=!0);break;case 6:if(oi(e,t),ri(t),s&4){if(t.stateNode===null)throw Error(h(162));s=t.memoizedProps,n=t.stateNode;try{n.nodeValue=s}catch(B){It(t,t.return,B)}}break;case 3:if(Mr=null,o=Ni,Ni=Ar(e.containerInfo),oi(e,t),Ni=o,ri(t),s&4&&n!==null&&n.memoizedState.isDehydrated)try{$l(e.containerInfo)}catch(B){It(t,t.return,B)}_c&&(_c=!1,md(t));break;case 4:s=Ni,Ni=Ar(t.stateNode.containerInfo),oi(e,t),ri(t),Ni=s;break;case 12:oi(e,t),ri(t);break;case 13:oi(e,t),ri(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Cc=Ve()),s&4&&(s=t.updateQueue,s!==null&&(t.updateQueue=null,vc(t,s)));break;case 22:o=t.memoizedState!==null;var T=n!==null&&n.memoizedState!==null,z=xn,Z=se;if(xn=z||o,se=Z||T,oi(e,t),se=Z,xn=z,ri(t),s&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(n===null||T||xn||se||Fa(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){T=n=e;try{if(c=T.stateNode,o)m=c.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none";else{v=T.stateNode;var G=T.memoizedProps.style,R=G!=null&&G.hasOwnProperty("display")?G.display:null;v.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(B){It(T,T.return,B)}}}else if(e.tag===6){if(n===null){T=e;try{T.stateNode.nodeValue=o?"":T.memoizedProps}catch(B){It(T,T.return,B)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}s&4&&(s=t.updateQueue,s!==null&&(n=s.retryQueue,n!==null&&(s.retryQueue=null,vc(t,n))));break;case 19:oi(e,t),ri(t),s&4&&(s=t.updateQueue,s!==null&&(t.updateQueue=null,vc(t,s)));break;case 30:break;case 21:break;default:oi(e,t),ri(t)}}function ri(t){var e=t.flags;if(e&2){try{for(var n,s=t.return;s!==null;){if(ld(s)){n=s;break}s=s.return}if(n==null)throw Error(h(160));switch(n.tag){case 27:var o=n.stateNode,c=pc(t);mr(t,c,o);break;case 5:var m=n.stateNode;n.flags&32&&(mi(m,""),n.flags&=-33);var v=pc(t);mr(t,v,m);break;case 3:case 4:var T=n.stateNode.containerInfo,z=pc(t);gc(t,z,T);break;default:throw Error(h(161))}}catch(Z){It(t,t.return,Z)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function md(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;md(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ua(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)ud(t,e.alternate,e),e=e.sibling}function Fa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ra(4,e,e.return),Fa(e);break;case 1:Xi(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&ad(e,e.return,n),Fa(e);break;case 27:Vl(e.stateNode);case 26:case 5:Xi(e,e.return),Fa(e);break;case 22:e.memoizedState===null&&Fa(e);break;case 30:Fa(e);break;default:Fa(e)}t=t.sibling}}function ca(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var s=e.alternate,o=t,c=e,m=c.flags;switch(c.tag){case 0:case 11:case 15:ca(o,c,n),Pl(4,c);break;case 1:if(ca(o,c,n),s=c,o=s.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(z){It(s,s.return,z)}if(s=c,o=s.updateQueue,o!==null){var v=s.stateNode;try{var T=o.shared.hiddenCallbacks;if(T!==null)for(o.shared.hiddenCallbacks=null,o=0;o<T.length;o++)If(T[o],v)}catch(z){It(s,s.return,z)}}n&&m&64&&nd(c),Bl(c,c.return);break;case 27:od(c);case 26:case 5:ca(o,c,n),n&&s===null&&m&4&&sd(c),Bl(c,c.return);break;case 12:ca(o,c,n);break;case 13:ca(o,c,n),n&&m&4&&hd(o,c);break;case 22:c.memoizedState===null&&ca(o,c,n),Bl(c,c.return);break;case 30:break;default:ca(o,c,n)}e=e.sibling}}function yc(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&bl(n))}function Sc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&bl(t))}function Ki(t,e,n,s){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)pd(t,e,n,s),e=e.sibling}function pd(t,e,n,s){var o=e.flags;switch(e.tag){case 0:case 11:case 15:Ki(t,e,n,s),o&2048&&Pl(9,e);break;case 1:Ki(t,e,n,s);break;case 3:Ki(t,e,n,s),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&bl(t)));break;case 12:if(o&2048){Ki(t,e,n,s),t=e.stateNode;try{var c=e.memoizedProps,m=c.id,v=c.onPostCommit;typeof v=="function"&&v(m,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(T){It(e,e.return,T)}}else Ki(t,e,n,s);break;case 13:Ki(t,e,n,s);break;case 23:break;case 22:c=e.stateNode,m=e.alternate,e.memoizedState!==null?c._visibility&2?Ki(t,e,n,s):Ul(t,e):c._visibility&2?Ki(t,e,n,s):(c._visibility|=2,Ns(t,e,n,s,(e.subtreeFlags&10256)!==0)),o&2048&&yc(m,e);break;case 24:Ki(t,e,n,s),o&2048&&Sc(e.alternate,e);break;default:Ki(t,e,n,s)}}function Ns(t,e,n,s,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,m=e,v=n,T=s,z=m.flags;switch(m.tag){case 0:case 11:case 15:Ns(c,m,v,T,o),Pl(8,m);break;case 23:break;case 22:var Z=m.stateNode;m.memoizedState!==null?Z._visibility&2?Ns(c,m,v,T,o):Ul(c,m):(Z._visibility|=2,Ns(c,m,v,T,o)),o&&z&2048&&yc(m.alternate,m);break;case 24:Ns(c,m,v,T,o),o&&z&2048&&Sc(m.alternate,m);break;default:Ns(c,m,v,T,o)}e=e.sibling}}function Ul(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,s=e,o=s.flags;switch(s.tag){case 22:Ul(n,s),o&2048&&yc(s.alternate,s);break;case 24:Ul(n,s),o&2048&&Sc(s.alternate,s);break;default:Ul(n,s)}e=e.sibling}}var kl=8192;function Rs(t){if(t.subtreeFlags&kl)for(t=t.child;t!==null;)gd(t),t=t.sibling}function gd(t){switch(t.tag){case 26:Rs(t),t.flags&kl&&t.memoizedState!==null&&_g(Ni,t.memoizedState,t.memoizedProps);break;case 5:Rs(t);break;case 3:case 4:var e=Ni;Ni=Ar(t.stateNode.containerInfo),Rs(t),Ni=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=kl,kl=16777216,Rs(t),kl=e):Rs(t));break;default:Rs(t)}}function _d(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Zl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var s=e[n];Se=s,yd(s,t)}_d(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)vd(t),t=t.sibling}function vd(t){switch(t.tag){case 0:case 11:case 15:Zl(t),t.flags&2048&&ra(9,t,t.return);break;case 3:Zl(t);break;case 12:Zl(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,pr(t)):Zl(t);break;default:Zl(t)}}function pr(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var s=e[n];Se=s,yd(s,t)}_d(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ra(8,e,e.return),pr(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,pr(e));break;default:pr(e)}t=t.sibling}}function yd(t,e){for(;Se!==null;){var n=Se;switch(n.tag){case 0:case 11:case 15:ra(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var s=n.memoizedState.cachePool.pool;s!=null&&s.refCount++}break;case 24:bl(n.memoizedState.cache)}if(s=n.child,s!==null)s.return=n,Se=s;else t:for(n=t;Se!==null;){s=Se;var o=s.sibling,c=s.return;if(cd(s),s===n){Se=null;break t}if(o!==null){o.return=c,Se=o;break t}Se=c}}}var Np={getCacheForType:function(t){var e=Re(me),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Rp=typeof WeakMap=="function"?WeakMap:Map,Ut=0,Xt=null,At=null,zt=0,kt=0,ui=null,fa=!1,Ps=!1,bc=!1,wn=0,ie=0,ha=0,Ja=0,Tc=0,Ti=0,Bs=0,Hl=null,Je=null,xc=!1,Cc=0,gr=1/0,_r=null,da=null,Ae=0,ma=null,Us=null,ks=0,wc=0,Lc=null,Sd=null,jl=0,Ac=null;function ci(){if((Ut&2)!==0&&zt!==0)return zt&-zt;if(P.T!==null){var t=ws;return t!==0?t:Rc()}return vo()}function bd(){Ti===0&&(Ti=(zt&536870912)===0||gt?po():536870912);var t=bi.current;return t!==null&&(t.flags|=32),Ti}function fi(t,e,n){(t===Xt&&(kt===2||kt===9)||t.cancelPendingCommit!==null)&&(Zs(t,0),pa(t,zt,Ti,!1)),Dn(t,n),((Ut&2)===0||t!==Xt)&&(t===Xt&&((Ut&2)===0&&(Ja|=n),ie===4&&pa(t,zt,Ti,!1)),Qi(t))}function Td(t,e,n){if((Ut&6)!==0)throw Error(h(327));var s=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Li(t,e),o=s?Up(t,e):Oc(t,e,!0),c=s;do{if(o===0){Ps&&!s&&pa(t,e,0,!1);break}else{if(n=t.current.alternate,c&&!Pp(n)){o=Oc(t,e,!1),c=!1;continue}if(o===2){if(c=e,t.errorRecoveryDisabledLanes&c)var m=0;else m=t.pendingLanes&-536870913,m=m!==0?m:m&536870912?536870912:0;if(m!==0){e=m;t:{var v=t;o=Hl;var T=v.current.memoizedState.isDehydrated;if(T&&(Zs(v,m).flags|=256),m=Oc(v,m,!1),m!==2){if(bc&&!T){v.errorRecoveryDisabledLanes|=c,Ja|=c,o=4;break t}c=Je,Je=o,c!==null&&(Je===null?Je=c:Je.push.apply(Je,c))}o=m}if(c=!1,o!==2)continue}}if(o===1){Zs(t,0),pa(t,e,0,!0);break}t:{switch(s=t,c=o,c){case 0:case 1:throw Error(h(345));case 4:if((e&4194048)!==e)break;case 6:pa(s,e,Ti,!fa);break t;case 2:Je=null;break;case 3:case 5:break;default:throw Error(h(329))}if((e&62914560)===e&&(o=Cc+300-Ve(),10<o)){if(pa(s,e,Ti,!fa),ns(s,0,!0)!==0)break t;s.timeoutHandle=Jd(xd.bind(null,s,n,Je,_r,xc,e,Ti,Ja,Bs,fa,c,2,-0,0),o);break t}xd(s,n,Je,_r,xc,e,Ti,Ja,Bs,fa,c,0,-0,0)}}break}while(!0);Qi(t)}function xd(t,e,n,s,o,c,m,v,T,z,Z,G,R,B){if(t.timeoutHandle=-1,G=e.subtreeFlags,(G&8192||(G&16785408)===16785408)&&(Kl={stylesheets:null,count:0,unsuspend:gg},gd(e),G=vg(),G!==null)){t.cancelPendingCommit=G(Od.bind(null,t,e,c,n,s,o,m,v,T,Z,1,R,B)),pa(t,c,m,!z);return}Od(t,e,c,n,s,o,m,v,T)}function Pp(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var s=0;s<n.length;s++){var o=n[s],c=o.getSnapshot;o=o.value;try{if(!Ze(c(),o))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function pa(t,e,n,s){e&=~Tc,e&=~Ja,t.suspendedLanes|=e,t.pingedLanes&=~e,s&&(t.warmLanes|=e),s=t.expirationTimes;for(var o=e;0<o;){var c=31-Ue(o),m=1<<c;s[c]=-1,o&=~m}n!==0&&go(t,n,e)}function vr(){return(Ut&6)===0?(Gl(0),!1):!0}function Ec(){if(At!==null){if(kt===0)var t=At.return;else t=At,Di=Si=null,Vu(t),Ds=null,zl=0,t=At;for(;t!==null;)id(t.alternate,t),t=t.return;At=null}}function Zs(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,$p(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Ec(),Xt=t,At=n=yi(t.current,null),zt=e,kt=0,ui=null,fa=!1,Ps=Li(t,e),bc=!1,Bs=Ti=Tc=Ja=ha=ie=0,Je=Hl=null,xc=!1,(e&8)!==0&&(e|=e&32);var s=t.entangledLanes;if(s!==0)for(t=t.entanglements,s&=e;0<s;){var o=31-Ue(s),c=1<<o;e|=t[o],s&=~c}return wn=e,Za(),n}function Cd(t,e){Ct=null,P.H=sr,e===xl||e===Qo?(e=jf(),kt=3):e===kf?(e=jf(),kt=4):kt=e===Gh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ui=e,At===null&&(ie=1,cr(t,je(e,t.current)))}function wd(){var t=P.H;return P.H=sr,t===null?sr:t}function Ld(){var t=P.A;return P.A=Np,t}function Mc(){ie=4,fa||(zt&4194048)!==zt&&bi.current!==null||(Ps=!0),(ha&134217727)===0&&(Ja&134217727)===0||Xt===null||pa(Xt,zt,Ti,!1)}function Oc(t,e,n){var s=Ut;Ut|=2;var o=wd(),c=Ld();(Xt!==t||zt!==e)&&(_r=null,Zs(t,e)),e=!1;var m=ie;t:do try{if(kt!==0&&At!==null){var v=At,T=ui;switch(kt){case 8:Ec(),m=6;break t;case 3:case 2:case 9:case 6:bi.current===null&&(e=!0);var z=kt;if(kt=0,ui=null,Hs(t,v,T,z),n&&Ps){m=0;break t}break;default:z=kt,kt=0,ui=null,Hs(t,v,T,z)}}Bp(),m=ie;break}catch(Z){Cd(t,Z)}while(!0);return e&&t.shellSuspendCounter++,Di=Si=null,Ut=s,P.H=o,P.A=c,At===null&&(Xt=null,zt=0,Za()),m}function Bp(){for(;At!==null;)Ad(At)}function Up(t,e){var n=Ut;Ut|=2;var s=wd(),o=Ld();Xt!==t||zt!==e?(_r=null,gr=Ve()+500,Zs(t,e)):Ps=Li(t,e);t:do try{if(kt!==0&&At!==null){e=At;var c=ui;e:switch(kt){case 1:kt=0,ui=null,Hs(t,e,c,1);break;case 2:case 9:if(Zf(c)){kt=0,ui=null,Ed(e);break}e=function(){kt!==2&&kt!==9||Xt!==t||(kt=7),Qi(t)},c.then(e,e);break t;case 3:kt=7;break t;case 4:kt=5;break t;case 7:Zf(c)?(kt=0,ui=null,Ed(e)):(kt=0,ui=null,Hs(t,e,c,7));break;case 5:var m=null;switch(At.tag){case 26:m=At.memoizedState;case 5:case 27:var v=At;if(!m||um(m)){kt=0,ui=null;var T=v.sibling;if(T!==null)At=T;else{var z=v.return;z!==null?(At=z,yr(z)):At=null}break e}}kt=0,ui=null,Hs(t,e,c,5);break;case 6:kt=0,ui=null,Hs(t,e,c,6);break;case 8:Ec(),ie=6;break t;default:throw Error(h(462))}}kp();break}catch(Z){Cd(t,Z)}while(!0);return Di=Si=null,P.H=s,P.A=o,Ut=n,At!==null?0:(Xt=null,zt=0,Za(),ie)}function kp(){for(;At!==null&&!ro();)Ad(At)}function Ad(t){var e=td(t.alternate,t,wn);t.memoizedProps=t.pendingProps,e===null?yr(t):At=e}function Ed(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Kh(n,e,e.pendingProps,e.type,void 0,zt);break;case 11:e=Kh(n,e,e.pendingProps,e.type.render,e.ref,zt);break;case 5:Vu(e);default:id(n,e),e=At=yl(e,wn),e=td(n,e,wn)}t.memoizedProps=t.pendingProps,e===null?yr(t):At=e}function Hs(t,e,n,s){Di=Si=null,Vu(e),Ds=null,zl=0;var o=e.return;try{if(Ap(t,o,e,n,zt)){ie=1,cr(t,je(n,t.current)),At=null;return}}catch(c){if(o!==null)throw At=o,c;ie=1,cr(t,je(n,t.current)),At=null;return}e.flags&32768?(gt||s===1?t=!0:Ps||(zt&536870912)!==0?t=!1:(fa=t=!0,(s===2||s===9||s===3||s===6)&&(s=bi.current,s!==null&&s.tag===13&&(s.flags|=16384))),Md(e,t)):yr(e)}function yr(t){var e=t;do{if((e.flags&32768)!==0){Md(e,fa);return}t=e.return;var n=Mp(e.alternate,e,wn);if(n!==null){At=n;return}if(e=e.sibling,e!==null){At=e;return}At=e=t}while(e!==null);ie===0&&(ie=5)}function Md(t,e){do{var n=Op(t.alternate,t);if(n!==null){n.flags&=32767,At=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){At=t;return}At=t=n}while(t!==null);ie=6,At=null}function Od(t,e,n,s,o,c,m,v,T){t.cancelPendingCommit=null;do Sr();while(Ae!==0);if((Ut&6)!==0)throw Error(h(327));if(e!==null){if(e===t.current)throw Error(h(177));if(c=e.lanes|e.childLanes,c|=li,hu(t,n,c,m,v,T),t===Xt&&(At=Xt=null,zt=0),Us=e,ma=t,ks=n,wc=c,Lc=o,Sd=s,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Gp(Mn,function(){return Pd(),null})):(t.callbackNode=null,t.callbackPriority=0),s=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||s){s=P.T,P.T=null,o=Q.p,Q.p=2,m=Ut,Ut|=4;try{Dp(t,e,n)}finally{Ut=m,Q.p=o,P.T=s}}Ae=1,Dd(),zd(),Nd()}}function Dd(){if(Ae===1){Ae=0;var t=ma,e=Us,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=P.T,P.T=null;var s=Q.p;Q.p=2;var o=Ut;Ut|=4;try{dd(e,t);var c=Gc,m=Ba(t.containerInfo),v=c.focusedElem,T=c.selectionRange;if(m!==v&&v&&v.ownerDocument&&_s(v.ownerDocument.documentElement,v)){if(T!==null&&Ua(v)){var z=T.start,Z=T.end;if(Z===void 0&&(Z=z),"selectionStart"in v)v.selectionStart=z,v.selectionEnd=Math.min(Z,v.value.length);else{var G=v.ownerDocument||document,R=G&&G.defaultView||window;if(R.getSelection){var B=R.getSelection(),mt=v.textContent.length,ct=Math.min(T.start,mt),jt=T.end===void 0?ct:Math.min(T.end,mt);!B.extend&&ct>jt&&(m=jt,jt=ct,ct=m);var w=_l(v,ct),C=_l(v,jt);if(w&&C&&(B.rangeCount!==1||B.anchorNode!==w.node||B.anchorOffset!==w.offset||B.focusNode!==C.node||B.focusOffset!==C.offset)){var D=G.createRange();D.setStart(w.node,w.offset),B.removeAllRanges(),ct>jt?(B.addRange(D),B.extend(C.node,C.offset)):(D.setEnd(C.node,C.offset),B.addRange(D))}}}}for(G=[],B=v;B=B.parentNode;)B.nodeType===1&&G.push({element:B,left:B.scrollLeft,top:B.scrollTop});for(typeof v.focus=="function"&&v.focus(),v=0;v<G.length;v++){var H=G[v];H.element.scrollLeft=H.left,H.element.scrollTop=H.top}}zr=!!jc,Gc=jc=null}finally{Ut=o,Q.p=s,P.T=n}}t.current=e,Ae=2}}function zd(){if(Ae===2){Ae=0;var t=ma,e=Us,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=P.T,P.T=null;var s=Q.p;Q.p=2;var o=Ut;Ut|=4;try{ud(t,e.alternate,e)}finally{Ut=o,Q.p=s,P.T=n}}Ae=3}}function Nd(){if(Ae===4||Ae===3){Ae=0,uo();var t=ma,e=Us,n=ks,s=Sd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ae=5:(Ae=0,Us=ma=null,Rd(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(da=null),el(n),e=e.stateNode,Oe&&typeof Oe.onCommitFiberRoot=="function")try{Oe.onCommitFiberRoot(Ji,e,void 0,(e.current.flags&128)===128)}catch{}if(s!==null){e=P.T,o=Q.p,Q.p=2,P.T=null;try{for(var c=t.onRecoverableError,m=0;m<s.length;m++){var v=s[m];c(v.value,{componentStack:v.stack})}}finally{P.T=e,Q.p=o}}(ks&3)!==0&&Sr(),Qi(t),o=t.pendingLanes,(n&4194090)!==0&&(o&42)!==0?t===Ac?jl++:(jl=0,Ac=t):jl=0,Gl(0)}}function Rd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,bl(e)))}function Sr(t){return Dd(),zd(),Nd(),Pd()}function Pd(){if(Ae!==5)return!1;var t=ma,e=wc;wc=0;var n=el(ks),s=P.T,o=Q.p;try{Q.p=32>n?32:n,P.T=null,n=Lc,Lc=null;var c=ma,m=ks;if(Ae=0,Us=ma=null,ks=0,(Ut&6)!==0)throw Error(h(331));var v=Ut;if(Ut|=4,vd(c.current),pd(c,c.current,m,n),Ut=v,Gl(0,!1),Oe&&typeof Oe.onPostCommitFiberRoot=="function")try{Oe.onPostCommitFiberRoot(Ji,c)}catch{}return!0}finally{Q.p=o,P.T=s,Rd(t,e)}}function Bd(t,e,n){e=je(n,e),e=sc(t.stateNode,e,2),t=aa(t,e,2),t!==null&&(Dn(t,2),Qi(t))}function It(t,e,n){if(t.tag===3)Bd(t,t,n);else for(;e!==null;){if(e.tag===3){Bd(e,t,n);break}else if(e.tag===1){var s=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(da===null||!da.has(s))){t=je(n,t),n=Hh(2),s=aa(e,n,2),s!==null&&(jh(n,s,e,t),Dn(s,2),Qi(s));break}}e=e.return}}function Dc(t,e,n){var s=t.pingCache;if(s===null){s=t.pingCache=new Rp;var o=new Set;s.set(e,o)}else o=s.get(e),o===void 0&&(o=new Set,s.set(e,o));o.has(n)||(bc=!0,o.add(n),t=Zp.bind(null,t,e,n),e.then(t,t))}function Zp(t,e,n){var s=t.pingCache;s!==null&&s.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Xt===t&&(zt&n)===n&&(ie===4||ie===3&&(zt&62914560)===zt&&300>Ve()-Cc?(Ut&2)===0&&Zs(t,0):Tc|=n,Bs===zt&&(Bs=0)),Qi(t)}function Ud(t,e){e===0&&(e=$s()),t=Wn(t,e),t!==null&&(Dn(t,e),Qi(t))}function Hp(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Ud(t,n)}function jp(t,e){var n=0;switch(t.tag){case 13:var s=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:s=t.stateNode;break;case 22:s=t.stateNode._retryCache;break;default:throw Error(h(314))}s!==null&&s.delete(e),Ud(t,n)}function Gp(t,e){return xa(t,e)}var br=null,js=null,zc=!1,Tr=!1,Nc=!1,Wa=0;function Qi(t){t!==js&&t.next===null&&(js===null?br=js=t:js=js.next=t),Tr=!0,zc||(zc=!0,qp())}function Gl(t,e){if(!Nc&&Tr){Nc=!0;do for(var n=!1,s=br;s!==null;){if(t!==0){var o=s.pendingLanes;if(o===0)var c=0;else{var m=s.suspendedLanes,v=s.pingedLanes;c=(1<<31-Ue(42|t)+1)-1,c&=o&~(m&~v),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,jd(s,c))}else c=zt,c=ns(s,s===Xt?c:0,s.cancelPendingCommit!==null||s.timeoutHandle!==-1),(c&3)===0||Li(s,c)||(n=!0,jd(s,c));s=s.next}while(n);Nc=!1}}function Ip(){kd()}function kd(){Tr=zc=!1;var t=0;Wa!==0&&(Wp()&&(t=Wa),Wa=0);for(var e=Ve(),n=null,s=br;s!==null;){var o=s.next,c=Zd(s,e);c===0?(s.next=null,n===null?br=o:n.next=o,o===null&&(js=n)):(n=s,(t!==0||(c&3)!==0)&&(Tr=!0)),s=o}Gl(t)}function Zd(t,e){for(var n=t.suspendedLanes,s=t.pingedLanes,o=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var m=31-Ue(c),v=1<<m,T=o[m];T===-1?((v&n)===0||(v&s)!==0)&&(o[m]=fu(v,e)):T<=e&&(t.expiredLanes|=v),c&=~v}if(e=Xt,n=zt,n=ns(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),s=t.callbackNode,n===0||t===e&&(kt===2||kt===9)||t.cancelPendingCommit!==null)return s!==null&&s!==null&&Fs(s),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||Li(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(s!==null&&Fs(s),el(n)){case 2:case 8:n=Js;break;case 32:n=Mn;break;case 268435456:n=Ws;break;default:n=Mn}return s=Hd.bind(null,t),n=xa(n,s),t.callbackPriority=e,t.callbackNode=n,e}return s!==null&&s!==null&&Fs(s),t.callbackPriority=2,t.callbackNode=null,2}function Hd(t,e){if(Ae!==0&&Ae!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Sr()&&t.callbackNode!==n)return null;var s=zt;return s=ns(t,t===Xt?s:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),s===0?null:(Td(t,s,e),Zd(t,Ve()),t.callbackNode!=null&&t.callbackNode===n?Hd.bind(null,t):null)}function jd(t,e){if(Sr())return null;Td(t,e,!0)}function qp(){tg(function(){(Ut&6)!==0?xa(co,Ip):kd()})}function Rc(){return Wa===0&&(Wa=po()),Wa}function Gd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Zn(""+t)}function Id(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Vp(t,e,n,s,o){if(e==="submit"&&n&&n.stateNode===o){var c=Gd((o[De]||null).action),m=s.submitter;m&&(e=(e=m[De]||null)?Gd(e.formAction):m.getAttribute("formAction"),e!==null&&(c=e,m=null));var v=new Da("action","action",null,s,o);t.push({event:v,listeners:[{instance:null,listener:function(){if(s.defaultPrevented){if(Wa!==0){var T=m?Id(o,m):new FormData(o);tc(n,{pending:!0,data:T,method:o.method,action:c},null,T)}}else typeof c=="function"&&(v.preventDefault(),T=m?Id(o,m):new FormData(o),tc(n,{pending:!0,data:T,method:o.method,action:c},c,T))},currentTarget:o}]})}}for(var Pc=0;Pc<mn.length;Pc++){var Bc=mn[Pc],Yp=Bc.toLowerCase(),Xp=Bc[0].toUpperCase()+Bc.slice(1);si(Yp,"on"+Xp)}si(Ho,"onAnimationEnd"),si(ai,"onAnimationIteration"),si(ka,"onAnimationStart"),si("dblclick","onDoubleClick"),si("focusin","onFocus"),si("focusout","onBlur"),si(Mu,"onTransitionRun"),si(bs,"onTransitionStart"),si(Ou,"onTransitionCancel"),si(vl,"onTransitionEnd"),en("onMouseEnter",["mouseout","mouseover"]),en("onMouseLeave",["mouseout","mouseover"]),en("onPointerEnter",["pointerout","pointerover"]),en("onPointerLeave",["pointerout","pointerover"]),tn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),tn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),tn("onBeforeInput",["compositionend","keypress","textInput","paste"]),tn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),tn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),tn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Il="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Kp=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Il));function qd(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var s=t[n],o=s.event;s=s.listeners;t:{var c=void 0;if(e)for(var m=s.length-1;0<=m;m--){var v=s[m],T=v.instance,z=v.currentTarget;if(v=v.listener,T!==c&&o.isPropagationStopped())break t;c=v,o.currentTarget=z;try{c(o)}catch(Z){ur(Z)}o.currentTarget=null,c=T}else for(m=0;m<s.length;m++){if(v=s[m],T=v.instance,z=v.currentTarget,v=v.listener,T!==c&&o.isPropagationStopped())break t;c=v,o.currentTarget=z;try{c(o)}catch(Z){ur(Z)}o.currentTarget=null,c=T}}}}function Et(t,e){var n=e[Ye];n===void 0&&(n=e[Ye]=new Set);var s=t+"__bubble";n.has(s)||(Vd(e,t,2,!1),n.add(s))}function Uc(t,e,n){var s=0;e&&(s|=4),Vd(n,t,s,e)}var xr="_reactListening"+Math.random().toString(36).slice(2);function kc(t){if(!t[xr]){t[xr]=!0,So.forEach(function(n){n!=="selectionchange"&&(Kp.has(n)||Uc(n,!1,t),Uc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[xr]||(e[xr]=!0,Uc("selectionchange",!1,e))}}function Vd(t,e,n,s){switch(pm(e)){case 2:var o=bg;break;case 8:o=Tg;break;default:o=Wc}n=o.bind(null,e,n,t),o=void 0,!Oa||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),s?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function Zc(t,e,n,s,o){var c=s;if((e&1)===0&&(e&2)===0&&s!==null)t:for(;;){if(s===null)return;var m=s.tag;if(m===3||m===4){var v=s.stateNode.containerInfo;if(v===o)break;if(m===4)for(m=s.return;m!==null;){var T=m.tag;if((T===3||T===4)&&m.stateNode.containerInfo===o)return;m=m.return}for(;v!==null;){if(m=ki(v),m===null)return;if(T=m.tag,T===5||T===6||T===26||T===27){s=c=m;continue t}v=v.parentNode}}s=s.return}ti(function(){var z=c,Z=Hn(n),G=[];t:{var R=jo.get(t);if(R!==void 0){var B=Da,mt=t;switch(t){case"keypress":if(ee(n)===0)break t;case"keydown":case"keyup":B=bu;break;case"focusin":mt="focus",B=rl;break;case"focusout":mt="blur",B=rl;break;case"beforeblur":case"afterblur":B=rl;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":B=In;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":B=gu;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":B=xu;break;case Ho:case ai:case ka:B=_u;break;case vl:B=Cu;break;case"scroll":case"scrollend":B=pu;break;case"wheel":B=Do;break;case"copy":case"cut":case"paste":B=ul;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":B=fl;break;case"toggle":case"beforetoggle":B=cn}var ct=(e&4)!==0,jt=!ct&&(t==="scroll"||t==="scrollend"),w=ct?R!==null?R+"Capture":null:R;ct=[];for(var C=z,D;C!==null;){var H=C;if(D=H.stateNode,H=H.tag,H!==5&&H!==26&&H!==27||D===null||w===null||(H=Pt(C,w),H!=null&&ct.push(ql(C,H,D))),jt)break;C=C.return}0<ct.length&&(R=new B(R,mt,null,n,Z),G.push({event:R,listeners:ct}))}}if((e&7)===0){t:{if(R=t==="mouseover"||t==="pointerover",B=t==="mouseout"||t==="pointerout",R&&n!==Ma&&(mt=n.relatedTarget||n.fromElement)&&(ki(mt)||mt[zn]))break t;if((B||R)&&(R=Z.window===Z?Z:(R=Z.ownerDocument)?R.defaultView||R.parentWindow:window,B?(mt=n.relatedTarget||n.toElement,B=z,mt=mt?ki(mt):null,mt!==null&&(jt=y(mt),ct=mt.tag,mt!==jt||ct!==5&&ct!==27&&ct!==6)&&(mt=null)):(B=null,mt=z),B!==mt)){if(ct=In,H="onMouseLeave",w="onMouseEnter",C="mouse",(t==="pointerout"||t==="pointerover")&&(ct=fl,H="onPointerLeave",w="onPointerEnter",C="pointer"),jt=B==null?R:di(B),D=mt==null?R:di(mt),R=new ct(H,C+"leave",B,n,Z),R.target=jt,R.relatedTarget=D,H=null,ki(Z)===z&&(ct=new ct(w,C+"enter",mt,n,Z),ct.target=D,ct.relatedTarget=jt,H=ct),jt=H,B&&mt)e:{for(ct=B,w=mt,C=0,D=ct;D;D=Gs(D))C++;for(D=0,H=w;H;H=Gs(H))D++;for(;0<C-D;)ct=Gs(ct),C--;for(;0<D-C;)w=Gs(w),D--;for(;C--;){if(ct===w||w!==null&&ct===w.alternate)break e;ct=Gs(ct),w=Gs(w)}ct=null}else ct=null;B!==null&&Yd(G,R,B,ct,!1),mt!==null&&jt!==null&&Yd(G,jt,mt,ct,!0)}}t:{if(R=z?di(z):window,B=R.nodeName&&R.nodeName.toLowerCase(),B==="select"||B==="input"&&R.type==="file")var at=Yn;else if(hn(R))if(ml)at=Eu;else{at=Au;var Lt=gl}else B=R.nodeName,!B||B.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?z&&Ea(z.elementType)&&(at=Yn):at=Oi;if(at&&(at=at(t,z))){Bo(G,at,n,Z);break t}Lt&&Lt(t,R,z),t==="focusout"&&z&&R.type==="number"&&z.memoizedProps.value!=null&&Un(R,"number",R.value)}switch(Lt=z?di(z):window,t){case"focusin":(hn(Lt)||Lt.contentEditable==="true")&&(ni=Lt,Qn=z,dn=null);break;case"focusout":dn=Qn=ni=null;break;case"mousedown":ys=!0;break;case"contextmenu":case"mouseup":case"dragend":ys=!1,ko(G,n,Z);break;case"selectionchange":if(vs)break;case"keydown":case"keyup":ko(G,n,Z)}var ut;if(fn)t:{switch(t){case"compositionstart":var ft="onCompositionStart";break t;case"compositionend":ft="onCompositionEnd";break t;case"compositionupdate":ft="onCompositionUpdate";break t}ft=void 0}else qn?ps(t,n)&&(ft="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(ft="onCompositionStart");ft&&(dl&&n.locale!=="ko"&&(qn||ft!=="onCompositionStart"?ft==="onCompositionEnd"&&qn&&(ut=Gn()):(gi=Z,Ei="value"in gi?gi.value:gi.textContent,qn=!0)),Lt=Cr(z,ft),0<Lt.length&&(ft=new ei(ft,t,null,n,Z),G.push({event:ft,listeners:Lt}),ut?ft.data=ut:(ut=Ro(n),ut!==null&&(ft.data=ut)))),(ut=zo?Po(t,n):Lu(t,n))&&(ft=Cr(z,"onBeforeInput"),0<ft.length&&(Lt=new ei("onBeforeInput","beforeinput",null,n,Z),G.push({event:Lt,listeners:ft}),Lt.data=ut)),Vp(G,t,z,n,Z)}qd(G,e)})}function ql(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Cr(t,e){for(var n=e+"Capture",s=[];t!==null;){var o=t,c=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||c===null||(o=Pt(t,n),o!=null&&s.unshift(ql(t,o,c)),o=Pt(t,e),o!=null&&s.push(ql(t,o,c))),t.tag===3)return s;t=t.return}return[]}function Gs(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Yd(t,e,n,s,o){for(var c=e._reactName,m=[];n!==null&&n!==s;){var v=n,T=v.alternate,z=v.stateNode;if(v=v.tag,T!==null&&T===s)break;v!==5&&v!==26&&v!==27||z===null||(T=z,o?(z=Pt(n,c),z!=null&&m.unshift(ql(n,z,T))):o||(z=Pt(n,c),z!=null&&m.push(ql(n,z,T)))),n=n.return}m.length!==0&&t.push({event:e,listeners:m})}var Qp=/\r\n?/g,Fp=/\u0000|\uFFFD/g;function Xd(t){return(typeof t=="string"?t:""+t).replace(Qp,`
`).replace(Fp,"")}function Kd(t,e){return e=Xd(e),Xd(t)===e}function wr(){}function Ht(t,e,n,s,o,c){switch(n){case"children":typeof s=="string"?e==="body"||e==="textarea"&&s===""||mi(t,s):(typeof s=="number"||typeof s=="bigint")&&e!=="body"&&mi(t,""+s);break;case"className":os(t,"class",s);break;case"tabIndex":os(t,"tabindex",s);break;case"dir":case"role":case"viewBox":case"width":case"height":os(t,n,s);break;case"style":kn(t,s,c);break;case"data":if(e!=="object"){os(t,"data",s);break}case"src":case"href":if(s===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(s==null||typeof s=="function"||typeof s=="symbol"||typeof s=="boolean"){t.removeAttribute(n);break}s=Zn(""+s),t.setAttribute(n,s);break;case"action":case"formAction":if(typeof s=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(e!=="input"&&Ht(t,e,"name",o.name,o,null),Ht(t,e,"formEncType",o.formEncType,o,null),Ht(t,e,"formMethod",o.formMethod,o,null),Ht(t,e,"formTarget",o.formTarget,o,null)):(Ht(t,e,"encType",o.encType,o,null),Ht(t,e,"method",o.method,o,null),Ht(t,e,"target",o.target,o,null)));if(s==null||typeof s=="symbol"||typeof s=="boolean"){t.removeAttribute(n);break}s=Zn(""+s),t.setAttribute(n,s);break;case"onClick":s!=null&&(t.onclick=wr);break;case"onScroll":s!=null&&Et("scroll",t);break;case"onScrollEnd":s!=null&&Et("scrollend",t);break;case"dangerouslySetInnerHTML":if(s!=null){if(typeof s!="object"||!("__html"in s))throw Error(h(61));if(n=s.__html,n!=null){if(o.children!=null)throw Error(h(60));t.innerHTML=n}}break;case"multiple":t.multiple=s&&typeof s!="function"&&typeof s!="symbol";break;case"muted":t.muted=s&&typeof s!="function"&&typeof s!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(s==null||typeof s=="function"||typeof s=="boolean"||typeof s=="symbol"){t.removeAttribute("xlink:href");break}n=Zn(""+s),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":s!=null&&typeof s!="function"&&typeof s!="symbol"?t.setAttribute(n,""+s):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":s&&typeof s!="function"&&typeof s!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":s===!0?t.setAttribute(n,""):s!==!1&&s!=null&&typeof s!="function"&&typeof s!="symbol"?t.setAttribute(n,s):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":s!=null&&typeof s!="function"&&typeof s!="symbol"&&!isNaN(s)&&1<=s?t.setAttribute(n,s):t.removeAttribute(n);break;case"rowSpan":case"start":s==null||typeof s=="function"||typeof s=="symbol"||isNaN(s)?t.removeAttribute(n):t.setAttribute(n,s);break;case"popover":Et("beforetoggle",t),Et("toggle",t),ls(t,"popover",s);break;case"xlinkActuate":Ai(t,"http://www.w3.org/1999/xlink","xlink:actuate",s);break;case"xlinkArcrole":Ai(t,"http://www.w3.org/1999/xlink","xlink:arcrole",s);break;case"xlinkRole":Ai(t,"http://www.w3.org/1999/xlink","xlink:role",s);break;case"xlinkShow":Ai(t,"http://www.w3.org/1999/xlink","xlink:show",s);break;case"xlinkTitle":Ai(t,"http://www.w3.org/1999/xlink","xlink:title",s);break;case"xlinkType":Ai(t,"http://www.w3.org/1999/xlink","xlink:type",s);break;case"xmlBase":Ai(t,"http://www.w3.org/XML/1998/namespace","xml:base",s);break;case"xmlLang":Ai(t,"http://www.w3.org/XML/1998/namespace","xml:lang",s);break;case"xmlSpace":Ai(t,"http://www.w3.org/XML/1998/namespace","xml:space",s);break;case"is":ls(t,"is",s);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=sl.get(n)||n,ls(t,n,s))}}function Hc(t,e,n,s,o,c){switch(n){case"style":kn(t,s,c);break;case"dangerouslySetInnerHTML":if(s!=null){if(typeof s!="object"||!("__html"in s))throw Error(h(61));if(n=s.__html,n!=null){if(o.children!=null)throw Error(h(60));t.innerHTML=n}}break;case"children":typeof s=="string"?mi(t,s):(typeof s=="number"||typeof s=="bigint")&&mi(t,""+s);break;case"onScroll":s!=null&&Et("scroll",t);break;case"onScrollEnd":s!=null&&Et("scrollend",t);break;case"onClick":s!=null&&(t.onclick=wr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!bo.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),e=n.slice(2,o?n.length-7:void 0),c=t[De]||null,c=c!=null?c[n]:null,typeof c=="function"&&t.removeEventListener(e,c,o),typeof s=="function")){typeof c!="function"&&c!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,s,o);break t}n in t?t[n]=s:s===!0?t.setAttribute(n,""):ls(t,n,s)}}}function Ee(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Et("error",t),Et("load",t);var s=!1,o=!1,c;for(c in n)if(n.hasOwnProperty(c)){var m=n[c];if(m!=null)switch(c){case"src":s=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(h(137,e));default:Ht(t,e,c,m,n,null)}}o&&Ht(t,e,"srcSet",n.srcSet,n,null),s&&Ht(t,e,"src",n.src,n,null);return;case"input":Et("invalid",t);var v=c=m=o=null,T=null,z=null;for(s in n)if(n.hasOwnProperty(s)){var Z=n[s];if(Z!=null)switch(s){case"name":o=Z;break;case"type":m=Z;break;case"checked":T=Z;break;case"defaultChecked":z=Z;break;case"value":c=Z;break;case"defaultValue":v=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(h(137,e));break;default:Ht(t,e,s,Z,n,null)}}xo(t,c,v,T,z,m,o,!1),Bn(t);return;case"select":Et("invalid",t),s=m=c=null;for(o in n)if(n.hasOwnProperty(o)&&(v=n[o],v!=null))switch(o){case"value":c=v;break;case"defaultValue":m=v;break;case"multiple":s=v;default:Ht(t,e,o,v,n,null)}e=c,n=m,t.multiple=!!s,e!=null?Xe(t,!!s,e,!1):n!=null&&Xe(t,!!s,n,!0);return;case"textarea":Et("invalid",t),c=o=s=null;for(m in n)if(n.hasOwnProperty(m)&&(v=n[m],v!=null))switch(m){case"value":s=v;break;case"defaultValue":o=v;break;case"children":c=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(h(91));break;default:Ht(t,e,m,v,n,null)}Zi(t,s,o,c),Bn(t);return;case"option":for(T in n)if(n.hasOwnProperty(T)&&(s=n[T],s!=null))switch(T){case"selected":t.selected=s&&typeof s!="function"&&typeof s!="symbol";break;default:Ht(t,e,T,s,n,null)}return;case"dialog":Et("beforetoggle",t),Et("toggle",t),Et("cancel",t),Et("close",t);break;case"iframe":case"object":Et("load",t);break;case"video":case"audio":for(s=0;s<Il.length;s++)Et(Il[s],t);break;case"image":Et("error",t),Et("load",t);break;case"details":Et("toggle",t);break;case"embed":case"source":case"link":Et("error",t),Et("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in n)if(n.hasOwnProperty(z)&&(s=n[z],s!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(h(137,e));default:Ht(t,e,z,s,n,null)}return;default:if(Ea(e)){for(Z in n)n.hasOwnProperty(Z)&&(s=n[Z],s!==void 0&&Hc(t,e,Z,s,n,void 0));return}}for(v in n)n.hasOwnProperty(v)&&(s=n[v],s!=null&&Ht(t,e,v,s,n,null))}function Jp(t,e,n,s){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,c=null,m=null,v=null,T=null,z=null,Z=null;for(B in n){var G=n[B];if(n.hasOwnProperty(B)&&G!=null)switch(B){case"checked":break;case"value":break;case"defaultValue":T=G;default:s.hasOwnProperty(B)||Ht(t,e,B,null,s,G)}}for(var R in s){var B=s[R];if(G=n[R],s.hasOwnProperty(R)&&(B!=null||G!=null))switch(R){case"type":c=B;break;case"name":o=B;break;case"checked":z=B;break;case"defaultChecked":Z=B;break;case"value":m=B;break;case"defaultValue":v=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(h(137,e));break;default:B!==G&&Ht(t,e,R,B,s,G)}}ze(t,m,v,T,z,Z,c,o);return;case"select":B=m=v=R=null;for(c in n)if(T=n[c],n.hasOwnProperty(c)&&T!=null)switch(c){case"value":break;case"multiple":B=T;default:s.hasOwnProperty(c)||Ht(t,e,c,null,s,T)}for(o in s)if(c=s[o],T=n[o],s.hasOwnProperty(o)&&(c!=null||T!=null))switch(o){case"value":R=c;break;case"defaultValue":v=c;break;case"multiple":m=c;default:c!==T&&Ht(t,e,o,c,s,T)}e=v,n=m,s=B,R!=null?Xe(t,!!n,R,!1):!!s!=!!n&&(e!=null?Xe(t,!!n,e,!0):Xe(t,!!n,n?[]:"",!1));return;case"textarea":B=R=null;for(v in n)if(o=n[v],n.hasOwnProperty(v)&&o!=null&&!s.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:Ht(t,e,v,null,s,o)}for(m in s)if(o=s[m],c=n[m],s.hasOwnProperty(m)&&(o!=null||c!=null))switch(m){case"value":R=o;break;case"defaultValue":B=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(h(91));break;default:o!==c&&Ht(t,e,m,o,s,c)}Wt(t,R,B);return;case"option":for(var mt in n)if(R=n[mt],n.hasOwnProperty(mt)&&R!=null&&!s.hasOwnProperty(mt))switch(mt){case"selected":t.selected=!1;break;default:Ht(t,e,mt,null,s,R)}for(T in s)if(R=s[T],B=n[T],s.hasOwnProperty(T)&&R!==B&&(R!=null||B!=null))switch(T){case"selected":t.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:Ht(t,e,T,R,s,B)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ct in n)R=n[ct],n.hasOwnProperty(ct)&&R!=null&&!s.hasOwnProperty(ct)&&Ht(t,e,ct,null,s,R);for(z in s)if(R=s[z],B=n[z],s.hasOwnProperty(z)&&R!==B&&(R!=null||B!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(h(137,e));break;default:Ht(t,e,z,R,s,B)}return;default:if(Ea(e)){for(var jt in n)R=n[jt],n.hasOwnProperty(jt)&&R!==void 0&&!s.hasOwnProperty(jt)&&Hc(t,e,jt,void 0,s,R);for(Z in s)R=s[Z],B=n[Z],!s.hasOwnProperty(Z)||R===B||R===void 0&&B===void 0||Hc(t,e,Z,R,s,B);return}}for(var w in n)R=n[w],n.hasOwnProperty(w)&&R!=null&&!s.hasOwnProperty(w)&&Ht(t,e,w,null,s,R);for(G in s)R=s[G],B=n[G],!s.hasOwnProperty(G)||R===B||R==null&&B==null||Ht(t,e,G,R,s,B)}var jc=null,Gc=null;function Lr(t){return t.nodeType===9?t:t.ownerDocument}function Qd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Fd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Ic(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var qc=null;function Wp(){var t=window.event;return t&&t.type==="popstate"?t===qc?!1:(qc=t,!0):(qc=null,!1)}var Jd=typeof setTimeout=="function"?setTimeout:void 0,$p=typeof clearTimeout=="function"?clearTimeout:void 0,Wd=typeof Promise=="function"?Promise:void 0,tg=typeof queueMicrotask=="function"?queueMicrotask:typeof Wd<"u"?function(t){return Wd.resolve(null).then(t).catch(eg)}:Jd;function eg(t){setTimeout(function(){throw t})}function ga(t){return t==="head"}function $d(t,e){var n=e,s=0,o=0;do{var c=n.nextSibling;if(t.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<s&&8>s){n=s;var m=t.ownerDocument;if(n&1&&Vl(m.documentElement),n&2&&Vl(m.body),n&4)for(n=m.head,Vl(n),m=n.firstChild;m;){var v=m.nextSibling,T=m.nodeName;m[Nn]||T==="SCRIPT"||T==="STYLE"||T==="LINK"&&m.rel.toLowerCase()==="stylesheet"||n.removeChild(m),m=v}}if(o===0){t.removeChild(c),$l(e);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:s=n.charCodeAt(0)-48;else s=0;n=c}while(n);$l(e)}function Vc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Vc(n),ss(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function ig(t,e,n,s){for(;t.nodeType===1;){var o=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!s&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(s){if(!t[Nn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=Ri(t.nextSibling),t===null)break}return null}function ng(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ri(t.nextSibling),t===null))return null;return t}function Yc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function ag(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var s=function(){e(),n.removeEventListener("DOMContentLoaded",s)};n.addEventListener("DOMContentLoaded",s),t._reactRetry=s}}function Ri(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Xc=null;function tm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function em(t,e,n){switch(e=Lr(n),t){case"html":if(t=e.documentElement,!t)throw Error(h(452));return t;case"head":if(t=e.head,!t)throw Error(h(453));return t;case"body":if(t=e.body,!t)throw Error(h(454));return t;default:throw Error(h(451))}}function Vl(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ss(t)}var xi=new Map,im=new Set;function Ar(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ln=Q.d;Q.d={f:sg,r:lg,D:og,C:rg,L:ug,m:cg,X:hg,S:fg,M:dg};function sg(){var t=Ln.f(),e=vr();return t||e}function lg(t){var e=Wi(t);e!==null&&e.tag===5&&e.type==="form"?Th(e):Ln.r(t)}var Is=typeof document>"u"?null:document;function nm(t,e,n){var s=Is;if(s&&typeof e=="string"&&e){var o=Ce(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),im.has(o)||(im.add(o),t={rel:t,crossOrigin:n,href:e},s.querySelector(o)===null&&(e=s.createElement("link"),Ee(e,"link",t),ue(e),s.head.appendChild(e)))}}function og(t){Ln.D(t),nm("dns-prefetch",t,null)}function rg(t,e){Ln.C(t,e),nm("preconnect",t,e)}function ug(t,e,n){Ln.L(t,e,n);var s=Is;if(s&&t&&e){var o='link[rel="preload"][as="'+Ce(e)+'"]';e==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+Ce(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+Ce(n.imageSizes)+'"]')):o+='[href="'+Ce(t)+'"]';var c=o;switch(e){case"style":c=qs(t);break;case"script":c=Vs(t)}xi.has(c)||(t=j({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),xi.set(c,t),s.querySelector(o)!==null||e==="style"&&s.querySelector(Yl(c))||e==="script"&&s.querySelector(Xl(c))||(e=s.createElement("link"),Ee(e,"link",t),ue(e),s.head.appendChild(e)))}}function cg(t,e){Ln.m(t,e);var n=Is;if(n&&t){var s=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ce(s)+'"][href="'+Ce(t)+'"]',c=o;switch(s){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Vs(t)}if(!xi.has(c)&&(t=j({rel:"modulepreload",href:t},e),xi.set(c,t),n.querySelector(o)===null)){switch(s){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Xl(c)))return}s=n.createElement("link"),Ee(s,"link",t),ue(s),n.head.appendChild(s)}}}function fg(t,e,n){Ln.S(t,e,n);var s=Is;if(s&&t){var o=$i(s).hoistableStyles,c=qs(t);e=e||"default";var m=o.get(c);if(!m){var v={loading:0,preload:null};if(m=s.querySelector(Yl(c)))v.loading=5;else{t=j({rel:"stylesheet",href:t,"data-precedence":e},n),(n=xi.get(c))&&Kc(t,n);var T=m=s.createElement("link");ue(T),Ee(T,"link",t),T._p=new Promise(function(z,Z){T.onload=z,T.onerror=Z}),T.addEventListener("load",function(){v.loading|=1}),T.addEventListener("error",function(){v.loading|=2}),v.loading|=4,Er(m,e,s)}m={type:"stylesheet",instance:m,count:1,state:v},o.set(c,m)}}}function hg(t,e){Ln.X(t,e);var n=Is;if(n&&t){var s=$i(n).hoistableScripts,o=Vs(t),c=s.get(o);c||(c=n.querySelector(Xl(o)),c||(t=j({src:t,async:!0},e),(e=xi.get(o))&&Qc(t,e),c=n.createElement("script"),ue(c),Ee(c,"link",t),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},s.set(o,c))}}function dg(t,e){Ln.M(t,e);var n=Is;if(n&&t){var s=$i(n).hoistableScripts,o=Vs(t),c=s.get(o);c||(c=n.querySelector(Xl(o)),c||(t=j({src:t,async:!0,type:"module"},e),(e=xi.get(o))&&Qc(t,e),c=n.createElement("script"),ue(c),Ee(c,"link",t),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},s.set(o,c))}}function am(t,e,n,s){var o=(o=W.current)?Ar(o):null;if(!o)throw Error(h(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=qs(n.href),n=$i(o).hoistableStyles,s=n.get(e),s||(s={type:"style",instance:null,count:0,state:null},n.set(e,s)),s):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=qs(n.href);var c=$i(o).hoistableStyles,m=c.get(t);if(m||(o=o.ownerDocument||o,m={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,m),(c=o.querySelector(Yl(t)))&&!c._p&&(m.instance=c,m.state.loading=5),xi.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},xi.set(t,n),c||mg(o,t,n,m.state))),e&&s===null)throw Error(h(528,""));return m}if(e&&s!==null)throw Error(h(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Vs(n),n=$i(o).hoistableScripts,s=n.get(e),s||(s={type:"script",instance:null,count:0,state:null},n.set(e,s)),s):{type:"void",instance:null,count:0,state:null};default:throw Error(h(444,t))}}function qs(t){return'href="'+Ce(t)+'"'}function Yl(t){return'link[rel="stylesheet"]['+t+"]"}function sm(t){return j({},t,{"data-precedence":t.precedence,precedence:null})}function mg(t,e,n,s){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?s.loading=1:(e=t.createElement("link"),s.preload=e,e.addEventListener("load",function(){return s.loading|=1}),e.addEventListener("error",function(){return s.loading|=2}),Ee(e,"link",n),ue(e),t.head.appendChild(e))}function Vs(t){return'[src="'+Ce(t)+'"]'}function Xl(t){return"script[async]"+t}function lm(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var s=t.querySelector('style[data-href~="'+Ce(n.href)+'"]');if(s)return e.instance=s,ue(s),s;var o=j({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return s=(t.ownerDocument||t).createElement("style"),ue(s),Ee(s,"style",o),Er(s,n.precedence,t),e.instance=s;case"stylesheet":o=qs(n.href);var c=t.querySelector(Yl(o));if(c)return e.state.loading|=4,e.instance=c,ue(c),c;s=sm(n),(o=xi.get(o))&&Kc(s,o),c=(t.ownerDocument||t).createElement("link"),ue(c);var m=c;return m._p=new Promise(function(v,T){m.onload=v,m.onerror=T}),Ee(c,"link",s),e.state.loading|=4,Er(c,n.precedence,t),e.instance=c;case"script":return c=Vs(n.src),(o=t.querySelector(Xl(c)))?(e.instance=o,ue(o),o):(s=n,(o=xi.get(c))&&(s=j({},n),Qc(s,o)),t=t.ownerDocument||t,o=t.createElement("script"),ue(o),Ee(o,"link",s),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(h(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(s=e.instance,e.state.loading|=4,Er(s,n.precedence,t));return e.instance}function Er(t,e,n){for(var s=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=s.length?s[s.length-1]:null,c=o,m=0;m<s.length;m++){var v=s[m];if(v.dataset.precedence===e)c=v;else if(c!==o)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Kc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Qc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Mr=null;function om(t,e,n){if(Mr===null){var s=new Map,o=Mr=new Map;o.set(n,s)}else o=Mr,s=o.get(n),s||(s=new Map,o.set(n,s));if(s.has(t))return s;for(s.set(t,null),n=n.getElementsByTagName(t),o=0;o<n.length;o++){var c=n[o];if(!(c[Nn]||c[ge]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var m=c.getAttribute(e)||"";m=t+m;var v=s.get(m);v?v.push(c):s.set(m,[c])}}return s}function rm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function pg(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function um(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Kl=null;function gg(){}function _g(t,e,n){if(Kl===null)throw Error(h(475));var s=Kl;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=qs(n.href),c=t.querySelector(Yl(o));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(s.count++,s=Or.bind(s),t.then(s,s)),e.state.loading|=4,e.instance=c,ue(c);return}c=t.ownerDocument||t,n=sm(n),(o=xi.get(o))&&Kc(n,o),c=c.createElement("link"),ue(c);var m=c;m._p=new Promise(function(v,T){m.onload=v,m.onerror=T}),Ee(c,"link",n),e.instance=c}s.stylesheets===null&&(s.stylesheets=new Map),s.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(s.count++,e=Or.bind(s),t.addEventListener("load",e),t.addEventListener("error",e))}}function vg(){if(Kl===null)throw Error(h(475));var t=Kl;return t.stylesheets&&t.count===0&&Fc(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Fc(t,t.stylesheets),t.unsuspend){var s=t.unsuspend;t.unsuspend=null,s()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Or(){if(this.count--,this.count===0){if(this.stylesheets)Fc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Dr=null;function Fc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Dr=new Map,e.forEach(yg,t),Dr=null,Or.call(t))}function yg(t,e){if(!(e.state.loading&4)){var n=Dr.get(t);if(n)var s=n.get(null);else{n=new Map,Dr.set(t,n);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<o.length;c++){var m=o[c];(m.nodeName==="LINK"||m.getAttribute("media")!=="not all")&&(n.set(m.dataset.precedence,m),s=m)}s&&n.set(null,s)}o=e.instance,m=o.getAttribute("data-precedence"),c=n.get(m)||s,c===s&&n.set(null,o),n.set(m,o),this.count++,s=Or.bind(this),o.addEventListener("load",s),o.addEventListener("error",s),c?c.parentNode.insertBefore(o,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var Ql={$$typeof:Tt,Provider:null,Consumer:null,_currentValue:V,_currentValue2:V,_threadCount:0};function Sg(t,e,n,s,o,c,m,v){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=as(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=as(0),this.hiddenUpdates=as(null),this.identifierPrefix=s,this.onUncaughtError=o,this.onCaughtError=c,this.onRecoverableError=m,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function cm(t,e,n,s,o,c,m,v,T,z,Z,G){return t=new Sg(t,e,n,m,v,T,z,G),e=1,c===!0&&(e|=24),c=Ie(3,null,null,e),t.current=c,c.stateNode=t,e=Du(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:s,isDehydrated:n,cache:e},Pu(c),t}function fm(t){return t?(t=_n,t):_n}function hm(t,e,n,s,o,c){o=fm(o),s.context===null?s.context=o:s.pendingContext=o,s=na(e),s.payload={element:n},c=c===void 0?null:c,c!==null&&(s.callback=c),n=aa(t,s,e),n!==null&&(fi(n,t,e),wl(n,t,e))}function dm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Jc(t,e){dm(t,e),(t=t.alternate)&&dm(t,e)}function mm(t){if(t.tag===13){var e=Wn(t,67108864);e!==null&&fi(e,t,67108864),Jc(t,67108864)}}var zr=!0;function bg(t,e,n,s){var o=P.T;P.T=null;var c=Q.p;try{Q.p=2,Wc(t,e,n,s)}finally{Q.p=c,P.T=o}}function Tg(t,e,n,s){var o=P.T;P.T=null;var c=Q.p;try{Q.p=8,Wc(t,e,n,s)}finally{Q.p=c,P.T=o}}function Wc(t,e,n,s){if(zr){var o=$c(s);if(o===null)Zc(t,e,s,Nr,n),gm(t,s);else if(Cg(o,t,e,n,s))s.stopPropagation();else if(gm(t,s),e&4&&-1<xg.indexOf(t)){for(;o!==null;){var c=Wi(o);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var m=Bi(c.pendingLanes);if(m!==0){var v=c;for(v.pendingLanes|=2,v.entangledLanes|=2;m;){var T=1<<31-Ue(m);v.entanglements[1]|=T,m&=~T}Qi(c),(Ut&6)===0&&(gr=Ve()+500,Gl(0))}}break;case 13:v=Wn(c,2),v!==null&&fi(v,c,2),vr(),Jc(c,2)}if(c=$c(s),c===null&&Zc(t,e,s,Nr,n),c===o)break;o=c}o!==null&&s.stopPropagation()}else Zc(t,e,s,null,n)}}function $c(t){return t=Hn(t),tf(t)}var Nr=null;function tf(t){if(Nr=null,t=ki(t),t!==null){var e=y(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=x(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Nr=t,null}function pm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(is()){case co:return 2;case Js:return 8;case Mn:case fo:return 32;case Ws:return 268435456;default:return 32}default:return 32}}var ef=!1,_a=null,va=null,ya=null,Fl=new Map,Jl=new Map,Sa=[],xg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function gm(t,e){switch(t){case"focusin":case"focusout":_a=null;break;case"dragenter":case"dragleave":va=null;break;case"mouseover":case"mouseout":ya=null;break;case"pointerover":case"pointerout":Fl.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Jl.delete(e.pointerId)}}function Wl(t,e,n,s,o,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:n,eventSystemFlags:s,nativeEvent:c,targetContainers:[o]},e!==null&&(e=Wi(e),e!==null&&mm(e)),t):(t.eventSystemFlags|=s,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function Cg(t,e,n,s,o){switch(e){case"focusin":return _a=Wl(_a,t,e,n,s,o),!0;case"dragenter":return va=Wl(va,t,e,n,s,o),!0;case"mouseover":return ya=Wl(ya,t,e,n,s,o),!0;case"pointerover":var c=o.pointerId;return Fl.set(c,Wl(Fl.get(c)||null,t,e,n,s,o)),!0;case"gotpointercapture":return c=o.pointerId,Jl.set(c,Wl(Jl.get(c)||null,t,e,n,s,o)),!0}return!1}function _m(t){var e=ki(t.target);if(e!==null){var n=y(e);if(n!==null){if(e=n.tag,e===13){if(e=x(n),e!==null){t.blockedOn=e,il(t.priority,function(){if(n.tag===13){var s=ci();s=tl(s);var o=Wn(n,s);o!==null&&fi(o,n,s),Jc(n,s)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Rr(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=$c(t.nativeEvent);if(n===null){n=t.nativeEvent;var s=new n.constructor(n.type,n);Ma=s,n.target.dispatchEvent(s),Ma=null}else return e=Wi(n),e!==null&&mm(e),t.blockedOn=n,!1;e.shift()}return!0}function vm(t,e,n){Rr(t)&&n.delete(e)}function wg(){ef=!1,_a!==null&&Rr(_a)&&(_a=null),va!==null&&Rr(va)&&(va=null),ya!==null&&Rr(ya)&&(ya=null),Fl.forEach(vm),Jl.forEach(vm)}function Pr(t,e){t.blockedOn===e&&(t.blockedOn=null,ef||(ef=!0,p.unstable_scheduleCallback(p.unstable_NormalPriority,wg)))}var Br=null;function ym(t){Br!==t&&(Br=t,p.unstable_scheduleCallback(p.unstable_NormalPriority,function(){Br===t&&(Br=null);for(var e=0;e<t.length;e+=3){var n=t[e],s=t[e+1],o=t[e+2];if(typeof s!="function"){if(tf(s||n)===null)continue;break}var c=Wi(n);c!==null&&(t.splice(e,3),e-=3,tc(c,{pending:!0,data:o,method:n.method,action:s},s,o))}}))}function $l(t){function e(T){return Pr(T,t)}_a!==null&&Pr(_a,t),va!==null&&Pr(va,t),ya!==null&&Pr(ya,t),Fl.forEach(e),Jl.forEach(e);for(var n=0;n<Sa.length;n++){var s=Sa[n];s.blockedOn===t&&(s.blockedOn=null)}for(;0<Sa.length&&(n=Sa[0],n.blockedOn===null);)_m(n),n.blockedOn===null&&Sa.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(s=0;s<n.length;s+=3){var o=n[s],c=n[s+1],m=o[De]||null;if(typeof c=="function")m||ym(n);else if(m){var v=null;if(c&&c.hasAttribute("formAction")){if(o=c,m=c[De]||null)v=m.formAction;else if(tf(o)!==null)continue}else v=m.action;typeof v=="function"?n[s+1]=v:(n.splice(s,3),s-=3),ym(n)}}}function nf(t){this._internalRoot=t}Ur.prototype.render=nf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(h(409));var n=e.current,s=ci();hm(n,s,t,e,null,null)},Ur.prototype.unmount=nf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;hm(t.current,2,null,t,null,null),vr(),e[zn]=null}};function Ur(t){this._internalRoot=t}Ur.prototype.unstable_scheduleHydration=function(t){if(t){var e=vo();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Sa.length&&e!==0&&e<Sa[n].priority;n++);Sa.splice(n,0,t),n===0&&_m(t)}};var Sm=r.version;if(Sm!=="19.1.1")throw Error(h(527,Sm,"19.1.1"));Q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(h(188)):(t=Object.keys(t).join(","),Error(h(268,t)));return t=A(e),t=t!==null?E(t):null,t=t===null?null:t.stateNode,t};var Lg={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:P,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var kr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!kr.isDisabled&&kr.supportsFiber)try{Ji=kr.inject(Lg),Oe=kr}catch{}}return eo.createRoot=function(t,e){if(!g(t))throw Error(h(299));var n=!1,s="",o=Bh,c=Uh,m=kh,v=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(s=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(m=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(v=e.unstable_transitionCallbacks)),e=cm(t,1,!1,null,null,n,s,o,c,m,v,null),t[zn]=e.current,kc(t),new nf(e)},eo.hydrateRoot=function(t,e,n){if(!g(t))throw Error(h(299));var s=!1,o="",c=Bh,m=Uh,v=kh,T=null,z=null;return n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(m=n.onCaughtError),n.onRecoverableError!==void 0&&(v=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(T=n.unstable_transitionCallbacks),n.formState!==void 0&&(z=n.formState)),e=cm(t,1,!0,e,n??null,s,o,c,m,v,T,z),e.context=fm(null),n=e.current,s=ci(),s=tl(s),o=na(s),o.callback=null,aa(n,o,s),n=s,e.current.lanes=n,Dn(e,n),Qi(e),t[zn]=e.current,kc(t),new Ur(e)},eo.version="19.1.1",eo}var Om;function Pg(){if(Om)return lf.exports;Om=1;function p(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(p)}catch(r){console.error(r)}}return p(),lf.exports=Rg(),lf.exports}var Bg=Pg(),eu=typeof self<"u"?self:global;const lo=typeof navigator<"u",Ug=lo&&typeof HTMLImageElement>"u",Fr=!(typeof global>"u"||typeof process>"u"||!process.versions||!process.versions.node),Lf=eu.Buffer,Zr=eu.BigInt,Af=!!Lf,kg=p=>p;function Jr(p,r=kg){if(Fr)try{return typeof require=="function"?Promise.resolve(r(require(p))):import(p).then(r)}catch{console.warn(`Couldn't load ${p}`)}}let Ef=eu.fetch;const Zg=p=>Ef=p;if(!eu.fetch){const p=Jr("http",h=>h),r=Jr("https",h=>h),u=(h,{headers:g}={})=>new Promise(async(y,x)=>{let{port:N,hostname:A,pathname:E,protocol:j,search:X}=new URL(h);const Y={method:"GET",hostname:A,path:encodeURI(E)+X,headers:g};N!==""&&(Y.port=Number(N));const dt=(j==="https:"?await r:await p).request(Y,ht=>{if(ht.statusCode===301||ht.statusCode===302){let st=new URL(ht.headers.location,h).toString();return u(st,{headers:g}).then(y).catch(x)}y({status:ht.statusCode,arrayBuffer:()=>new Promise(st=>{let xt=[];ht.on("data",Kt=>xt.push(Kt)),ht.on("end",()=>st(Buffer.concat(xt)))})})});dt.on("error",x),dt.end()});Zg(u)}function pt(p,r,u){return r in p?Object.defineProperty(p,r,{value:u,enumerable:!0,configurable:!0,writable:!0}):p[r]=u,p}const Wr=p=>Wm(p)?void 0:p,Hg=p=>p!==void 0;function Wm(p){return p===void 0||(p instanceof Map?p.size===0:Object.values(p).filter(Hg).length===0)}function he(p){let r=new Error(p);throw delete r.stack,r}function ts(p){return(p=function(r){for(;r.endsWith("\0");)r=r.slice(0,-1);return r}(p).trim())===""?void 0:p}function gf(p){let r=function(u){let h=0;return u.ifd0.enabled&&(h+=1024),u.exif.enabled&&(h+=2048),u.makerNote&&(h+=2048),u.userComment&&(h+=1024),u.gps.enabled&&(h+=512),u.interop.enabled&&(h+=100),u.ifd1.enabled&&(h+=1024),h+2048}(p);return p.jfif.enabled&&(r+=50),p.xmp.enabled&&(r+=2e4),p.iptc.enabled&&(r+=14e3),p.icc.enabled&&(r+=6e3),r}const _f=p=>String.fromCharCode.apply(null,p),Dm=typeof TextDecoder<"u"?new TextDecoder("utf-8"):void 0;function $m(p){return Dm?Dm.decode(p):Af?Buffer.from(p).toString("utf8"):decodeURIComponent(escape(_f(p)))}class We{static from(r,u){return r instanceof this&&r.le===u?r:new We(r,void 0,void 0,u)}constructor(r,u=0,h,g){if(typeof g=="boolean"&&(this.le=g),Array.isArray(r)&&(r=new Uint8Array(r)),r===0)this.byteOffset=0,this.byteLength=0;else if(r instanceof ArrayBuffer){h===void 0&&(h=r.byteLength-u);let y=new DataView(r,u,h);this._swapDataView(y)}else if(r instanceof Uint8Array||r instanceof DataView||r instanceof We){h===void 0&&(h=r.byteLength-u),(u+=r.byteOffset)+h>r.byteOffset+r.byteLength&&he("Creating view outside of available memory in ArrayBuffer");let y=new DataView(r.buffer,u,h);this._swapDataView(y)}else if(typeof r=="number"){let y=new DataView(new ArrayBuffer(r));this._swapDataView(y)}else he("Invalid input argument for BufferView: "+r)}_swapArrayBuffer(r){this._swapDataView(new DataView(r))}_swapBuffer(r){this._swapDataView(new DataView(r.buffer,r.byteOffset,r.byteLength))}_swapDataView(r){this.dataView=r,this.buffer=r.buffer,this.byteOffset=r.byteOffset,this.byteLength=r.byteLength}_lengthToEnd(r){return this.byteLength-r}set(r,u,h=We){return r instanceof DataView||r instanceof We?r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength):r instanceof ArrayBuffer&&(r=new Uint8Array(r)),r instanceof Uint8Array||he("BufferView.set(): Invalid data argument."),this.toUint8().set(r,u),new h(this,u,r.byteLength)}subarray(r,u){return u=u||this._lengthToEnd(r),new We(this,r,u)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(r,u){return new Uint8Array(this.buffer,this.byteOffset+r,u)}getString(r=0,u=this.byteLength){return $m(this.getUint8Array(r,u))}getLatin1String(r=0,u=this.byteLength){let h=this.getUint8Array(r,u);return _f(h)}getUnicodeString(r=0,u=this.byteLength){const h=[];for(let g=0;g<u&&r+g<this.byteLength;g+=2)h.push(this.getUint16(r+g));return _f(h)}getInt8(r){return this.dataView.getInt8(r)}getUint8(r){return this.dataView.getUint8(r)}getInt16(r,u=this.le){return this.dataView.getInt16(r,u)}getInt32(r,u=this.le){return this.dataView.getInt32(r,u)}getUint16(r,u=this.le){return this.dataView.getUint16(r,u)}getUint32(r,u=this.le){return this.dataView.getUint32(r,u)}getFloat32(r,u=this.le){return this.dataView.getFloat32(r,u)}getFloat64(r,u=this.le){return this.dataView.getFloat64(r,u)}getFloat(r,u=this.le){return this.dataView.getFloat32(r,u)}getDouble(r,u=this.le){return this.dataView.getFloat64(r,u)}getUintBytes(r,u,h){switch(u){case 1:return this.getUint8(r,h);case 2:return this.getUint16(r,h);case 4:return this.getUint32(r,h);case 8:return this.getUint64&&this.getUint64(r,h)}}getUint(r,u,h){switch(u){case 8:return this.getUint8(r,h);case 16:return this.getUint16(r,h);case 32:return this.getUint32(r,h);case 64:return this.getUint64&&this.getUint64(r,h)}}toString(r){return this.dataView.toString(r,this.constructor.name)}ensureChunk(){}}function vf(p,r){he(`${p} '${r}' was not loaded, try using full build of exifr.`)}class Mf extends Map{constructor(r){super(),this.kind=r}get(r,u){return this.has(r)||vf(this.kind,r),u&&(r in u||function(h,g){he(`Unknown ${h} '${g}'.`)}(this.kind,r),u[r].enabled||vf(this.kind,r)),super.get(r)}keyList(){return Array.from(this.keys())}}var Xs=new Mf("file parser"),Be=new Mf("segment parser"),Ks=new Mf("file reader");function jg(p,r){return typeof p=="string"?zm(p,r):lo&&!Ug&&p instanceof HTMLImageElement?zm(p.src,r):p instanceof Uint8Array||p instanceof ArrayBuffer||p instanceof DataView?new We(p):lo&&p instanceof Blob?yf(p,r,"blob",Tf):void he("Invalid input argument")}function zm(p,r){return(u=p).startsWith("data:")||u.length>1e4?Sf(p,r,"base64"):Fr&&p.includes("://")?yf(p,r,"url",bf):Fr?Sf(p,r,"fs"):lo?yf(p,r,"url",bf):void he("Invalid input argument");var u}async function yf(p,r,u,h){return Ks.has(u)?Sf(p,r,u):h?async function(g,y){let x=await y(g);return new We(x)}(p,h):void he(`Parser ${u} is not loaded`)}async function Sf(p,r,u){let h=new(Ks.get(u))(p,r);return await h.read(),h}const bf=p=>Ef(p).then(r=>r.arrayBuffer()),Tf=p=>new Promise((r,u)=>{let h=new FileReader;h.onloadend=()=>r(h.result||new ArrayBuffer),h.onerror=u,h.readAsArrayBuffer(p)});let Gg=class extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}};function Me(p,r,u){let h=new Gg;for(let[g,y]of u)h.set(g,y);if(Array.isArray(r))for(let g of r)p.set(g,h);else p.set(r,h);return h}function xf(p,r,u){let h,g=p.get(r);for(h of u)g.set(h[0],h[1])}const $e=new Map,es=new Map,Xr=new Map,Hr=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],tp=["jfif","xmp","icc","iptc","ihdr"],Cf=["tiff",...tp],Te=["ifd0","ifd1","exif","gps","interop"],jr=[...Cf,...Te],Gr=["makerNote","userComment"],ep=["translateKeys","translateValues","reviveValues","multiSegment"],Ir=[...ep,"sanitize","mergeOutput","silentErrors"];class ip{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class io extends ip{get needed(){return this.enabled||this.deps.size>0}constructor(r,u,h,g){if(super(),pt(this,"enabled",!1),pt(this,"skip",new Set),pt(this,"pick",new Set),pt(this,"deps",new Set),pt(this,"translateKeys",!1),pt(this,"translateValues",!1),pt(this,"reviveValues",!1),this.key=r,this.enabled=u,this.parse=this.enabled,this.applyInheritables(g),this.canBeFiltered=Te.includes(r),this.canBeFiltered&&(this.dict=$e.get(r)),h!==void 0)if(Array.isArray(h))this.parse=this.enabled=!0,this.canBeFiltered&&h.length>0&&this.translateTagSet(h,this.pick);else if(typeof h=="object"){if(this.enabled=!0,this.parse=h.parse!==!1,this.canBeFiltered){let{pick:y,skip:x}=h;y&&y.length>0&&this.translateTagSet(y,this.pick),x&&x.length>0&&this.translateTagSet(x,this.skip)}this.applyInheritables(h)}else h===!0||h===!1?this.parse=this.enabled=h:he(`Invalid options argument: ${h}`)}applyInheritables(r){let u,h;for(u of ep)h=r[u],h!==void 0&&(this[u]=h)}translateTagSet(r,u){if(this.dict){let h,g,{tagKeys:y,tagValues:x}=this.dict;for(h of r)typeof h=="string"?(g=x.indexOf(h),g===-1&&(g=y.indexOf(Number(h))),g!==-1&&u.add(Number(y[g]))):u.add(h)}else for(let h of r)u.add(h)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,$r(this.pick,this.deps)):this.enabled&&this.pick.size>0&&$r(this.pick,this.deps)}}var qe={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},Nm=new Map;class Of extends ip{static useCached(r){let u=Nm.get(r);return u!==void 0||(u=new this(r),Nm.set(r,u)),u}constructor(r){super(),r===!0?this.setupFromTrue():r===void 0?this.setupFromUndefined():Array.isArray(r)?this.setupFromArray(r):typeof r=="object"?this.setupFromObject(r):he(`Invalid options argument ${r}`),this.firstChunkSize===void 0&&(this.firstChunkSize=lo?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let r;for(r of Hr)this[r]=qe[r];for(r of Ir)this[r]=qe[r];for(r of Gr)this[r]=qe[r];for(r of jr)this[r]=new io(r,qe[r],void 0,this)}setupFromTrue(){let r;for(r of Hr)this[r]=qe[r];for(r of Ir)this[r]=qe[r];for(r of Gr)this[r]=!0;for(r of jr)this[r]=new io(r,!0,void 0,this)}setupFromArray(r){let u;for(u of Hr)this[u]=qe[u];for(u of Ir)this[u]=qe[u];for(u of Gr)this[u]=qe[u];for(u of jr)this[u]=new io(u,!1,void 0,this);this.setupGlobalFilters(r,void 0,Te)}setupFromObject(r){let u;for(u of(Te.ifd0=Te.ifd0||Te.image,Te.ifd1=Te.ifd1||Te.thumbnail,Object.assign(this,r),Hr))this[u]=cf(r[u],qe[u]);for(u of Ir)this[u]=cf(r[u],qe[u]);for(u of Gr)this[u]=cf(r[u],qe[u]);for(u of Cf)this[u]=new io(u,qe[u],r[u],this);for(u of Te)this[u]=new io(u,qe[u],r[u],this.tiff);this.setupGlobalFilters(r.pick,r.skip,Te,jr),r.tiff===!0?this.batchEnableWithBool(Te,!0):r.tiff===!1?this.batchEnableWithUserValue(Te,r):Array.isArray(r.tiff)?this.setupGlobalFilters(r.tiff,void 0,Te):typeof r.tiff=="object"&&this.setupGlobalFilters(r.tiff.pick,r.tiff.skip,Te)}batchEnableWithBool(r,u){for(let h of r)this[h].enabled=u}batchEnableWithUserValue(r,u){for(let h of r){let g=u[h];this[h].enabled=g!==!1&&g!==void 0}}setupGlobalFilters(r,u,h,g=h){if(r&&r.length){for(let x of g)this[x].enabled=!1;let y=Rm(r,h);for(let[x,N]of y)$r(this[x].pick,N),this[x].enabled=!0}else if(u&&u.length){let y=Rm(u,h);for(let[x,N]of y)$r(this[x].skip,N)}}filterNestedSegmentTags(){let{ifd0:r,exif:u,xmp:h,iptc:g,icc:y}=this;this.makerNote?u.deps.add(37500):u.skip.add(37500),this.userComment?u.deps.add(37510):u.skip.add(37510),h.enabled||r.skip.add(700),g.enabled||r.skip.add(33723),y.enabled||r.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:r,exif:u,gps:h,interop:g}=this;g.needed&&(u.deps.add(40965),r.deps.add(40965)),u.needed&&r.deps.add(34665),h.needed&&r.deps.add(34853),this.tiff.enabled=Te.some(y=>this[y].enabled===!0)||this.makerNote||this.userComment;for(let y of Te)this[y].finalizeFilters()}get onlyTiff(){return!tp.map(r=>this[r].enabled).some(r=>r===!0)&&this.tiff.enabled}checkLoadedPlugins(){for(let r of Cf)this[r].enabled&&!Be.has(r)&&vf("segment parser",r)}}function Rm(p,r){let u,h,g,y,x=[];for(g of r){for(y of(u=$e.get(g),h=[],u))(p.includes(y[0])||p.includes(y[1]))&&h.push(y[0]);h.length&&x.push([g,h])}return x}function cf(p,r){return p!==void 0?p:r!==void 0?r:void 0}function $r(p,r){for(let u of r)p.add(u)}pt(Of,"default",qe);class Ig{constructor(r){pt(this,"parsers",{}),pt(this,"output",{}),pt(this,"errors",[]),pt(this,"pushToErrors",u=>this.errors.push(u)),this.options=Of.useCached(r)}async read(r){this.file=await jg(r,this.options)}setup(){if(this.fileParser)return;let{file:r}=this,u=r.getUint16(0);for(let[h,g]of Xs)if(g.canHandle(r,u))return this.fileParser=new g(this.options,this.file,this.parsers),r[h]=!0;this.file.close&&this.file.close(),he("Unknown file format")}async parse(){let{output:r,errors:u}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),u.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&u.length>0&&(r.errors=u),Wr(r)}async executeParsers(){let{output:r}=this;await this.fileParser.parse();let u=Object.values(this.parsers).map(async h=>{let g=await h.parse();h.assignToOutput(r,g)});this.options.silentErrors&&(u=u.map(h=>h.catch(this.pushToErrors))),await Promise.all(u)}async extractThumbnail(){this.setup();let{options:r,file:u}=this,h=Be.get("tiff",r);var g;if(u.tiff?g={start:0,type:"tiff"}:u.jpeg&&(g=await this.fileParser.getOrFindSegment("tiff")),g===void 0)return;let y=await this.fileParser.ensureSegmentChunk(g),x=this.parsers.tiff=new h(y,r,u),N=await x.extractThumbnail();return u.close&&u.close(),N}}async function qg(p,r){let u=new Ig(r);return await u.read(p),u.parse()}class iu{constructor(r,u,h){pt(this,"errors",[]),pt(this,"ensureSegmentChunk",async g=>{let y=g.start,x=g.size||65536;if(this.file.chunked)if(this.file.available(y,x))g.chunk=this.file.subarray(y,x);else try{g.chunk=await this.file.readChunk(y,x)}catch(N){he(`Couldn't read segment: ${JSON.stringify(g)}. ${N.message}`)}else this.file.byteLength>y+x?g.chunk=this.file.subarray(y,x):g.size===void 0?g.chunk=this.file.subarray(y):he("Segment unreachable: "+JSON.stringify(g));return g.chunk}),this.extendOptions&&this.extendOptions(r),this.options=r,this.file=u,this.parsers=h}injectSegment(r,u){this.options[r].enabled&&this.createParser(r,u)}createParser(r,u){let h=new(Be.get(r))(u,this.options,this.file);return this.parsers[r]=h}createParsers(r){for(let u of r){let{type:h,chunk:g}=u,y=this.options[h];if(y&&y.enabled){let x=this.parsers[h];x&&x.append||x||this.createParser(h,g)}}}async readSegments(r){let u=r.map(this.ensureSegmentChunk);await Promise.all(u)}}class Pi{static findPosition(r,u){let h=r.getUint16(u+2)+2,g=typeof this.headerLength=="function"?this.headerLength(r,u,h):this.headerLength,y=u+g,x=h-g;return{offset:u,length:h,headerLength:g,start:y,size:x,end:y+x}}static parse(r,u={}){return new this(r,new Of({[this.type]:u}),r).parse()}normalizeInput(r){return r instanceof We?r:new We(r)}constructor(r,u={},h){pt(this,"errors",[]),pt(this,"raw",new Map),pt(this,"handleError",g=>{if(!this.options.silentErrors)throw g;this.errors.push(g.message)}),this.chunk=this.normalizeInput(r),this.file=h,this.type=this.constructor.type,this.globalOptions=this.options=u,this.localOptions=u[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(r,u){let h=Xr.get(u),g=es.get(u),y=$e.get(u),x=this.options[u],N=x.reviveValues&&!!h,A=x.translateValues&&!!g,E=x.translateKeys&&!!y,j={};for(let[X,Y]of r)N&&h.has(X)?Y=h.get(X)(Y):A&&g.has(X)&&(Y=this.translateValue(Y,g.get(X))),E&&y.has(X)&&(X=y.get(X)||X),j[X]=Y;return j}translateValue(r,u){return u[r]||u.DEFAULT||r}assignToOutput(r,u){this.assignObjectToOutput(r,this.constructor.type,u)}assignObjectToOutput(r,u,h){if(this.globalOptions.mergeOutput)return Object.assign(r,h);r[u]?Object.assign(r[u],h):r[u]=h}}pt(Pi,"headerLength",4),pt(Pi,"type",void 0),pt(Pi,"multiSegment",!1),pt(Pi,"canHandle",()=>!1);function Vg(p){return p===192||p===194||p===196||p===219||p===221||p===218||p===254}function Yg(p){return p>=224&&p<=239}function Xg(p,r,u){for(let[h,g]of Be)if(g.canHandle(p,r,u))return h}class Pm extends iu{constructor(...r){super(...r),pt(this,"appSegments",[]),pt(this,"jpegSegments",[]),pt(this,"unknownSegments",[])}static canHandle(r,u){return u===65496}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(r){r===!0?(this.findAll=!0,this.wanted=new Set(Be.keyList())):(r=r===void 0?Be.keyList().filter(u=>this.options[u].enabled):r.filter(u=>this.options[u].enabled&&Be.has(u)),this.findAll=!1,this.remaining=new Set(r),this.wanted=new Set(r)),this.unfinishedMultiSegment=!1}async findAppSegments(r=0,u){this.setupSegmentFinderArgs(u);let{file:h,findAll:g,wanted:y,remaining:x}=this;if(!g&&this.file.chunked&&(g=Array.from(y).some(N=>{let A=Be.get(N),E=this.options[N];return A.multiSegment&&E.multiSegment}),g&&await this.file.readWhole()),r=this.findAppSegmentsInRange(r,h.byteLength),!this.options.onlyTiff&&h.chunked){let N=!1;for(;x.size>0&&!N&&(h.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:A}=h,E=this.appSegments.some(j=>!this.file.available(j.offset||j.start,j.length||j.size));if(N=r>A&&!E?!await h.readNextChunk(r):!await h.readNextChunk(A),(r=this.findAppSegmentsInRange(r,h.byteLength))===void 0)return}}}findAppSegmentsInRange(r,u){u-=2;let h,g,y,x,N,A,{file:E,findAll:j,wanted:X,remaining:Y,options:dt}=this;for(;r<u;r++)if(E.getUint8(r)===255){if(h=E.getUint8(r+1),Yg(h)){if(g=E.getUint16(r+2),y=Xg(E,r,g),y&&X.has(y)&&(x=Be.get(y),N=x.findPosition(E,r),A=dt[y],N.type=y,this.appSegments.push(N),!j&&(x.multiSegment&&A.multiSegment?(this.unfinishedMultiSegment=N.chunkNumber<N.chunkCount,this.unfinishedMultiSegment||Y.delete(y)):Y.delete(y),Y.size===0)))break;dt.recordUnknownSegments&&(N=Pi.findPosition(E,r),N.marker=h,this.unknownSegments.push(N)),r+=g+1}else if(Vg(h)){if(g=E.getUint16(r+2),h===218&&dt.stopAfterSos!==!1)return;dt.recordJpegSegments&&this.jpegSegments.push({offset:r,length:g,marker:h}),r+=g+1}}return r}mergeMultiSegments(){if(!this.appSegments.some(u=>u.multiSegment))return;let r=function(u,h){let g,y,x,N=new Map;for(let A=0;A<u.length;A++)g=u[A],y=g[h],N.has(y)?x=N.get(y):N.set(y,x=[]),x.push(g);return Array.from(N)}(this.appSegments,"type");this.mergedAppSegments=r.map(([u,h])=>{let g=Be.get(u,this.options);return g.handleMultiSegments?{type:u,chunk:g.handleMultiSegments(h)}:h[0]})}getSegment(r){return this.appSegments.find(u=>u.type===r)}async getOrFindSegment(r){let u=this.getSegment(r);return u===void 0&&(await this.findAppSegments(0,[r]),u=this.getSegment(r)),u}}pt(Pm,"type","jpeg"),Xs.set("jpeg",Pm);const Kg=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class Qg extends Pi{parseHeader(){var r=this.chunk.getUint16();r===18761?this.le=!0:r===19789&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(r,u,h=new Map){let{pick:g,skip:y}=this.options[u];g=new Set(g);let x=g.size>0,N=y.size===0,A=this.chunk.getUint16(r);r+=2;for(let E=0;E<A;E++){let j=this.chunk.getUint16(r);if(x){if(g.has(j)&&(h.set(j,this.parseTag(r,j,u)),g.delete(j),g.size===0))break}else!N&&y.has(j)||h.set(j,this.parseTag(r,j,u));r+=12}return h}parseTag(r,u,h){let{chunk:g}=this,y=g.getUint16(r+2),x=g.getUint32(r+4),N=Kg[y];if(N*x<=4?r+=8:r=g.getUint32(r+8),(y<1||y>13)&&he(`Invalid TIFF value type. block: ${h.toUpperCase()}, tag: ${u.toString(16)}, type: ${y}, offset ${r}`),r>g.byteLength&&he(`Invalid TIFF value offset. block: ${h.toUpperCase()}, tag: ${u.toString(16)}, type: ${y}, offset ${r} is outside of chunk size ${g.byteLength}`),y===1)return g.getUint8Array(r,x);if(y===2)return ts(g.getString(r,x));if(y===7)return g.getUint8Array(r,x);if(x===1)return this.parseTagValue(y,r);{let A=new(function(j){switch(j){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(y))(x),E=N;for(let j=0;j<x;j++)A[j]=this.parseTagValue(y,r),r+=E;return A}}parseTagValue(r,u){let{chunk:h}=this;switch(r){case 1:return h.getUint8(u);case 3:return h.getUint16(u);case 4:return h.getUint32(u);case 5:return h.getUint32(u)/h.getUint32(u+4);case 6:return h.getInt8(u);case 8:return h.getInt16(u);case 9:return h.getInt32(u);case 10:return h.getInt32(u)/h.getInt32(u+4);case 11:return h.getFloat(u);case 12:return h.getDouble(u);case 13:return h.getUint32(u);default:he(`Invalid tiff type ${r}`)}}}class ff extends Qg{static canHandle(r,u){return r.getUint8(u+1)===225&&r.getUint32(u+4)===1165519206&&r.getUint16(u+8)===0}async parse(){this.parseHeader();let{options:r}=this;return r.ifd0.enabled&&await this.parseIfd0Block(),r.exif.enabled&&await this.safeParse("parseExifBlock"),r.gps.enabled&&await this.safeParse("parseGpsBlock"),r.interop.enabled&&await this.safeParse("parseInteropBlock"),r.ifd1.enabled&&await this.safeParse("parseThumbnailBlock"),this.createOutput()}safeParse(r){let u=this[r]();return u.catch!==void 0&&(u=u.catch(this.handleError)),u}findIfd0Offset(){this.ifd0Offset===void 0&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(this.ifd1Offset===void 0){this.findIfd0Offset();let r=this.chunk.getUint16(this.ifd0Offset),u=this.ifd0Offset+2+12*r;this.ifd1Offset=this.chunk.getUint32(u)}}parseBlock(r,u){let h=new Map;return this[u]=h,this.parseTags(r,u,h),h}async parseIfd0Block(){if(this.ifd0)return;let{file:r}=this;this.findIfd0Offset(),this.ifd0Offset<8&&he("Malformed EXIF data"),!r.chunked&&this.ifd0Offset>r.byteLength&&he(`IFD0 offset points to outside of file.
this.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${r.byteLength}`),r.tiff&&await r.ensureChunk(this.ifd0Offset,gf(this.options));let u=this.parseBlock(this.ifd0Offset,"ifd0");return u.size!==0?(this.exifOffset=u.get(34665),this.interopOffset=u.get(40965),this.gpsOffset=u.get(34853),this.xmp=u.get(700),this.iptc=u.get(33723),this.icc=u.get(34675),this.options.sanitize&&(u.delete(34665),u.delete(40965),u.delete(34853),u.delete(700),u.delete(33723),u.delete(34675)),u):void 0}async parseExifBlock(){if(this.exif||(this.ifd0||await this.parseIfd0Block(),this.exifOffset===void 0))return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,gf(this.options));let r=this.parseBlock(this.exifOffset,"exif");return this.interopOffset||(this.interopOffset=r.get(40965)),this.makerNote=r.get(37500),this.userComment=r.get(37510),this.options.sanitize&&(r.delete(40965),r.delete(37500),r.delete(37510)),this.unpack(r,41728),this.unpack(r,41729),r}unpack(r,u){let h=r.get(u);h&&h.length===1&&r.set(u,h[0])}async parseGpsBlock(){if(this.gps||(this.ifd0||await this.parseIfd0Block(),this.gpsOffset===void 0))return;let r=this.parseBlock(this.gpsOffset,"gps");return r&&r.has(2)&&r.has(4)&&(r.set("latitude",Bm(...r.get(2),r.get(1))),r.set("longitude",Bm(...r.get(4),r.get(3)))),r}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),this.interopOffset!==void 0||this.exif||await this.parseExifBlock(),this.interopOffset!==void 0))return this.parseBlock(this.interopOffset,"interop")}async parseThumbnailBlock(r=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||r))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,"ifd1"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),this.ifd1===void 0)return;let r=this.ifd1.get(513),u=this.ifd1.get(514);return this.chunk.getUint8Array(r,u)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let r,u,h,g={};for(u of Te)if(r=this[u],!Wm(r))if(h=this.canTranslate?this.translateBlock(r,u):Object.fromEntries(r),this.options.mergeOutput){if(u==="ifd1")continue;Object.assign(g,h)}else g[u]=h;return this.makerNote&&(g.makerNote=this.makerNote),this.userComment&&(g.userComment=this.userComment),g}assignToOutput(r,u){if(this.globalOptions.mergeOutput)Object.assign(r,u);else for(let[h,g]of Object.entries(u))this.assignObjectToOutput(r,h,g)}}function Bm(p,r,u,h){var g=p+r/60+u/3600;return h!=="S"&&h!=="W"||(g*=-1),g}pt(ff,"type","tiff"),pt(ff,"headerLength",10),Be.set("tiff",ff);const Df={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};Object.assign({},Df,{firstChunkSize:4e4,gps:[1,2,3,4]});Object.assign({},Df,{tiff:!1,ifd1:!0,mergeOutput:!1});Object.assign({},Df,{firstChunkSize:4e4,ifd0:[274]});if(typeof navigator=="object"){let p=navigator.userAgent;if(p.includes("iPad")||p.includes("iPhone")){let r=p.match(/OS (\d+)_(\d+)/);if(r){let[,u,h]=r}}else if(p.includes("OS X 10")){let[,r]=p.match(/OS X 10[_.](\d+)/)}if(p.includes("Chrome/")){let[,r]=p.match(/Chrome\/(\d+)/)}else if(p.includes("Firefox/")){let[,r]=p.match(/Firefox\/(\d+)/)}}class Fg extends We{constructor(...r){super(...r),pt(this,"ranges",new Jg),this.byteLength!==0&&this.ranges.add(0,this.byteLength)}_tryExtend(r,u,h){if(r===0&&this.byteLength===0&&h){let g=new DataView(h.buffer||h,h.byteOffset,h.byteLength);this._swapDataView(g)}else{let g=r+u;if(g>this.byteLength){let{dataView:y}=this._extend(g);this._swapDataView(y)}}}_extend(r){let u;u=Af?Lf.allocUnsafe(r):new Uint8Array(r);let h=new DataView(u.buffer,u.byteOffset,u.byteLength);return u.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:u,dataView:h}}subarray(r,u,h=!1){return u=u||this._lengthToEnd(r),h&&this._tryExtend(r,u),this.ranges.add(r,u),super.subarray(r,u)}set(r,u,h=!1){h&&this._tryExtend(u,r.byteLength,r);let g=super.set(r,u);return this.ranges.add(u,g.byteLength),g}async ensureChunk(r,u){this.chunked&&(this.ranges.available(r,u)||await this.readChunk(r,u))}available(r,u){return this.ranges.available(r,u)}}class Jg{constructor(){pt(this,"list",[])}get length(){return this.list.length}add(r,u,h=0){let g=r+u,y=this.list.filter(x=>Um(r,x.offset,g)||Um(r,x.end,g));if(y.length>0){r=Math.min(r,...y.map(N=>N.offset)),g=Math.max(g,...y.map(N=>N.end)),u=g-r;let x=y.shift();x.offset=r,x.length=u,x.end=g,this.list=this.list.filter(N=>!y.includes(N))}else this.list.push({offset:r,length:u,end:g})}available(r,u){let h=r+u;return this.list.some(g=>g.offset<=r&&h<=g.end)}}function Um(p,r,u){return p<=r&&r<=u}class nu extends Fg{constructor(r,u){super(0),pt(this,"chunksRead",0),this.input=r,this.options=u}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(r=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let u=this.options.chunkSize,h=await this.readChunk(r,u);return!!h&&h.byteLength===u}async readChunk(r,u){if(this.chunksRead++,(u=this.safeWrapAddress(r,u))!==0)return this._readChunk(r,u)}safeWrapAddress(r,u){return this.size!==void 0&&r+u>this.size?Math.max(0,this.size-r):u}get nextChunkOffset(){if(this.ranges.list.length!==0)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return this.size!==void 0&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}Ks.set("blob",class extends nu{async readWhole(){this.chunked=!1;let p=await Tf(this.input);this._swapArrayBuffer(p)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(p,r){let u=r?p+r:void 0,h=this.input.slice(p,u),g=await Tf(h);return this.set(g,p,!0)}});Ks.set("url",class extends nu{async readWhole(){this.chunked=!1;let p=await bf(this.input);p instanceof ArrayBuffer?this._swapArrayBuffer(p):p instanceof Uint8Array&&this._swapBuffer(p)}async _readChunk(p,r){let u=r?p+r-1:void 0,h=this.options.httpHeaders||{};(p||u)&&(h.range=`bytes=${[p,u].join("-")}`);let g=await Ef(this.input,{headers:h}),y=await g.arrayBuffer(),x=y.byteLength;if(g.status!==416)return x!==r&&(this.size=p+x),this.set(y,p,!0)}});We.prototype.getUint64=function(p){let r=this.getUint32(p),u=this.getUint32(p+4);return r<1048575?r<<32|u:typeof Zr!==void 0?(console.warn("Using BigInt because of type 64uint but JS can only handle 53b numbers."),Zr(r)<<Zr(32)|Zr(u)):void he("Trying to read 64b value but JS can only handle 53b numbers.")};class Wg extends iu{parseBoxes(r=0){let u=[];for(;r<this.file.byteLength-4;){let h=this.parseBoxHead(r);if(u.push(h),h.length===0)break;r+=h.length}return u}parseSubBoxes(r){r.boxes=this.parseBoxes(r.start)}findBox(r,u){return r.boxes===void 0&&this.parseSubBoxes(r),r.boxes.find(h=>h.kind===u)}parseBoxHead(r){let u=this.file.getUint32(r),h=this.file.getString(r+4,4),g=r+8;return u===1&&(u=this.file.getUint64(r+8),g+=8),{offset:r,length:u,kind:h,start:g}}parseBoxFullHead(r){if(r.version!==void 0)return;let u=this.file.getUint32(r.start);r.version=u>>24,r.start+=4}}class np extends Wg{static canHandle(r,u){if(u!==0)return!1;let h=r.getUint16(2);if(h>50)return!1;let g=16,y=[];for(;g<h;)y.push(r.getString(g,4)),g+=4;return y.includes(this.type)}async parse(){let r=this.file.getUint32(0),u=this.parseBoxHead(r);for(;u.kind!=="meta";)r+=u.length,await this.file.ensureChunk(r,16),u=this.parseBoxHead(r);await this.file.ensureChunk(u.offset,u.length),this.parseBoxFullHead(u),this.parseSubBoxes(u),this.options.icc.enabled&&await this.findIcc(u),this.options.tiff.enabled&&await this.findExif(u)}async registerSegment(r,u,h){await this.file.ensureChunk(u,h);let g=this.file.subarray(u,h);this.createParser(r,g)}async findIcc(r){let u=this.findBox(r,"iprp");if(u===void 0)return;let h=this.findBox(u,"ipco");if(h===void 0)return;let g=this.findBox(h,"colr");g!==void 0&&await this.registerSegment("icc",g.offset+12,g.length)}async findExif(r){let u=this.findBox(r,"iinf");if(u===void 0)return;let h=this.findBox(r,"iloc");if(h===void 0)return;let g=this.findExifLocIdInIinf(u),y=this.findExtentInIloc(h,g);if(y===void 0)return;let[x,N]=y;await this.file.ensureChunk(x,N);let A=4+this.file.getUint32(x);x+=A,N-=A,await this.registerSegment("tiff",x,N)}findExifLocIdInIinf(r){this.parseBoxFullHead(r);let u,h,g,y,x=r.start,N=this.file.getUint16(x);for(x+=2;N--;){if(u=this.parseBoxHead(x),this.parseBoxFullHead(u),h=u.start,u.version>=2&&(g=u.version===3?4:2,y=this.file.getString(h+g+2,4),y==="Exif"))return this.file.getUintBytes(h,g);x+=u.length}}get8bits(r){let u=this.file.getUint8(r);return[u>>4,15&u]}findExtentInIloc(r,u){this.parseBoxFullHead(r);let h=r.start,[g,y]=this.get8bits(h++),[x,N]=this.get8bits(h++),A=r.version===2?4:2,E=r.version===1||r.version===2?2:0,j=N+g+y,X=r.version===2?4:2,Y=this.file.getUintBytes(h,X);for(h+=X;Y--;){let dt=this.file.getUintBytes(h,A);h+=A+E+2+x;let ht=this.file.getUint16(h);if(h+=2,dt===u)return ht>1&&console.warn(`ILOC box has more than one extent but we're only processing one
Please create an issue at https://github.com/MikeKovarik/exifr with this file`),[this.file.getUintBytes(h+N,g),this.file.getUintBytes(h+N+g,y)];h+=ht*j}}}class ap extends np{}pt(ap,"type","heic");class km extends np{}pt(km,"type","avif"),Xs.set("heic",ap),Xs.set("avif",km),Me($e,["ifd0","ifd1"],[[256,"ImageWidth"],[257,"ImageHeight"],[258,"BitsPerSample"],[259,"Compression"],[262,"PhotometricInterpretation"],[270,"ImageDescription"],[271,"Make"],[272,"Model"],[273,"StripOffsets"],[274,"Orientation"],[277,"SamplesPerPixel"],[278,"RowsPerStrip"],[279,"StripByteCounts"],[282,"XResolution"],[283,"YResolution"],[284,"PlanarConfiguration"],[296,"ResolutionUnit"],[301,"TransferFunction"],[305,"Software"],[306,"ModifyDate"],[315,"Artist"],[316,"HostComputer"],[317,"Predictor"],[318,"WhitePoint"],[319,"PrimaryChromaticities"],[513,"ThumbnailOffset"],[514,"ThumbnailLength"],[529,"YCbCrCoefficients"],[530,"YCbCrSubSampling"],[531,"YCbCrPositioning"],[532,"ReferenceBlackWhite"],[700,"ApplicationNotes"],[33432,"Copyright"],[33723,"IPTC"],[34665,"ExifIFD"],[34675,"ICC"],[34853,"GpsIFD"],[330,"SubIFD"],[40965,"InteropIFD"],[40091,"XPTitle"],[40092,"XPComment"],[40093,"XPAuthor"],[40094,"XPKeywords"],[40095,"XPSubject"]]),Me($e,"exif",[[33434,"ExposureTime"],[33437,"FNumber"],[34850,"ExposureProgram"],[34852,"SpectralSensitivity"],[34855,"ISO"],[34858,"TimeZoneOffset"],[34859,"SelfTimerMode"],[34864,"SensitivityType"],[34865,"StandardOutputSensitivity"],[34866,"RecommendedExposureIndex"],[34867,"ISOSpeed"],[34868,"ISOSpeedLatitudeyyy"],[34869,"ISOSpeedLatitudezzz"],[36864,"ExifVersion"],[36867,"DateTimeOriginal"],[36868,"CreateDate"],[36873,"GooglePlusUploadCode"],[36880,"OffsetTime"],[36881,"OffsetTimeOriginal"],[36882,"OffsetTimeDigitized"],[37121,"ComponentsConfiguration"],[37122,"CompressedBitsPerPixel"],[37377,"ShutterSpeedValue"],[37378,"ApertureValue"],[37379,"BrightnessValue"],[37380,"ExposureCompensation"],[37381,"MaxApertureValue"],[37382,"SubjectDistance"],[37383,"MeteringMode"],[37384,"LightSource"],[37385,"Flash"],[37386,"FocalLength"],[37393,"ImageNumber"],[37394,"SecurityClassification"],[37395,"ImageHistory"],[37396,"SubjectArea"],[37500,"MakerNote"],[37510,"UserComment"],[37520,"SubSecTime"],[37521,"SubSecTimeOriginal"],[37522,"SubSecTimeDigitized"],[37888,"AmbientTemperature"],[37889,"Humidity"],[37890,"Pressure"],[37891,"WaterDepth"],[37892,"Acceleration"],[37893,"CameraElevationAngle"],[40960,"FlashpixVersion"],[40961,"ColorSpace"],[40962,"ExifImageWidth"],[40963,"ExifImageHeight"],[40964,"RelatedSoundFile"],[41483,"FlashEnergy"],[41486,"FocalPlaneXResolution"],[41487,"FocalPlaneYResolution"],[41488,"FocalPlaneResolutionUnit"],[41492,"SubjectLocation"],[41493,"ExposureIndex"],[41495,"SensingMethod"],[41728,"FileSource"],[41729,"SceneType"],[41730,"CFAPattern"],[41985,"CustomRendered"],[41986,"ExposureMode"],[41987,"WhiteBalance"],[41988,"DigitalZoomRatio"],[41989,"FocalLengthIn35mmFormat"],[41990,"SceneCaptureType"],[41991,"GainControl"],[41992,"Contrast"],[41993,"Saturation"],[41994,"Sharpness"],[41996,"SubjectDistanceRange"],[42016,"ImageUniqueID"],[42032,"OwnerName"],[42033,"SerialNumber"],[42034,"LensInfo"],[42035,"LensMake"],[42036,"LensModel"],[42037,"LensSerialNumber"],[42080,"CompositeImage"],[42081,"CompositeImageCount"],[42082,"CompositeImageExposureTimes"],[42240,"Gamma"],[59932,"Padding"],[59933,"OffsetSchema"],[65e3,"OwnerName"],[65001,"SerialNumber"],[65002,"Lens"],[65100,"RawFile"],[65101,"Converter"],[65102,"WhiteBalance"],[65105,"Exposure"],[65106,"Shadows"],[65107,"Brightness"],[65108,"Contrast"],[65109,"Saturation"],[65110,"Sharpness"],[65111,"Smoothness"],[65112,"MoireFilter"],[40965,"InteropIFD"]]),Me($e,"gps",[[0,"GPSVersionID"],[1,"GPSLatitudeRef"],[2,"GPSLatitude"],[3,"GPSLongitudeRef"],[4,"GPSLongitude"],[5,"GPSAltitudeRef"],[6,"GPSAltitude"],[7,"GPSTimeStamp"],[8,"GPSSatellites"],[9,"GPSStatus"],[10,"GPSMeasureMode"],[11,"GPSDOP"],[12,"GPSSpeedRef"],[13,"GPSSpeed"],[14,"GPSTrackRef"],[15,"GPSTrack"],[16,"GPSImgDirectionRef"],[17,"GPSImgDirection"],[18,"GPSMapDatum"],[19,"GPSDestLatitudeRef"],[20,"GPSDestLatitude"],[21,"GPSDestLongitudeRef"],[22,"GPSDestLongitude"],[23,"GPSDestBearingRef"],[24,"GPSDestBearing"],[25,"GPSDestDistanceRef"],[26,"GPSDestDistance"],[27,"GPSProcessingMethod"],[28,"GPSAreaInformation"],[29,"GPSDateStamp"],[30,"GPSDifferential"],[31,"GPSHPositioningError"]]),Me(es,["ifd0","ifd1"],[[274,{1:"Horizontal (normal)",2:"Mirror horizontal",3:"Rotate 180",4:"Mirror vertical",5:"Mirror horizontal and rotate 270 CW",6:"Rotate 90 CW",7:"Mirror horizontal and rotate 90 CW",8:"Rotate 270 CW"}],[296,{1:"None",2:"inches",3:"cm"}]]);let so=Me(es,"exif",[[34850,{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"}],[37121,{0:"-",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}],[37383,{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"}],[37384,{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"}],[37385,{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"}],[41495,{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"}],[41728,{1:"Film Scanner",2:"Reflection Print Scanner",3:"Digital Camera"}],[41729,{1:"Directly photographed"}],[41985,{0:"Normal",1:"Custom",2:"HDR (no original saved)",3:"HDR (original saved)",4:"Original (for HDR)",6:"Panorama",7:"Portrait HDR",8:"Portrait"}],[41986,{0:"Auto",1:"Manual",2:"Auto bracket"}],[41987,{0:"Auto",1:"Manual"}],[41990,{0:"Standard",1:"Landscape",2:"Portrait",3:"Night",4:"Other"}],[41991,{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"}],[41996,{0:"Unknown",1:"Macro",2:"Close",3:"Distant"}],[42080,{0:"Unknown",1:"Not a Composite Image",2:"General Composite Image",3:"Composite Image Captured While Shooting"}]]);const Zm={1:"No absolute unit of measurement",2:"Inch",3:"Centimeter"};so.set(37392,Zm),so.set(41488,Zm);const hf={0:"Normal",1:"Low",2:"High"};function Hm(p){return typeof p=="object"&&p.length!==void 0?p[0]:p}function jm(p){let r=Array.from(p).slice(1);return r[1]>15&&(r=r.map(u=>String.fromCharCode(u))),r[2]!=="0"&&r[2]!==0||r.pop(),r.join(".")}function df(p){if(typeof p=="string"){var[r,u,h,g,y,x]=p.trim().split(/[-: ]/g).map(Number),N=new Date(r,u-1,h);return Number.isNaN(g)||Number.isNaN(y)||Number.isNaN(x)||(N.setHours(g),N.setMinutes(y),N.setSeconds(x)),Number.isNaN(+N)?p:N}}function no(p){if(typeof p=="string")return p;let r=[];if(p[1]===0&&p[p.length-1]===0)for(let u=0;u<p.length;u+=2)r.push(Gm(p[u+1],p[u]));else for(let u=0;u<p.length;u+=2)r.push(Gm(p[u],p[u+1]));return ts(String.fromCodePoint(...r))}function Gm(p,r){return p<<8|r}so.set(41992,hf),so.set(41993,hf),so.set(41994,hf),Me(Xr,["ifd0","ifd1"],[[50827,function(p){return typeof p!="string"?$m(p):p}],[306,df],[40091,no],[40092,no],[40093,no],[40094,no],[40095,no]]),Me(Xr,"exif",[[40960,jm],[36864,jm],[36867,df],[36868,df],[40962,Hm],[40963,Hm]]),Me(Xr,"gps",[[0,p=>Array.from(p).join(".")],[7,p=>Array.from(p).join(":")]]);class mf extends Pi{static canHandle(r,u){return r.getUint8(u+1)===225&&r.getUint32(u+4)===1752462448&&r.getString(u+4,20)==="http://ns.adobe.com/"}static headerLength(r,u){return r.getString(u+4,34)==="http://ns.adobe.com/xmp/extension/"?79:33}static findPosition(r,u){let h=super.findPosition(r,u);return h.multiSegment=h.extended=h.headerLength===79,h.multiSegment?(h.chunkCount=r.getUint8(u+72),h.chunkNumber=r.getUint8(u+76),r.getUint8(u+77)!==0&&h.chunkNumber++):(h.chunkCount=1/0,h.chunkNumber=-1),h}static handleMultiSegments(r){return r.map(u=>u.chunk.getString()).join("")}normalizeInput(r){return typeof r=="string"?r:We.from(r).getString()}parse(r=this.chunk){if(!this.localOptions.parse)return r;r=function(y){let x={},N={};for(let A of rp)x[A]=[],N[A]=0;return y.replace(i_,(A,E,j)=>{if(E==="<"){let X=++N[j];return x[j].push(X),`${A}#${X}`}return`${A}#${x[j].pop()}`})}(r);let u=Ys.findAll(r,"rdf","Description");u.length===0&&u.push(new Ys("rdf","Description",void 0,r));let h,g={};for(let y of u)for(let x of y.properties)h=e_(x.ns,g),sp(x,h);return function(y){let x;for(let N in y)x=y[N]=Wr(y[N]),x===void 0&&delete y[N];return Wr(y)}(g)}assignToOutput(r,u){if(this.localOptions.parse)for(let[h,g]of Object.entries(u))switch(h){case"tiff":this.assignObjectToOutput(r,"ifd0",g);break;case"exif":this.assignObjectToOutput(r,"exif",g);break;case"xmlns":break;default:this.assignObjectToOutput(r,h,g)}else r.xmp=u}}pt(mf,"type","xmp"),pt(mf,"multiSegment",!0),Be.set("xmp",mf);class tu{static findAll(r){return lp(r,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=("[^"]*"|'[^']*')/gm).map(tu.unpackMatch)}static unpackMatch(r){let u=r[1],h=r[2],g=r[3].slice(1,-1);return g=op(g),new tu(u,h,g)}constructor(r,u,h){this.ns=r,this.name=u,this.value=h}serialize(){return this.value}}class Ys{static findAll(r,u,h){if(u!==void 0||h!==void 0){u=u||"[\\w\\d-]+",h=h||"[\\w\\d-]+";var g=new RegExp(`<(${u}):(${h})(#\\d+)?((\\s+?[\\w\\d-:]+=("[^"]*"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)`,"gm")}else g=/<([\w\d-]+):([\w\d-]+)(#\d+)?((\s+?[\w\d-:]+=("[^"]*"|'[^']*'))*\s*)(\/>|>([\s\S]*?)<\/\1:\2\3>)/gm;return lp(r,g).map(Ys.unpackMatch)}static unpackMatch(r){let u=r[1],h=r[2],g=r[4],y=r[8];return new Ys(u,h,g,y)}constructor(r,u,h,g){this.ns=r,this.name=u,this.attrString=h,this.innerXml=g,this.attrs=tu.findAll(h),this.children=Ys.findAll(g),this.value=this.children.length===0?op(g):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return this.value!==void 0&&this.attrs.length===0&&this.children.length===0}get isListContainer(){return this.children.length===1&&this.children[0].isList}get isList(){let{ns:r,name:u}=this;return r==="rdf"&&(u==="Seq"||u==="Bag"||u==="Alt")}get isListItem(){return this.ns==="rdf"&&this.name==="li"}serialize(){if(this.properties.length===0&&this.value===void 0)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return t_(this.children.map($g));if(this.isListItem&&this.children.length===1&&this.attrs.length===0)return this.children[0].serialize();let r={};for(let u of this.properties)sp(u,r);return this.value!==void 0&&(r.value=this.value),Wr(r)}}function sp(p,r){let u=p.serialize();u!==void 0&&(r[p.name]=u)}var $g=p=>p.serialize(),t_=p=>p.length===1?p[0]:p,e_=(p,r)=>r[p]?r[p]:r[p]={};function lp(p,r){let u,h=[];if(!p)return h;for(;(u=r.exec(p))!==null;)h.push(u);return h}function op(p){if(function(h){return h==null||h==="null"||h==="undefined"||h===""||h.trim()===""}(p))return;let r=Number(p);if(!Number.isNaN(r))return r;let u=p.toLowerCase();return u==="true"||u!=="false"&&p.trim()}const rp=["rdf:li","rdf:Seq","rdf:Bag","rdf:Alt","rdf:Description"],i_=new RegExp(`(<|\\/)(${rp.join("|")})`,"g");let Im=Jr("fs",p=>p.promises);Ks.set("fs",class extends nu{async readWhole(){this.chunked=!1,this.fs=await Im;let p=await this.fs.readFile(this.input);this._swapBuffer(p)}async readChunked(){this.chunked=!0,this.fs=await Im,await this.open(),await this.readChunk(0,this.options.firstChunkSize)}async open(){this.fh===void 0&&(this.fh=await this.fs.open(this.input,"r"),this.size=(await this.fh.stat(this.input)).size)}async _readChunk(p,r){this.fh===void 0&&await this.open(),p+r>this.size&&(r=this.size-p);var u=this.subarray(p,r,!0);return await this.fh.read(u.dataView,0,r,p),u}async close(){if(this.fh){let p=this.fh;this.fh=void 0,await p.close()}}});Ks.set("base64",class extends nu{constructor(...p){super(...p),this.input=this.input.replace(/^data:([^;]+);base64,/gim,""),this.size=this.input.length/4*3,this.input.endsWith("==")?this.size-=2:this.input.endsWith("=")&&(this.size-=1)}async _readChunk(p,r){let u,h,g=this.input;p===void 0?(p=0,u=0,h=0):(u=4*Math.floor(p/3),h=p-u/4*3),r===void 0&&(r=this.size);let y=p+r,x=u+4*Math.ceil(y/3);g=g.slice(u,x);let N=Math.min(r,this.size-p);if(Af){let A=Lf.from(g,"base64").slice(h,h+N);return this.set(A,p,!0)}{let A=this.subarray(p,N,!0),E=atob(g),j=A.toUint8();for(let X=0;X<N;X++)j[X]=E.charCodeAt(h+X);return A}}});class qm extends iu{static canHandle(r,u){return u===18761||u===19789}extendOptions(r){let{ifd0:u,xmp:h,iptc:g,icc:y}=r;h.enabled&&u.deps.add(700),g.enabled&&u.deps.add(33723),y.enabled&&u.deps.add(34675),u.finalizeFilters()}async parse(){let{tiff:r,xmp:u,iptc:h,icc:g}=this.options;if(r.enabled||u.enabled||h.enabled||g.enabled){let y=Math.max(gf(this.options),this.options.chunkSize);await this.file.ensureChunk(0,y),this.createParser("tiff",this.file),this.parsers.tiff.parseHeader(),await this.parsers.tiff.parseIfd0Block(),this.adaptTiffPropAsSegment("xmp"),this.adaptTiffPropAsSegment("iptc"),this.adaptTiffPropAsSegment("icc")}}adaptTiffPropAsSegment(r){if(this.parsers.tiff[r]){let u=this.parsers.tiff[r];this.injectSegment(r,u)}}}pt(qm,"type","tiff"),Xs.set("tiff",qm);let n_=Jr("zlib");const a_=["ihdr","iccp","text","itxt","exif"];class Vm extends iu{constructor(...r){super(...r),pt(this,"catchError",u=>this.errors.push(u)),pt(this,"metaChunks",[]),pt(this,"unknownChunks",[])}static canHandle(r,u){return u===35152&&r.getUint32(0)===2303741511&&r.getUint32(4)===218765834}async parse(){let{file:r}=this;await this.findPngChunksInRange(8,r.byteLength),await this.readSegments(this.metaChunks),this.findIhdr(),this.parseTextChunks(),await this.findExif().catch(this.catchError),await this.findXmp().catch(this.catchError),await this.findIcc().catch(this.catchError)}async findPngChunksInRange(r,u){let{file:h}=this;for(;r<u;){let g=h.getUint32(r),y=h.getUint32(r+4),x=h.getString(r+4,4).toLowerCase(),N=g+4+4+4,A={type:x,offset:r,length:N,start:r+4+4,size:g,marker:y};a_.includes(x)?this.metaChunks.push(A):this.unknownChunks.push(A),r+=N}}parseTextChunks(){let r=this.metaChunks.filter(u=>u.type==="text");for(let u of r){let[h,g]=this.file.getString(u.start,u.size).split("\0");this.injectKeyValToIhdr(h,g)}}injectKeyValToIhdr(r,u){let h=this.parsers.ihdr;h&&h.raw.set(r,u)}findIhdr(){let r=this.metaChunks.find(u=>u.type==="ihdr");r&&this.options.ihdr.enabled!==!1&&this.createParser("ihdr",r.chunk)}async findExif(){let r=this.metaChunks.find(u=>u.type==="exif");r&&this.injectSegment("tiff",r.chunk)}async findXmp(){let r=this.metaChunks.filter(u=>u.type==="itxt");for(let u of r)u.chunk.getString(0,17)==="XML:com.adobe.xmp"&&this.injectSegment("xmp",u.chunk)}async findIcc(){let r=this.metaChunks.find(N=>N.type==="iccp");if(!r)return;let{chunk:u}=r,h=u.getUint8Array(0,81),g=0;for(;g<80&&h[g]!==0;)g++;let y=g+2,x=u.getString(0,g);if(this.injectKeyValToIhdr("ProfileName",x),Fr){let N=await n_,A=u.getUint8Array(y);A=N.inflateSync(A),this.injectSegment("icc",A)}}}pt(Vm,"type","png"),Xs.set("png",Vm),Me($e,"interop",[[1,"InteropIndex"],[2,"InteropVersion"],[4096,"RelatedImageFileFormat"],[4097,"RelatedImageWidth"],[4098,"RelatedImageHeight"]]),xf($e,"ifd0",[[11,"ProcessingSoftware"],[254,"SubfileType"],[255,"OldSubfileType"],[263,"Thresholding"],[264,"CellWidth"],[265,"CellLength"],[266,"FillOrder"],[269,"DocumentName"],[280,"MinSampleValue"],[281,"MaxSampleValue"],[285,"PageName"],[286,"XPosition"],[287,"YPosition"],[290,"GrayResponseUnit"],[297,"PageNumber"],[321,"HalftoneHints"],[322,"TileWidth"],[323,"TileLength"],[332,"InkSet"],[337,"TargetPrinter"],[18246,"Rating"],[18249,"RatingPercent"],[33550,"PixelScale"],[34264,"ModelTransform"],[34377,"PhotoshopSettings"],[50706,"DNGVersion"],[50707,"DNGBackwardVersion"],[50708,"UniqueCameraModel"],[50709,"LocalizedCameraModel"],[50736,"DNGLensInfo"],[50739,"ShadowScale"],[50740,"DNGPrivateData"],[33920,"IntergraphMatrix"],[33922,"ModelTiePoint"],[34118,"SEMInfo"],[34735,"GeoTiffDirectory"],[34736,"GeoTiffDoubleParams"],[34737,"GeoTiffAsciiParams"],[50341,"PrintIM"],[50721,"ColorMatrix1"],[50722,"ColorMatrix2"],[50723,"CameraCalibration1"],[50724,"CameraCalibration2"],[50725,"ReductionMatrix1"],[50726,"ReductionMatrix2"],[50727,"AnalogBalance"],[50728,"AsShotNeutral"],[50729,"AsShotWhiteXY"],[50730,"BaselineExposure"],[50731,"BaselineNoise"],[50732,"BaselineSharpness"],[50734,"LinearResponseLimit"],[50735,"CameraSerialNumber"],[50741,"MakerNoteSafety"],[50778,"CalibrationIlluminant1"],[50779,"CalibrationIlluminant2"],[50781,"RawDataUniqueID"],[50827,"OriginalRawFileName"],[50828,"OriginalRawFileData"],[50831,"AsShotICCProfile"],[50832,"AsShotPreProfileMatrix"],[50833,"CurrentICCProfile"],[50834,"CurrentPreProfileMatrix"],[50879,"ColorimetricReference"],[50885,"SRawType"],[50898,"PanasonicTitle"],[50899,"PanasonicTitle2"],[50931,"CameraCalibrationSig"],[50932,"ProfileCalibrationSig"],[50933,"ProfileIFD"],[50934,"AsShotProfileName"],[50936,"ProfileName"],[50937,"ProfileHueSatMapDims"],[50938,"ProfileHueSatMapData1"],[50939,"ProfileHueSatMapData2"],[50940,"ProfileToneCurve"],[50941,"ProfileEmbedPolicy"],[50942,"ProfileCopyright"],[50964,"ForwardMatrix1"],[50965,"ForwardMatrix2"],[50966,"PreviewApplicationName"],[50967,"PreviewApplicationVersion"],[50968,"PreviewSettingsName"],[50969,"PreviewSettingsDigest"],[50970,"PreviewColorSpace"],[50971,"PreviewDateTime"],[50972,"RawImageDigest"],[50973,"OriginalRawFileDigest"],[50981,"ProfileLookTableDims"],[50982,"ProfileLookTableData"],[51043,"TimeCodes"],[51044,"FrameRate"],[51058,"TStop"],[51081,"ReelName"],[51089,"OriginalDefaultFinalSize"],[51090,"OriginalBestQualitySize"],[51091,"OriginalDefaultCropSize"],[51105,"CameraLabel"],[51107,"ProfileHueSatMapEncoding"],[51108,"ProfileLookTableEncoding"],[51109,"BaselineExposureOffset"],[51110,"DefaultBlackRender"],[51111,"NewRawImageDigest"],[51112,"RawToPreviewGain"]]);let Ym=[[273,"StripOffsets"],[279,"StripByteCounts"],[288,"FreeOffsets"],[289,"FreeByteCounts"],[291,"GrayResponseCurve"],[292,"T4Options"],[293,"T6Options"],[300,"ColorResponseUnit"],[320,"ColorMap"],[324,"TileOffsets"],[325,"TileByteCounts"],[326,"BadFaxLines"],[327,"CleanFaxData"],[328,"ConsecutiveBadFaxLines"],[330,"SubIFD"],[333,"InkNames"],[334,"NumberofInks"],[336,"DotRange"],[338,"ExtraSamples"],[339,"SampleFormat"],[340,"SMinSampleValue"],[341,"SMaxSampleValue"],[342,"TransferRange"],[343,"ClipPath"],[344,"XClipPathUnits"],[345,"YClipPathUnits"],[346,"Indexed"],[347,"JPEGTables"],[351,"OPIProxy"],[400,"GlobalParametersIFD"],[401,"ProfileType"],[402,"FaxProfile"],[403,"CodingMethods"],[404,"VersionYear"],[405,"ModeNumber"],[433,"Decode"],[434,"DefaultImageColor"],[435,"T82Options"],[437,"JPEGTables"],[512,"JPEGProc"],[515,"JPEGRestartInterval"],[517,"JPEGLosslessPredictors"],[518,"JPEGPointTransforms"],[519,"JPEGQTables"],[520,"JPEGDCTables"],[521,"JPEGACTables"],[559,"StripRowCounts"],[999,"USPTOMiscellaneous"],[18247,"XP_DIP_XML"],[18248,"StitchInfo"],[28672,"SonyRawFileType"],[28688,"SonyToneCurve"],[28721,"VignettingCorrection"],[28722,"VignettingCorrParams"],[28724,"ChromaticAberrationCorrection"],[28725,"ChromaticAberrationCorrParams"],[28726,"DistortionCorrection"],[28727,"DistortionCorrParams"],[29895,"SonyCropTopLeft"],[29896,"SonyCropSize"],[32781,"ImageID"],[32931,"WangTag1"],[32932,"WangAnnotation"],[32933,"WangTag3"],[32934,"WangTag4"],[32953,"ImageReferencePoints"],[32954,"RegionXformTackPoint"],[32955,"WarpQuadrilateral"],[32956,"AffineTransformMat"],[32995,"Matteing"],[32996,"DataType"],[32997,"ImageDepth"],[32998,"TileDepth"],[33300,"ImageFullWidth"],[33301,"ImageFullHeight"],[33302,"TextureFormat"],[33303,"WrapModes"],[33304,"FovCot"],[33305,"MatrixWorldToScreen"],[33306,"MatrixWorldToCamera"],[33405,"Model2"],[33421,"CFARepeatPatternDim"],[33422,"CFAPattern2"],[33423,"BatteryLevel"],[33424,"KodakIFD"],[33445,"MDFileTag"],[33446,"MDScalePixel"],[33447,"MDColorTable"],[33448,"MDLabName"],[33449,"MDSampleInfo"],[33450,"MDPrepDate"],[33451,"MDPrepTime"],[33452,"MDFileUnits"],[33589,"AdventScale"],[33590,"AdventRevision"],[33628,"UIC1Tag"],[33629,"UIC2Tag"],[33630,"UIC3Tag"],[33631,"UIC4Tag"],[33918,"IntergraphPacketData"],[33919,"IntergraphFlagRegisters"],[33921,"INGRReserved"],[34016,"Site"],[34017,"ColorSequence"],[34018,"IT8Header"],[34019,"RasterPadding"],[34020,"BitsPerRunLength"],[34021,"BitsPerExtendedRunLength"],[34022,"ColorTable"],[34023,"ImageColorIndicator"],[34024,"BackgroundColorIndicator"],[34025,"ImageColorValue"],[34026,"BackgroundColorValue"],[34027,"PixelIntensityRange"],[34028,"TransparencyIndicator"],[34029,"ColorCharacterization"],[34030,"HCUsage"],[34031,"TrapIndicator"],[34032,"CMYKEquivalent"],[34152,"AFCP_IPTC"],[34232,"PixelMagicJBIGOptions"],[34263,"JPLCartoIFD"],[34306,"WB_GRGBLevels"],[34310,"LeafData"],[34687,"TIFF_FXExtensions"],[34688,"MultiProfiles"],[34689,"SharedData"],[34690,"T88Options"],[34732,"ImageLayer"],[34750,"JBIGOptions"],[34856,"Opto-ElectricConvFactor"],[34857,"Interlace"],[34908,"FaxRecvParams"],[34909,"FaxSubAddress"],[34910,"FaxRecvTime"],[34929,"FedexEDR"],[34954,"LeafSubIFD"],[37387,"FlashEnergy"],[37388,"SpatialFrequencyResponse"],[37389,"Noise"],[37390,"FocalPlaneXResolution"],[37391,"FocalPlaneYResolution"],[37392,"FocalPlaneResolutionUnit"],[37397,"ExposureIndex"],[37398,"TIFF-EPStandardID"],[37399,"SensingMethod"],[37434,"CIP3DataFile"],[37435,"CIP3Sheet"],[37436,"CIP3Side"],[37439,"StoNits"],[37679,"MSDocumentText"],[37680,"MSPropertySetStorage"],[37681,"MSDocumentTextPosition"],[37724,"ImageSourceData"],[40965,"InteropIFD"],[40976,"SamsungRawPointersOffset"],[40977,"SamsungRawPointersLength"],[41217,"SamsungRawByteOrder"],[41218,"SamsungRawUnknown"],[41484,"SpatialFrequencyResponse"],[41485,"Noise"],[41489,"ImageNumber"],[41490,"SecurityClassification"],[41491,"ImageHistory"],[41494,"TIFF-EPStandardID"],[41995,"DeviceSettingDescription"],[42112,"GDALMetadata"],[42113,"GDALNoData"],[44992,"ExpandSoftware"],[44993,"ExpandLens"],[44994,"ExpandFilm"],[44995,"ExpandFilterLens"],[44996,"ExpandScanner"],[44997,"ExpandFlashLamp"],[46275,"HasselbladRawImage"],[48129,"PixelFormat"],[48130,"Transformation"],[48131,"Uncompressed"],[48132,"ImageType"],[48256,"ImageWidth"],[48257,"ImageHeight"],[48258,"WidthResolution"],[48259,"HeightResolution"],[48320,"ImageOffset"],[48321,"ImageByteCount"],[48322,"AlphaOffset"],[48323,"AlphaByteCount"],[48324,"ImageDataDiscard"],[48325,"AlphaDataDiscard"],[50215,"OceScanjobDesc"],[50216,"OceApplicationSelector"],[50217,"OceIDNumber"],[50218,"OceImageLogic"],[50255,"Annotations"],[50459,"HasselbladExif"],[50547,"OriginalFileName"],[50560,"USPTOOriginalContentType"],[50656,"CR2CFAPattern"],[50710,"CFAPlaneColor"],[50711,"CFALayout"],[50712,"LinearizationTable"],[50713,"BlackLevelRepeatDim"],[50714,"BlackLevel"],[50715,"BlackLevelDeltaH"],[50716,"BlackLevelDeltaV"],[50717,"WhiteLevel"],[50718,"DefaultScale"],[50719,"DefaultCropOrigin"],[50720,"DefaultCropSize"],[50733,"BayerGreenSplit"],[50737,"ChromaBlurRadius"],[50738,"AntiAliasStrength"],[50752,"RawImageSegmentation"],[50780,"BestQualityScale"],[50784,"AliasLayerMetadata"],[50829,"ActiveArea"],[50830,"MaskedAreas"],[50935,"NoiseReductionApplied"],[50974,"SubTileBlockSize"],[50975,"RowInterleaveFactor"],[51008,"OpcodeList1"],[51009,"OpcodeList2"],[51022,"OpcodeList3"],[51041,"NoiseProfile"],[51114,"CacheVersion"],[51125,"DefaultUserCrop"],[51157,"NikonNEFInfo"],[65024,"KdcIFD"]];xf($e,"ifd0",Ym),xf($e,"exif",Ym),Me(es,"gps",[[23,{M:"Magnetic North",T:"True North"}],[25,{K:"Kilometers",M:"Miles",N:"Nautical Miles"}]]);class pf extends Pi{static canHandle(r,u){return r.getUint8(u+1)===224&&r.getUint32(u+4)===1246120262&&r.getUint8(u+8)===0}parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}pt(pf,"type","jfif"),pt(pf,"headerLength",9),Be.set("jfif",pf),Me($e,"jfif",[[0,"JFIFVersion"],[2,"ResolutionUnit"],[3,"XResolution"],[5,"YResolution"],[7,"ThumbnailWidth"],[8,"ThumbnailHeight"]]);class Xm extends Pi{parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)],...Array.from(this.raw)])}}pt(Xm,"type","ihdr"),Be.set("ihdr",Xm),Me($e,"ihdr",[[0,"ImageWidth"],[4,"ImageHeight"],[8,"BitDepth"],[9,"ColorType"],[10,"Compression"],[11,"Filter"],[12,"Interlace"]]),Me(es,"ihdr",[[9,{0:"Grayscale",2:"RGB",3:"Palette",4:"Grayscale with Alpha",6:"RGB with Alpha",DEFAULT:"Unknown"}],[10,{0:"Deflate/Inflate",DEFAULT:"Unknown"}],[11,{0:"Adaptive",DEFAULT:"Unknown"}],[12,{0:"Noninterlaced",1:"Adam7 Interlace",DEFAULT:"Unknown"}]]);class Kr extends Pi{static canHandle(r,u){return r.getUint8(u+1)===226&&r.getUint32(u+4)===1229144927}static findPosition(r,u){let h=super.findPosition(r,u);return h.chunkNumber=r.getUint8(u+16),h.chunkCount=r.getUint8(u+17),h.multiSegment=h.chunkCount>1,h}static handleMultiSegments(r){return function(u){let h=function(g){let y=g[0].constructor,x=0;for(let E of g)x+=E.length;let N=new y(x),A=0;for(let E of g)N.set(E,A),A+=E.length;return N}(u.map(g=>g.chunk.toUint8()));return new We(h)}(r)}parse(){return this.raw=new Map,this.parseHeader(),this.parseTags(),this.translate(),this.output}parseHeader(){let{raw:r}=this;this.chunk.byteLength<84&&he("ICC header is too short");for(let[u,h]of Object.entries(s_)){u=parseInt(u,10);let g=h(this.chunk,u);g!=="\0\0\0\0"&&r.set(u,g)}}parseTags(){let r,u,h,g,y,{raw:x}=this,N=this.chunk.getUint32(128),A=132,E=this.chunk.byteLength;for(;N--;){if(r=this.chunk.getString(A,4),u=this.chunk.getUint32(A+4),h=this.chunk.getUint32(A+8),g=this.chunk.getString(u,4),u+h>E)return void console.warn("reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.");y=this.parseTag(g,u,h),y!==void 0&&y!=="\0\0\0\0"&&x.set(r,y),A+=12}}parseTag(r,u,h){switch(r){case"desc":return this.parseDesc(u);case"mluc":return this.parseMluc(u);case"text":return this.parseText(u,h);case"sig ":return this.parseSig(u)}if(!(u+h>this.chunk.byteLength))return this.chunk.getUint8Array(u,h)}parseDesc(r){let u=this.chunk.getUint32(r+8)-1;return ts(this.chunk.getString(r+12,u))}parseText(r,u){return ts(this.chunk.getString(r+8,u-8))}parseSig(r){return ts(this.chunk.getString(r+8,4))}parseMluc(r){let{chunk:u}=this,h=u.getUint32(r+8),g=u.getUint32(r+12),y=r+16,x=[];for(let N=0;N<h;N++){let A=u.getString(y+0,2),E=u.getString(y+2,2),j=u.getUint32(y+4),X=u.getUint32(y+8)+r,Y=ts(u.getUnicodeString(X,j));x.push({lang:A,country:E,text:Y}),y+=g}return h===1?x[0].text:x}translateValue(r,u){return typeof r=="string"?u[r]||u[r.toLowerCase()]||r:u[r]||r}}pt(Kr,"type","icc"),pt(Kr,"multiSegment",!0),pt(Kr,"headerLength",18);const s_={4:An,8:function(p,r){return[p.getUint8(r),p.getUint8(r+1)>>4,p.getUint8(r+1)%16].map(u=>u.toString(10)).join(".")},12:An,16:An,20:An,24:function(p,r){const u=p.getUint16(r),h=p.getUint16(r+2)-1,g=p.getUint16(r+4),y=p.getUint16(r+6),x=p.getUint16(r+8),N=p.getUint16(r+10);return new Date(Date.UTC(u,h,g,y,x,N))},36:An,40:An,48:An,52:An,64:(p,r)=>p.getUint32(r),80:An};function An(p,r){return ts(p.getString(r,4))}Be.set("icc",Kr),Me($e,"icc",[[4,"ProfileCMMType"],[8,"ProfileVersion"],[12,"ProfileClass"],[16,"ColorSpaceData"],[20,"ProfileConnectionSpace"],[24,"ProfileDateTime"],[36,"ProfileFileSignature"],[40,"PrimaryPlatform"],[44,"CMMFlags"],[48,"DeviceManufacturer"],[52,"DeviceModel"],[56,"DeviceAttributes"],[64,"RenderingIntent"],[68,"ConnectionSpaceIlluminant"],[80,"ProfileCreator"],[84,"ProfileID"],["Header","ProfileHeader"],["MS00","WCSProfiles"],["bTRC","BlueTRC"],["bXYZ","BlueMatrixColumn"],["bfd","UCRBG"],["bkpt","MediaBlackPoint"],["calt","CalibrationDateTime"],["chad","ChromaticAdaptation"],["chrm","Chromaticity"],["ciis","ColorimetricIntentImageState"],["clot","ColorantTableOut"],["clro","ColorantOrder"],["clrt","ColorantTable"],["cprt","ProfileCopyright"],["crdi","CRDInfo"],["desc","ProfileDescription"],["devs","DeviceSettings"],["dmdd","DeviceModelDesc"],["dmnd","DeviceMfgDesc"],["dscm","ProfileDescriptionML"],["fpce","FocalPlaneColorimetryEstimates"],["gTRC","GreenTRC"],["gXYZ","GreenMatrixColumn"],["gamt","Gamut"],["kTRC","GrayTRC"],["lumi","Luminance"],["meas","Measurement"],["meta","Metadata"],["mmod","MakeAndModel"],["ncl2","NamedColor2"],["ncol","NamedColor"],["ndin","NativeDisplayInfo"],["pre0","Preview0"],["pre1","Preview1"],["pre2","Preview2"],["ps2i","PS2RenderingIntent"],["ps2s","PostScript2CSA"],["psd0","PostScript2CRD0"],["psd1","PostScript2CRD1"],["psd2","PostScript2CRD2"],["psd3","PostScript2CRD3"],["pseq","ProfileSequenceDesc"],["psid","ProfileSequenceIdentifier"],["psvm","PS2CRDVMSize"],["rTRC","RedTRC"],["rXYZ","RedMatrixColumn"],["resp","OutputResponse"],["rhoc","ReflectionHardcopyOrigColorimetry"],["rig0","PerceptualRenderingIntentGamut"],["rig2","SaturationRenderingIntentGamut"],["rpoc","ReflectionPrintOutputColorimetry"],["sape","SceneAppearanceEstimates"],["scoe","SceneColorimetryEstimates"],["scrd","ScreeningDesc"],["scrn","Screening"],["targ","CharTarget"],["tech","Technology"],["vcgt","VideoCardGamma"],["view","ViewingConditions"],["vued","ViewingCondDesc"],["wtpt","MediaWhitePoint"]]);const qr={"4d2p":"Erdt Systems",AAMA:"Aamazing Technologies",ACER:"Acer",ACLT:"Acolyte Color Research",ACTI:"Actix Sytems",ADAR:"Adara Technology",ADBE:"Adobe",ADI:"ADI Systems",AGFA:"Agfa Graphics",ALMD:"Alps Electric",ALPS:"Alps Electric",ALWN:"Alwan Color Expertise",AMTI:"Amiable Technologies",AOC:"AOC International",APAG:"Apago",APPL:"Apple Computer",AST:"AST","AT&T":"AT&T",BAEL:"BARBIERI electronic",BRCO:"Barco NV",BRKP:"Breakpoint",BROT:"Brother",BULL:"Bull",BUS:"Bus Computer Systems","C-IT":"C-Itoh",CAMR:"Intel",CANO:"Canon",CARR:"Carroll Touch",CASI:"Casio",CBUS:"Colorbus PL",CEL:"Crossfield",CELx:"Crossfield",CGS:"CGS Publishing Technologies International",CHM:"Rochester Robotics",CIGL:"Colour Imaging Group, London",CITI:"Citizen",CL00:"Candela",CLIQ:"Color IQ",CMCO:"Chromaco",CMiX:"CHROMiX",COLO:"Colorgraphic Communications",COMP:"Compaq",COMp:"Compeq/Focus Technology",CONR:"Conrac Display Products",CORD:"Cordata Technologies",CPQ:"Compaq",CPRO:"ColorPro",CRN:"Cornerstone",CTX:"CTX International",CVIS:"ColorVision",CWC:"Fujitsu Laboratories",DARI:"Darius Technology",DATA:"Dataproducts",DCP:"Dry Creek Photo",DCRC:"Digital Contents Resource Center, Chung-Ang University",DELL:"Dell Computer",DIC:"Dainippon Ink and Chemicals",DICO:"Diconix",DIGI:"Digital","DL&C":"Digital Light & Color",DPLG:"Doppelganger",DS:"Dainippon Screen",DSOL:"DOOSOL",DUPN:"DuPont",EPSO:"Epson",ESKO:"Esko-Graphics",ETRI:"Electronics and Telecommunications Research Institute",EVER:"Everex Systems",EXAC:"ExactCODE",Eizo:"Eizo",FALC:"Falco Data Products",FF:"Fuji Photo Film",FFEI:"FujiFilm Electronic Imaging",FNRD:"Fnord Software",FORA:"Fora",FORE:"Forefront Technology",FP:"Fujitsu",FPA:"WayTech Development",FUJI:"Fujitsu",FX:"Fuji Xerox",GCC:"GCC Technologies",GGSL:"Global Graphics Software",GMB:"Gretagmacbeth",GMG:"GMG",GOLD:"GoldStar Technology",GOOG:"Google",GPRT:"Giantprint",GTMB:"Gretagmacbeth",GVC:"WayTech Development",GW2K:"Sony",HCI:"HCI",HDM:"Heidelberger Druckmaschinen",HERM:"Hermes",HITA:"Hitachi America",HP:"Hewlett-Packard",HTC:"Hitachi",HiTi:"HiTi Digital",IBM:"IBM",IDNT:"Scitex",IEC:"Hewlett-Packard",IIYA:"Iiyama North America",IKEG:"Ikegami Electronics",IMAG:"Image Systems",IMI:"Ingram Micro",INTC:"Intel",INTL:"N/A (INTL)",INTR:"Intra Electronics",IOCO:"Iocomm International Technology",IPS:"InfoPrint Solutions Company",IRIS:"Scitex",ISL:"Ichikawa Soft Laboratory",ITNL:"N/A (ITNL)",IVM:"IVM",IWAT:"Iwatsu Electric",Idnt:"Scitex",Inca:"Inca Digital Printers",Iris:"Scitex",JPEG:"Joint Photographic Experts Group",JSFT:"Jetsoft Development",JVC:"JVC Information Products",KART:"Scitex",KFC:"KFC Computek Components",KLH:"KLH Computers",KMHD:"Konica Minolta",KNCA:"Konica",KODA:"Kodak",KYOC:"Kyocera",Kart:"Scitex",LCAG:"Leica",LCCD:"Leeds Colour",LDAK:"Left Dakota",LEAD:"Leading Technology",LEXM:"Lexmark International",LINK:"Link Computer",LINO:"Linotronic",LITE:"Lite-On",Leaf:"Leaf",Lino:"Linotronic",MAGC:"Mag Computronic",MAGI:"MAG Innovision",MANN:"Mannesmann",MICN:"Micron Technology",MICR:"Microtek",MICV:"Microvitec",MINO:"Minolta",MITS:"Mitsubishi Electronics America",MITs:"Mitsuba",MNLT:"Minolta",MODG:"Modgraph",MONI:"Monitronix",MONS:"Monaco Systems",MORS:"Morse Technology",MOTI:"Motive Systems",MSFT:"Microsoft",MUTO:"MUTOH INDUSTRIES",Mits:"Mitsubishi Electric",NANA:"NANAO",NEC:"NEC",NEXP:"NexPress Solutions",NISS:"Nissei Sangyo America",NKON:"Nikon",NONE:"none",OCE:"Oce Technologies",OCEC:"OceColor",OKI:"Oki",OKID:"Okidata",OKIP:"Okidata",OLIV:"Olivetti",OLYM:"Olympus",ONYX:"Onyx Graphics",OPTI:"Optiquest",PACK:"Packard Bell",PANA:"Matsushita Electric Industrial",PANT:"Pantone",PBN:"Packard Bell",PFU:"PFU",PHIL:"Philips Consumer Electronics",PNTX:"HOYA",POne:"Phase One A/S",PREM:"Premier Computer Innovations",PRIN:"Princeton Graphic Systems",PRIP:"Princeton Publishing Labs",QLUX:"Hong Kong",QMS:"QMS",QPCD:"QPcard AB",QUAD:"QuadLaser",QUME:"Qume",RADI:"Radius",RDDx:"Integrated Color Solutions",RDG:"Roland DG",REDM:"REDMS Group",RELI:"Relisys",RGMS:"Rolf Gierling Multitools",RICO:"Ricoh",RNLD:"Edmund Ronald",ROYA:"Royal",RPC:"Ricoh Printing Systems",RTL:"Royal Information Electronics",SAMP:"Sampo",SAMS:"Samsung",SANT:"Jaime Santana Pomares",SCIT:"Scitex",SCRN:"Dainippon Screen",SDP:"Scitex",SEC:"Samsung",SEIK:"Seiko Instruments",SEIk:"Seikosha",SGUY:"ScanGuy.com",SHAR:"Sharp Laboratories",SICC:"International Color Consortium",SONY:"Sony",SPCL:"SpectraCal",STAR:"Star",STC:"Sampo Technology",Scit:"Scitex",Sdp:"Scitex",Sony:"Sony",TALO:"Talon Technology",TAND:"Tandy",TATU:"Tatung",TAXA:"TAXAN America",TDS:"Tokyo Denshi Sekei",TECO:"TECO Information Systems",TEGR:"Tegra",TEKT:"Tektronix",TI:"Texas Instruments",TMKR:"TypeMaker",TOSB:"Toshiba",TOSH:"Toshiba",TOTK:"TOTOKU ELECTRIC",TRIU:"Triumph",TSBT:"Toshiba",TTX:"TTX Computer Products",TVM:"TVM Professional Monitor",TW:"TW Casper",ULSX:"Ulead Systems",UNIS:"Unisys",UTZF:"Utz Fehlau & Sohn",VARI:"Varityper",VIEW:"Viewsonic",VISL:"Visual communication",VIVO:"Vivo Mobile Communication",WANG:"Wang",WLBR:"Wilbur Imaging",WTG2:"Ware To Go",WYSE:"WYSE Technology",XERX:"Xerox",XRIT:"X-Rite",ZRAN:"Zoran",Zebr:"Zebra Technologies",appl:"Apple Computer",bICC:"basICColor",berg:"bergdesign",ceyd:"Integrated Color Solutions",clsp:"MacDermid ColorSpan",ds:"Dainippon Screen",dupn:"DuPont",ffei:"FujiFilm Electronic Imaging",flux:"FluxData",iris:"Scitex",kart:"Scitex",lcms:"Little CMS",lino:"Linotronic",none:"none",ob4d:"Erdt Systems",obic:"Medigraph",quby:"Qubyx Sarl",scit:"Scitex",scrn:"Dainippon Screen",sdp:"Scitex",siwi:"SIWI GRAFIKA",yxym:"YxyMaster"},Km={scnr:"Scanner",mntr:"Monitor",prtr:"Printer",link:"Device Link",abst:"Abstract",spac:"Color Space Conversion Profile",nmcl:"Named Color",cenc:"ColorEncodingSpace profile",mid:"MultiplexIdentification profile",mlnk:"MultiplexLink profile",mvis:"MultiplexVisualization profile",nkpf:"Nikon Input Device Profile (NON-STANDARD!)"};Me(es,"icc",[[4,qr],[12,Km],[40,Object.assign({},qr,Km)],[48,qr],[80,qr],[64,{0:"Perceptual",1:"Relative Colorimetric",2:"Saturation",3:"Absolute Colorimetric"}],["tech",{amd:"Active Matrix Display",crt:"Cathode Ray Tube Display",kpcd:"Photo CD",pmd:"Passive Matrix Display",dcam:"Digital Camera",dcpj:"Digital Cinema Projector",dmpc:"Digital Motion Picture Camera",dsub:"Dye Sublimation Printer",epho:"Electrophotographic Printer",esta:"Electrostatic Printer",flex:"Flexography",fprn:"Film Writer",fscn:"Film Scanner",grav:"Gravure",ijet:"Ink Jet Printer",imgs:"Photo Image Setter",mpfr:"Motion Picture Film Recorder",mpfs:"Motion Picture Film Scanner",offs:"Offset Lithography",pjtv:"Projection Television",rpho:"Photographic Paper Printer",rscn:"Reflective Scanner",silk:"Silkscreen",twax:"Thermal Wax Printer",vidc:"Video Camera",vidm:"Video Monitor"}]]);class Vr extends Pi{static canHandle(r,u,h){return r.getUint8(u+1)===237&&r.getString(u+4,9)==="Photoshop"&&this.containsIptc8bim(r,u,h)!==void 0}static headerLength(r,u,h){let g,y=this.containsIptc8bim(r,u,h);if(y!==void 0)return g=r.getUint8(u+y+7),g%2!=0&&(g+=1),g===0&&(g=4),y+8+g}static containsIptc8bim(r,u,h){for(let g=0;g<h;g++)if(this.isIptcSegmentHead(r,u+g))return g}static isIptcSegmentHead(r,u){return r.getUint8(u)===56&&r.getUint32(u)===943868237&&r.getUint16(u+4)===1028}parse(){let{raw:r}=this,u=this.chunk.byteLength-1,h=!1;for(let g=0;g<u;g++)if(this.chunk.getUint8(g)===28&&this.chunk.getUint8(g+1)===2){h=!0;let y=this.chunk.getUint16(g+3),x=this.chunk.getUint8(g+2),N=this.chunk.getLatin1String(g+5,y);r.set(x,this.pluralizeValue(r.get(x),N)),g+=4+y}else if(h)break;return this.translate(),this.output}pluralizeValue(r,u){return r!==void 0?r instanceof Array?(r.push(u),r):[r,u]:u}}pt(Vr,"type","iptc"),pt(Vr,"translateValues",!1),pt(Vr,"reviveValues",!1),Be.set("iptc",Vr),Me($e,"iptc",[[0,"ApplicationRecordVersion"],[3,"ObjectTypeReference"],[4,"ObjectAttributeReference"],[5,"ObjectName"],[7,"EditStatus"],[8,"EditorialUpdate"],[10,"Urgency"],[12,"SubjectReference"],[15,"Category"],[20,"SupplementalCategories"],[22,"FixtureIdentifier"],[25,"Keywords"],[26,"ContentLocationCode"],[27,"ContentLocationName"],[30,"ReleaseDate"],[35,"ReleaseTime"],[37,"ExpirationDate"],[38,"ExpirationTime"],[40,"SpecialInstructions"],[42,"ActionAdvised"],[45,"ReferenceService"],[47,"ReferenceDate"],[50,"ReferenceNumber"],[55,"DateCreated"],[60,"TimeCreated"],[62,"DigitalCreationDate"],[63,"DigitalCreationTime"],[65,"OriginatingProgram"],[70,"ProgramVersion"],[75,"ObjectCycle"],[80,"Byline"],[85,"BylineTitle"],[90,"City"],[92,"Sublocation"],[95,"State"],[100,"CountryCode"],[101,"Country"],[103,"OriginalTransmissionReference"],[105,"Headline"],[110,"Credit"],[115,"Source"],[116,"CopyrightNotice"],[118,"Contact"],[120,"Caption"],[121,"LocalCaption"],[122,"Writer"],[125,"RasterizedCaption"],[130,"ImageType"],[131,"ImageOrientation"],[135,"LanguageIdentifier"],[150,"AudioType"],[151,"AudioSamplingRate"],[152,"AudioSamplingResolution"],[153,"AudioDuration"],[154,"AudioOutcue"],[184,"JobID"],[185,"MasterDocumentID"],[186,"ShortDocumentID"],[187,"UniqueDocumentID"],[188,"OwnerID"],[200,"ObjectPreviewFileFormat"],[201,"ObjectPreviewFileVersion"],[202,"ObjectPreviewData"],[221,"Prefs"],[225,"ClassifyState"],[228,"SimilarityIndex"],[230,"DocumentNotes"],[231,"DocumentHistory"],[232,"ExifCameraInfo"],[255,"CatalogSets"]]),Me(es,"iptc",[[10,{0:"0 (reserved)",1:"1 (most urgent)",2:"2",3:"3",4:"4",5:"5 (normal urgency)",6:"6",7:"7",8:"8 (least urgent)",9:"9 (user-defined priority)"}],[75,{a:"Morning",b:"Both Morning and Evening",p:"Evening"}],[131,{L:"Landscape",P:"Portrait",S:"Square"}]]);const l_=({onPhotosUploaded:p,clearTrigger:r,photos:u=[]})=>{const[h,g]=it.useState([]),[y,x]=it.useState(!1),N=[...u,...h],[A,E]=it.useState(!1),[j,X]=it.useState(""),[Y,dt]=it.useState(""),[ht,st]=it.useState("");it.useEffect(()=>{r!==void 0&&(console.log("PhotoUploadSimple: Clearing photos due to clearTrigger:",r),g([]))},[r]);const xt=it.useCallback(async tt=>{x(!0);const q=Array.from(tt),F=[];for(const Mt of q)if(Mt.type.startsWith("image/"))try{const Nt=URL.createObjectURL(Mt),P=await qg(Mt);let Q,V,Ot=!1;P?.latitude&&P?.longitude&&(Q=P.latitude,V=P.longitude,Ot=!0);const S={id:`${Mt.name}-${Date.now()}-${Math.random()}`,file:Mt,url:Nt,name:Mt.name,latitude:Q,longitude:V,hasGPS:Ot,metadata:P,locationSource:Ot?"gps":"none"};F.push(S)}catch(Nt){console.error("Error processing file:",Mt.name,Nt)}const vt=[...h,...F];g(vt),p(vt),x(!1)},[h,p]),Kt=it.useCallback(tt=>{const q=tt.target.files;q&&q.length>0&&xt(q)},[xt]),Gt=it.useCallback(tt=>{tt.preventDefault();const q=tt.dataTransfer.files;q&&q.length>0&&xt(q)},[xt]),Tt=it.useCallback(tt=>{tt.preventDefault()},[]),le=(tt,q,F,vt)=>{if(h.some(Nt=>Nt.id===tt)){const Nt=h.map(P=>P.id===tt?{...P,latitude:q,longitude:F,hasGPS:!0,manualLocation:{latitude:q,longitude:F,placeName:vt,addedAt:new Date},locationSource:"manual"}:P);g(Nt),p(Nt)}else alert("To edit photos from a collection, please save the current state as a new collection first.")},St=tt=>{if(h.some(F=>F.id===tt)){const F=h.map(vt=>vt.id===tt?{...vt,latitude:void 0,longitude:void 0,hasGPS:!1,manualLocation:void 0,locationSource:"none"}:vt);g(F),p(F)}else alert("To edit photos from a collection, please save the current state as a new collection first.")},ne=tt=>{if(tt.latitude&&tt.longitude){const q=`https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${tt.latitude},${tt.longitude}`;window.open(q,"_blank")}},oe=tt=>{if(h.some(F=>F.id===tt)){const F=h.find(Mt=>Mt.id===tt);F&&URL.revokeObjectURL(F.url);const vt=h.filter(Mt=>Mt.id!==tt);g(vt),p(vt)}else alert("Photos from collections cannot be deleted here. Please manage them through the Collections interface.")},re=(tt,q)=>{X(tt),dt(q),st(""),E(!0)},de=()=>{if(!ht.trim()){alert("❌ Please enter a location");return}const tt=ht.trim();console.log("User entered:",tt);const q=tt.match(/^(-?\d+\.?\d*),\s*(-?\d+\.?\d*)$/);if(q){const F=parseFloat(q[1]),vt=parseFloat(q[2]);F>=-90&&F<=90&&vt>=-180&&vt<=180?(le(j,F,vt,`Coordinates: ${tt}`),E(!1),alert(`✅ Location set to: ${F}, ${vt}`)):alert("❌ Invalid coordinates. Latitude must be -90 to 90, longitude -180 to 180.")}else console.log("Searching for place:",tt),fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(tt)}&limit=1`).then(F=>F.json()).then(F=>{if(F.length>0){const vt=F[0],Mt=parseFloat(vt.lat),Nt=parseFloat(vt.lon);le(j,Mt,Nt,vt.display_name),E(!1),alert(`✅ Found: ${vt.display_name}`)}else alert("❌ Location not found. Try a different search term.")}).catch(F=>{console.error("Search error:",F),alert("❌ Search error. Check your internet connection.")})};return O.jsxs("div",{className:"photo-upload",children:[O.jsx("div",{className:"upload-area-professional",onDrop:Gt,onDragOver:Tt,onClick:()=>!y&&document.getElementById("file-input")?.click(),children:y?O.jsxs("div",{className:"processing-state",children:[O.jsx("div",{className:"processing-spinner",children:"⏳"}),O.jsx("h3",{children:"Processing Photos..."}),O.jsx("p",{children:"Extracting GPS metadata and generating thumbnails"})]}):O.jsxs("div",{className:"upload-content",children:[O.jsx("h3",{className:"upload-title",children:"Photo Mapper"}),O.jsx("p",{className:"upload-description",children:"Drag and drop photos here, or click to browse files"}),O.jsxs("div",{className:"upload-features",children:[O.jsx("div",{className:"feature",children:"✓ Automatic GPS extraction"}),O.jsx("div",{className:"feature",children:"✓ Multiple format support"}),O.jsx("div",{className:"feature",children:"✓ Batch processing"})]}),O.jsx("input",{type:"file",id:"file-input",multiple:!0,accept:"image/*",onChange:Kt,style:{display:"none"}}),O.jsx("button",{className:"upload-btn-professional",type:"button",children:"Choose Files"}),O.jsx("p",{className:"upload-formats",children:"Supports: JPG, PNG, HEIC, TIFF, and more"})]})}),N.length>0&&O.jsxs("div",{className:"photos-section-professional",children:[O.jsxs("div",{className:"photos-header-professional",children:[O.jsxs("div",{className:"header-left",children:[O.jsx("h3",{className:"photos-title",children:"Photo Collection"}),O.jsxs("div",{className:"photos-count",children:[N.length," ",N.length===1?"photo":"photos"]})]}),O.jsxs("div",{className:"header-stats",children:[O.jsxs("div",{className:"stat-chip gps",children:[N.filter(tt=>tt.hasGPS).length," with location"]}),O.jsxs("div",{className:"stat-chip no-gps",children:[N.filter(tt=>!tt.hasGPS).length," without location"]})]})]}),O.jsx("div",{className:"photos-grid-professional",children:N.map(tt=>O.jsxs("div",{className:"photo-card-professional",children:[O.jsxs("div",{className:"photo-card-header",children:[O.jsx("img",{src:tt.url,alt:tt.name,className:"photo-thumbnail-professional"}),O.jsxs("div",{className:"photo-details",children:[O.jsx("h4",{className:"photo-name-professional",children:tt.name}),O.jsx("div",{className:"location-status-professional",children:tt.hasGPS?O.jsxs("div",{className:"location-info-professional",children:[O.jsx("div",{className:"status-badge has-location",children:"Located"}),O.jsxs("div",{className:"coordinates-display",children:[tt.latitude?.toFixed(6),", ",tt.longitude?.toFixed(6)]}),tt.manualLocation?.placeName&&O.jsx("div",{className:"place-name-display",children:tt.manualLocation.placeName}),O.jsx("div",{className:"source-indicator",children:tt.locationSource==="gps"?"GPS Metadata":"Manual Entry"})]}):O.jsxs("div",{className:"no-location-info",children:[O.jsx("div",{className:"status-badge no-location",children:"No Location"}),O.jsx("div",{className:"help-text",children:"Add location to show on map"})]})})]})]}),O.jsxs("div",{className:"photo-actions-professional",children:[O.jsx("button",{className:`action-btn primary ${tt.hasGPS?"edit":"add"}`,onClick:q=>{q.preventDefault(),q.stopPropagation(),re(tt.id,tt.name)},children:tt.hasGPS?"Edit Location":"Add Location"}),tt.hasGPS&&O.jsxs(O.Fragment,{children:[O.jsx("button",{className:"action-btn secondary street-view",onClick:q=>{q.preventDefault(),q.stopPropagation(),ne(tt)},children:"Street View"}),O.jsx("button",{className:"action-btn secondary remove",onClick:q=>{q.preventDefault(),q.stopPropagation(),confirm(`Remove location from ${tt.name}?`)&&St(tt.id)},children:"Remove Location"})]}),O.jsx("button",{className:"action-btn danger delete",onClick:q=>{q.preventDefault(),q.stopPropagation(),confirm(`Delete photo "${tt.name}" completely? This cannot be undone.`)&&oe(tt.id)},children:"Delete Photo"})]})]},tt.id))})]}),A&&O.jsx("div",{className:"modal-overlay-professional",children:O.jsxs("div",{className:"modal-content-professional",children:[O.jsxs("div",{className:"modal-header-professional",children:[O.jsx("h3",{className:"modal-title",children:"Add Location"}),O.jsx("button",{className:"modal-close-btn",onClick:()=>E(!1),children:"✕"})]}),O.jsxs("div",{className:"modal-body-professional",children:[O.jsxs("div",{className:"photo-preview-modal",children:[O.jsx("div",{className:"preview-label",children:"Adding location to:"}),O.jsx("div",{className:"preview-name",children:Y})]}),O.jsxs("div",{className:"input-section",children:[O.jsx("label",{className:"input-label",children:"Location"}),O.jsx("input",{type:"text",value:ht,onChange:tt=>st(tt.target.value),placeholder:"e.g., Paris, France or 48.8566,2.3522",className:"location-input-professional",onKeyPress:tt=>{tt.key==="Enter"&&de()},autoFocus:!0}),O.jsx("div",{className:"input-help",children:'Try: "Eiffel Tower, Paris" or coordinates like "48.8566,2.3522"'})]})]}),O.jsxs("div",{className:"modal-footer-professional",children:[O.jsx("button",{className:"modal-btn secondary",onClick:()=>E(!1),children:"Cancel"}),O.jsx("button",{className:"modal-btn primary",onClick:de,disabled:!ht.trim(),children:"Add Location"})]})]})})]})};function up(p,r){const u=it.useRef(r);it.useEffect(function(){r!==u.current&&p.attributionControl!=null&&(u.current!=null&&p.attributionControl.removeAttribution(u.current),r!=null&&p.attributionControl.addAttribution(r)),u.current=r},[p,r])}var o_=Jm();const r_=1;function u_(p){return Object.freeze({__version:r_,map:p})}function zf(p,r){return Object.freeze({...p,...r})}const au=it.createContext(null);function oo(){const p=it.use(au);if(p==null)throw new Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return p}function cp(p){function r(u,h){const{instance:g,context:y}=p(u).current;it.useImperativeHandle(h,()=>g);const{children:x}=u;return x==null?null:Qr.createElement(au,{value:y},x)}return it.forwardRef(r)}function c_(p){function r(u,h){const[g,y]=it.useState(!1),{instance:x}=p(u,y).current;it.useImperativeHandle(h,()=>x),it.useEffect(function(){g&&x.update()},[x,g,u.children]);const N=x._contentNode;return N?o_.createPortal(u.children,N):null}return it.forwardRef(r)}function f_(p){function r(u,h){const{instance:g}=p(u).current;return it.useImperativeHandle(h,()=>g),null}return it.forwardRef(r)}function h_(p){return function(u){const h=oo(),g=p(u,h),{instance:y}=g.current,x=it.useRef(u.position),{position:N}=u;return it.useEffect(function(){return y.addTo(h.map),function(){y.remove()}},[h.map,y]),it.useEffect(function(){N!=null&&N!==x.current&&(y.setPosition(N),x.current=N)},[y,N]),g}}function fp(p,r){const u=it.useRef(void 0);it.useEffect(function(){return r!=null&&p.instance.on(r),u.current=r,function(){u.current!=null&&p.instance.off(u.current),u.current=null}},[p,r])}function Nf(p,r){const u=p.pane??r.pane;return u?{...p,pane:u}:p}function d_(p,r){return function(h,g){const y=oo(),x=p(Nf(h,y),y);return up(y.map,h.attribution),fp(x.current,h.eventHandlers),r(x.current,y,h,g),x}}var ao={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */var m_=ao.exports,Qm;function p_(){return Qm||(Qm=1,function(p,r){(function(u,h){h(r)})(m_,function(u){var h="1.9.4";function g(i){var a,l,f,d;for(l=1,f=arguments.length;l<f;l++){d=arguments[l];for(a in d)i[a]=d[a]}return i}var y=Object.create||function(){function i(){}return function(a){return i.prototype=a,new i}}();function x(i,a){var l=Array.prototype.slice;if(i.bind)return i.bind.apply(i,l.call(arguments,1));var f=l.call(arguments,2);return function(){return i.apply(a,f.length?f.concat(l.call(arguments)):arguments)}}var N=0;function A(i){return"_leaflet_id"in i||(i._leaflet_id=++N),i._leaflet_id}function E(i,a,l){var f,d,_,b;return b=function(){f=!1,d&&(_.apply(l,d),d=!1)},_=function(){f?d=arguments:(i.apply(l,arguments),setTimeout(b,a),f=!0)},_}function j(i,a,l){var f=a[1],d=a[0],_=f-d;return i===f&&l?i:((i-d)%_+_)%_+d}function X(){return!1}function Y(i,a){if(a===!1)return i;var l=Math.pow(10,a===void 0?6:a);return Math.round(i*l)/l}function dt(i){return i.trim?i.trim():i.replace(/^\s+|\s+$/g,"")}function ht(i){return dt(i).split(/\s+/)}function st(i,a){Object.prototype.hasOwnProperty.call(i,"options")||(i.options=i.options?y(i.options):{});for(var l in a)i.options[l]=a[l];return i.options}function xt(i,a,l){var f=[];for(var d in i)f.push(encodeURIComponent(l?d.toUpperCase():d)+"="+encodeURIComponent(i[d]));return(!a||a.indexOf("?")===-1?"?":"&")+f.join("&")}var Kt=/\{ *([\w_ -]+) *\}/g;function Gt(i,a){return i.replace(Kt,function(l,f){var d=a[f];if(d===void 0)throw new Error("No value provided for variable "+l);return typeof d=="function"&&(d=d(a)),d})}var Tt=Array.isArray||function(i){return Object.prototype.toString.call(i)==="[object Array]"};function le(i,a){for(var l=0;l<i.length;l++)if(i[l]===a)return l;return-1}var St="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function ne(i){return window["webkit"+i]||window["moz"+i]||window["ms"+i]}var oe=0;function re(i){var a=+new Date,l=Math.max(0,16-(a-oe));return oe=a+l,window.setTimeout(i,l)}var de=window.requestAnimationFrame||ne("RequestAnimationFrame")||re,tt=window.cancelAnimationFrame||ne("CancelAnimationFrame")||ne("CancelRequestAnimationFrame")||function(i){window.clearTimeout(i)};function q(i,a,l){if(l&&de===re)i.call(a);else return de.call(window,x(i,a))}function F(i){i&&tt.call(window,i)}var vt={__proto__:null,extend:g,create:y,bind:x,get lastId(){return N},stamp:A,throttle:E,wrapNum:j,falseFn:X,formatNum:Y,trim:dt,splitWords:ht,setOptions:st,getParamString:xt,template:Gt,isArray:Tt,indexOf:le,emptyImageUrl:St,requestFn:de,cancelFn:tt,requestAnimFrame:q,cancelAnimFrame:F};function Mt(){}Mt.extend=function(i){var a=function(){st(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},l=a.__super__=this.prototype,f=y(l);f.constructor=a,a.prototype=f;for(var d in this)Object.prototype.hasOwnProperty.call(this,d)&&d!=="prototype"&&d!=="__super__"&&(a[d]=this[d]);return i.statics&&g(a,i.statics),i.includes&&(Nt(i.includes),g.apply(null,[f].concat(i.includes))),g(f,i),delete f.statics,delete f.includes,f.options&&(f.options=l.options?y(l.options):{},g(f.options,i.options)),f._initHooks=[],f.callInitHooks=function(){if(!this._initHooksCalled){l.callInitHooks&&l.callInitHooks.call(this),this._initHooksCalled=!0;for(var _=0,b=f._initHooks.length;_<b;_++)f._initHooks[_].call(this)}},a},Mt.include=function(i){var a=this.prototype.options;return g(this.prototype,i),i.options&&(this.prototype.options=a,this.mergeOptions(i.options)),this},Mt.mergeOptions=function(i){return g(this.prototype.options,i),this},Mt.addInitHook=function(i){var a=Array.prototype.slice.call(arguments,1),l=typeof i=="function"?i:function(){this[i].apply(this,a)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(l),this};function Nt(i){if(!(typeof L>"u"||!L||!L.Mixin)){i=Tt(i)?i:[i];for(var a=0;a<i.length;a++)i[a]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var P={on:function(i,a,l){if(typeof i=="object")for(var f in i)this._on(f,i[f],a);else{i=ht(i);for(var d=0,_=i.length;d<_;d++)this._on(i[d],a,l)}return this},off:function(i,a,l){if(!arguments.length)delete this._events;else if(typeof i=="object")for(var f in i)this._off(f,i[f],a);else{i=ht(i);for(var d=arguments.length===1,_=0,b=i.length;_<b;_++)d?this._off(i[_]):this._off(i[_],a,l)}return this},_on:function(i,a,l,f){if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}if(this._listens(i,a,l)===!1){l===this&&(l=void 0);var d={fn:a,ctx:l};f&&(d.once=!0),this._events=this._events||{},this._events[i]=this._events[i]||[],this._events[i].push(d)}},_off:function(i,a,l){var f,d,_;if(this._events&&(f=this._events[i],!!f)){if(arguments.length===1){if(this._firingCount)for(d=0,_=f.length;d<_;d++)f[d].fn=X;delete this._events[i];return}if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}var b=this._listens(i,a,l);if(b!==!1){var M=f[b];this._firingCount&&(M.fn=X,this._events[i]=f=f.slice()),f.splice(b,1)}}},fire:function(i,a,l){if(!this.listens(i,l))return this;var f=g({},a,{type:i,target:this,sourceTarget:a&&a.sourceTarget||this});if(this._events){var d=this._events[i];if(d){this._firingCount=this._firingCount+1||1;for(var _=0,b=d.length;_<b;_++){var M=d[_],U=M.fn;M.once&&this.off(i,U,M.ctx),U.call(M.ctx||this,f)}this._firingCount--}}return l&&this._propagateEvent(f),this},listens:function(i,a,l,f){typeof i!="string"&&console.warn('"string" type argument expected');var d=a;typeof a!="function"&&(f=!!a,d=void 0,l=void 0);var _=this._events&&this._events[i];if(_&&_.length&&this._listens(i,d,l)!==!1)return!0;if(f){for(var b in this._eventParents)if(this._eventParents[b].listens(i,a,l,f))return!0}return!1},_listens:function(i,a,l){if(!this._events)return!1;var f=this._events[i]||[];if(!a)return!!f.length;l===this&&(l=void 0);for(var d=0,_=f.length;d<_;d++)if(f[d].fn===a&&f[d].ctx===l)return d;return!1},once:function(i,a,l){if(typeof i=="object")for(var f in i)this._on(f,i[f],a,!0);else{i=ht(i);for(var d=0,_=i.length;d<_;d++)this._on(i[d],a,l,!0)}return this},addEventParent:function(i){return this._eventParents=this._eventParents||{},this._eventParents[A(i)]=i,this},removeEventParent:function(i){return this._eventParents&&delete this._eventParents[A(i)],this},_propagateEvent:function(i){for(var a in this._eventParents)this._eventParents[a].fire(i.type,g({layer:i.target,propagatedFrom:i.target},i),!0)}};P.addEventListener=P.on,P.removeEventListener=P.clearAllEventListeners=P.off,P.addOneTimeEventListener=P.once,P.fireEvent=P.fire,P.hasEventListeners=P.listens;var Q=Mt.extend(P);function V(i,a,l){this.x=l?Math.round(i):i,this.y=l?Math.round(a):a}var Ot=Math.trunc||function(i){return i>0?Math.floor(i):Math.ceil(i)};V.prototype={clone:function(){return new V(this.x,this.y)},add:function(i){return this.clone()._add(S(i))},_add:function(i){return this.x+=i.x,this.y+=i.y,this},subtract:function(i){return this.clone()._subtract(S(i))},_subtract:function(i){return this.x-=i.x,this.y-=i.y,this},divideBy:function(i){return this.clone()._divideBy(i)},_divideBy:function(i){return this.x/=i,this.y/=i,this},multiplyBy:function(i){return this.clone()._multiplyBy(i)},_multiplyBy:function(i){return this.x*=i,this.y*=i,this},scaleBy:function(i){return new V(this.x*i.x,this.y*i.y)},unscaleBy:function(i){return new V(this.x/i.x,this.y/i.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=Ot(this.x),this.y=Ot(this.y),this},distanceTo:function(i){i=S(i);var a=i.x-this.x,l=i.y-this.y;return Math.sqrt(a*a+l*l)},equals:function(i){return i=S(i),i.x===this.x&&i.y===this.y},contains:function(i){return i=S(i),Math.abs(i.x)<=Math.abs(this.x)&&Math.abs(i.y)<=Math.abs(this.y)},toString:function(){return"Point("+Y(this.x)+", "+Y(this.y)+")"}};function S(i,a,l){return i instanceof V?i:Tt(i)?new V(i[0],i[1]):i==null?i:typeof i=="object"&&"x"in i&&"y"in i?new V(i.x,i.y):new V(i,a,l)}function k(i,a){if(i)for(var l=a?[i,a]:i,f=0,d=l.length;f<d;f++)this.extend(l[f])}k.prototype={extend:function(i){var a,l;if(!i)return this;if(i instanceof V||typeof i[0]=="number"||"x"in i)a=l=S(i);else if(i=J(i),a=i.min,l=i.max,!a||!l)return this;return!this.min&&!this.max?(this.min=a.clone(),this.max=l.clone()):(this.min.x=Math.min(a.x,this.min.x),this.max.x=Math.max(l.x,this.max.x),this.min.y=Math.min(a.y,this.min.y),this.max.y=Math.max(l.y,this.max.y)),this},getCenter:function(i){return S((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,i)},getBottomLeft:function(){return S(this.min.x,this.max.y)},getTopRight:function(){return S(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(i){var a,l;return typeof i[0]=="number"||i instanceof V?i=S(i):i=J(i),i instanceof k?(a=i.min,l=i.max):a=l=i,a.x>=this.min.x&&l.x<=this.max.x&&a.y>=this.min.y&&l.y<=this.max.y},intersects:function(i){i=J(i);var a=this.min,l=this.max,f=i.min,d=i.max,_=d.x>=a.x&&f.x<=l.x,b=d.y>=a.y&&f.y<=l.y;return _&&b},overlaps:function(i){i=J(i);var a=this.min,l=this.max,f=i.min,d=i.max,_=d.x>a.x&&f.x<l.x,b=d.y>a.y&&f.y<l.y;return _&&b},isValid:function(){return!!(this.min&&this.max)},pad:function(i){var a=this.min,l=this.max,f=Math.abs(a.x-l.x)*i,d=Math.abs(a.y-l.y)*i;return J(S(a.x-f,a.y-d),S(l.x+f,l.y+d))},equals:function(i){return i?(i=J(i),this.min.equals(i.getTopLeft())&&this.max.equals(i.getBottomRight())):!1}};function J(i,a){return!i||i instanceof k?i:new k(i,a)}function K(i,a){if(i)for(var l=a?[i,a]:i,f=0,d=l.length;f<d;f++)this.extend(l[f])}K.prototype={extend:function(i){var a=this._southWest,l=this._northEast,f,d;if(i instanceof lt)f=i,d=i;else if(i instanceof K){if(f=i._southWest,d=i._northEast,!f||!d)return this}else return i?this.extend(W(i)||et(i)):this;return!a&&!l?(this._southWest=new lt(f.lat,f.lng),this._northEast=new lt(d.lat,d.lng)):(a.lat=Math.min(f.lat,a.lat),a.lng=Math.min(f.lng,a.lng),l.lat=Math.max(d.lat,l.lat),l.lng=Math.max(d.lng,l.lng)),this},pad:function(i){var a=this._southWest,l=this._northEast,f=Math.abs(a.lat-l.lat)*i,d=Math.abs(a.lng-l.lng)*i;return new K(new lt(a.lat-f,a.lng-d),new lt(l.lat+f,l.lng+d))},getCenter:function(){return new lt((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new lt(this.getNorth(),this.getWest())},getSouthEast:function(){return new lt(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(i){typeof i[0]=="number"||i instanceof lt||"lat"in i?i=W(i):i=et(i);var a=this._southWest,l=this._northEast,f,d;return i instanceof K?(f=i.getSouthWest(),d=i.getNorthEast()):f=d=i,f.lat>=a.lat&&d.lat<=l.lat&&f.lng>=a.lng&&d.lng<=l.lng},intersects:function(i){i=et(i);var a=this._southWest,l=this._northEast,f=i.getSouthWest(),d=i.getNorthEast(),_=d.lat>=a.lat&&f.lat<=l.lat,b=d.lng>=a.lng&&f.lng<=l.lng;return _&&b},overlaps:function(i){i=et(i);var a=this._southWest,l=this._northEast,f=i.getSouthWest(),d=i.getNorthEast(),_=d.lat>a.lat&&f.lat<l.lat,b=d.lng>a.lng&&f.lng<l.lng;return _&&b},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(i,a){return i?(i=et(i),this._southWest.equals(i.getSouthWest(),a)&&this._northEast.equals(i.getNorthEast(),a)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function et(i,a){return i instanceof K?i:new K(i,a)}function lt(i,a,l){if(isNaN(i)||isNaN(a))throw new Error("Invalid LatLng object: ("+i+", "+a+")");this.lat=+i,this.lng=+a,l!==void 0&&(this.alt=+l)}lt.prototype={equals:function(i,a){if(!i)return!1;i=W(i);var l=Math.max(Math.abs(this.lat-i.lat),Math.abs(this.lng-i.lng));return l<=(a===void 0?1e-9:a)},toString:function(i){return"LatLng("+Y(this.lat,i)+", "+Y(this.lng,i)+")"},distanceTo:function(i){return Rt.distance(this,W(i))},wrap:function(){return Rt.wrapLatLng(this)},toBounds:function(i){var a=180*i/40075017,l=a/Math.cos(Math.PI/180*this.lat);return et([this.lat-a,this.lng-l],[this.lat+a,this.lng+l])},clone:function(){return new lt(this.lat,this.lng,this.alt)}};function W(i,a,l){return i instanceof lt?i:Tt(i)&&typeof i[0]!="object"?i.length===3?new lt(i[0],i[1],i[2]):i.length===2?new lt(i[0],i[1]):null:i==null?i:typeof i=="object"&&"lat"in i?new lt(i.lat,"lng"in i?i.lng:i.lon,i.alt):a===void 0?null:new lt(i,a,l)}var Jt={latLngToPoint:function(i,a){var l=this.projection.project(i),f=this.scale(a);return this.transformation._transform(l,f)},pointToLatLng:function(i,a){var l=this.scale(a),f=this.transformation.untransform(i,l);return this.projection.unproject(f)},project:function(i){return this.projection.project(i)},unproject:function(i){return this.projection.unproject(i)},scale:function(i){return 256*Math.pow(2,i)},zoom:function(i){return Math.log(i/256)/Math.LN2},getProjectedBounds:function(i){if(this.infinite)return null;var a=this.projection.bounds,l=this.scale(i),f=this.transformation.transform(a.min,l),d=this.transformation.transform(a.max,l);return new k(f,d)},infinite:!1,wrapLatLng:function(i){var a=this.wrapLng?j(i.lng,this.wrapLng,!0):i.lng,l=this.wrapLat?j(i.lat,this.wrapLat,!0):i.lat,f=i.alt;return new lt(l,a,f)},wrapLatLngBounds:function(i){var a=i.getCenter(),l=this.wrapLatLng(a),f=a.lat-l.lat,d=a.lng-l.lng;if(f===0&&d===0)return i;var _=i.getSouthWest(),b=i.getNorthEast(),M=new lt(_.lat-f,_.lng-d),U=new lt(b.lat-f,b.lng-d);return new K(M,U)}},Rt=g({},Jt,{wrapLng:[-180,180],R:6371e3,distance:function(i,a){var l=Math.PI/180,f=i.lat*l,d=a.lat*l,_=Math.sin((a.lat-i.lat)*l/2),b=Math.sin((a.lng-i.lng)*l/2),M=_*_+Math.cos(f)*Math.cos(d)*b*b,U=2*Math.atan2(Math.sqrt(M),Math.sqrt(1-M));return this.R*U}}),Ci=6378137,Ta={R:Ci,MAX_LATITUDE:85.0511287798,project:function(i){var a=Math.PI/180,l=this.MAX_LATITUDE,f=Math.max(Math.min(l,i.lat),-l),d=Math.sin(f*a);return new V(this.R*i.lng*a,this.R*Math.log((1+d)/(1-d))/2)},unproject:function(i){var a=180/Math.PI;return new lt((2*Math.atan(Math.exp(i.y/this.R))-Math.PI/2)*a,i.x*a/this.R)},bounds:function(){var i=Ci*Math.PI;return new k([-i,-i],[i,i])}()};function En(i,a,l,f){if(Tt(i)){this._a=i[0],this._b=i[1],this._c=i[2],this._d=i[3];return}this._a=i,this._b=a,this._c=l,this._d=f}En.prototype={transform:function(i,a){return this._transform(i.clone(),a)},_transform:function(i,a){return a=a||1,i.x=a*(this._a*i.x+this._b),i.y=a*(this._c*i.y+this._d),i},untransform:function(i,a){return a=a||1,new V((i.x/a-this._b)/this._a,(i.y/a-this._d)/this._c)}};function Fi(i,a,l,f){return new En(i,a,l,f)}var xa=g({},Rt,{code:"EPSG:3857",projection:Ta,transformation:function(){var i=.5/(Math.PI*Ta.R);return Fi(i,.5,-i,.5)}()}),Fs=g({},xa,{code:"EPSG:900913"});function ro(i){return document.createElementNS("http://www.w3.org/2000/svg",i)}function uo(i,a){var l="",f,d,_,b,M,U;for(f=0,_=i.length;f<_;f++){for(M=i[f],d=0,b=M.length;d<b;d++)U=M[d],l+=(d?"L":"M")+U.x+" "+U.y;l+=a?rt.svg?"z":"x":""}return l||"M0 0"}var Ve=document.documentElement.style,is="ActiveXObject"in window,co=is&&!document.addEventListener,Js="msLaunchUri"in navigator&&!("documentMode"in document),Mn=Ye("webkit"),fo=Ye("android"),Ws=Ye("android 2")||Ye("android 3"),ru=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),uu=fo&&Ye("Google")&&ru<537&&!("AudioNode"in window),Ji=!!window.opera,Oe=!Js&&Ye("chrome"),wi=Ye("gecko")&&!Mn&&!Ji&&!is,Ue=!Oe&&Ye("safari"),ho=Ye("phantom"),mo="OTransition"in Ve,cu=navigator.platform.indexOf("Win")===0,Ca=is&&"transition"in Ve,On="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!Ws,Bi="MozPerspective"in Ve,ns=!window.L_DISABLE_3D&&(Ca||On||Bi)&&!mo&&!ho,Li=typeof orientation<"u"||Ye("mobile"),fu=Li&&Mn,po=Li&&On,$s=!window.PointerEvent&&window.MSPointerEvent,as=!!(window.PointerEvent||$s),Dn="ontouchstart"in window||!!window.TouchEvent,hu=!window.L_NO_TOUCH&&(Dn||as),go=Li&&Ji,_o=Li&&wi,tl=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,el=function(){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){i=!0}});window.addEventListener("testPassiveEventSupport",X,a),window.removeEventListener("testPassiveEventSupport",X,a)}catch{}return i}(),vo=function(){return!!document.createElement("canvas").getContext}(),il=!!(document.createElementNS&&ro("svg").createSVGRect),Ui=!!il&&function(){var i=document.createElement("div");return i.innerHTML="<svg/>",(i.firstChild&&i.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),ge=!il&&function(){try{var i=document.createElement("div");i.innerHTML='<v:shape adj="1"/>';var a=i.firstChild;return a.style.behavior="url(#default#VML)",a&&typeof a.adj=="object"}catch{return!1}}(),De=navigator.platform.indexOf("Mac")===0,zn=navigator.platform.indexOf("Linux")===0;function Ye(i){return navigator.userAgent.toLowerCase().indexOf(i)>=0}var rt={ie:is,ielt9:co,edge:Js,webkit:Mn,android:fo,android23:Ws,androidStock:uu,opera:Ji,chrome:Oe,gecko:wi,safari:Ue,phantom:ho,opera12:mo,win:cu,ie3d:Ca,webkit3d:On,gecko3d:Bi,any3d:ns,mobile:Li,mobileWebkit:fu,mobileWebkit3d:po,msPointer:$s,pointer:as,touch:hu,touchNative:Dn,mobileOpera:go,mobileGecko:_o,retina:tl,passiveEvents:el,canvas:vo,svg:il,vml:ge,inlineSvg:Ui,mac:De,linux:zn},yo=rt.msPointer?"MSPointerDown":"pointerdown",nl=rt.msPointer?"MSPointerMove":"pointermove",Nn=rt.msPointer?"MSPointerUp":"pointerup",ss=rt.msPointer?"MSPointerCancel":"pointercancel",ki={touchstart:yo,touchmove:nl,touchend:Nn,touchcancel:ss},Wi={touchstart:To,touchmove:wa,touchend:wa,touchcancel:wa},di={},$i=!1;function ue(i,a,l){return a==="touchstart"&&du(),Wi[a]?(l=Wi[a].bind(this,l),i.addEventListener(ki[a],l,!1),l):(console.warn("wrong event specified:",a),X)}function So(i,a,l){if(!ki[a]){console.warn("wrong event specified:",a);return}i.removeEventListener(ki[a],l,!1)}function bo(i){di[i.pointerId]=i}function tn(i){di[i.pointerId]&&(di[i.pointerId]=i)}function en(i){delete di[i.pointerId]}function du(){$i||(document.addEventListener(yo,bo,!0),document.addEventListener(nl,tn,!0),document.addEventListener(Nn,en,!0),document.addEventListener(ss,en,!0),$i=!0)}function wa(i,a){if(a.pointerType!==(a.MSPOINTER_TYPE_MOUSE||"mouse")){a.touches=[];for(var l in di)a.touches.push(di[l]);a.changedTouches=[a],i(a)}}function To(i,a){a.MSPOINTER_TYPE_TOUCH&&a.pointerType===a.MSPOINTER_TYPE_TOUCH&&ee(a),wa(i,a)}function mu(i){var a={},l,f;for(f in i)l=i[f],a[f]=l&&l.bind?l.bind(i):l;return i=a,a.type="dblclick",a.detail=2,a.isTrusted=!1,a._simulated=!0,a}var ls=200;function os(i,a){i.addEventListener("dblclick",a);var l=0,f;function d(_){if(_.detail!==1){f=_.detail;return}if(!(_.pointerType==="mouse"||_.sourceCapabilities&&!_.sourceCapabilities.firesTouchEvents)){var b=ll(_);if(!(b.some(function(U){return U instanceof HTMLLabelElement&&U.attributes.for})&&!b.some(function(U){return U instanceof HTMLInputElement||U instanceof HTMLSelectElement}))){var M=Date.now();M-l<=ls?(f++,f===2&&a(mu(_))):f=1,l=M}}}return i.addEventListener("click",d),{dblclick:a,simDblclick:d}}function Ai(i,a){i.removeEventListener("dblclick",a.dblclick),i.removeEventListener("click",a.simDblclick)}var La=Un(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Rn=Un(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),nn=Rn==="webkitTransition"||Rn==="OTransition"?Rn+"End":"transitionend";function rs(i){return typeof i=="string"?document.getElementById(i):i}function an(i,a){var l=i.style[a]||i.currentStyle&&i.currentStyle[a];if((!l||l==="auto")&&document.defaultView){var f=document.defaultView.getComputedStyle(i,null);l=f?f[a]:null}return l==="auto"?null:l}function Dt(i,a,l){var f=document.createElement(i);return f.className=a||"",l&&l.appendChild(f),f}function qt(i){var a=i.parentNode;a&&a.removeChild(i)}function xe(i){for(;i.firstChild;)i.removeChild(i.firstChild)}function sn(i){var a=i.parentNode;a&&a.lastChild!==i&&a.appendChild(i)}function Pn(i){var a=i.parentNode;a&&a.firstChild!==i&&a.insertBefore(i,a.firstChild)}function Bn(i,a){if(i.classList!==void 0)return i.classList.contains(a);var l=Ce(i);return l.length>0&&new RegExp("(^|\\s)"+a+"(\\s|$)").test(l)}function yt(i,a){if(i.classList!==void 0)for(var l=ht(a),f=0,d=l.length;f<d;f++)i.classList.add(l[f]);else if(!Bn(i,a)){var _=Ce(i);al(i,(_?_+" ":"")+a)}}function Vt(i,a){i.classList!==void 0?i.classList.remove(a):al(i,dt((" "+Ce(i)+" ").replace(" "+a+" "," ")))}function al(i,a){i.className.baseVal===void 0?i.className=a:i.className.baseVal=a}function Ce(i){return i.correspondingElement&&(i=i.correspondingElement),i.className.baseVal===void 0?i.className:i.className.baseVal}function ze(i,a){"opacity"in i.style?i.style.opacity=a:"filter"in i.style&&xo(i,a)}function xo(i,a){var l=!1,f="DXImageTransform.Microsoft.Alpha";try{l=i.filters.item(f)}catch{if(a===1)return}a=Math.round(a*100),l?(l.Enabled=a!==100,l.Opacity=a):i.style.filter+=" progid:"+f+"(opacity="+a+")"}function Un(i){for(var a=document.documentElement.style,l=0;l<i.length;l++)if(i[l]in a)return i[l];return!1}function Xe(i,a,l){var f=a||new V(0,0);i.style[La]=(rt.ie3d?"translate("+f.x+"px,"+f.y+"px)":"translate3d("+f.x+"px,"+f.y+"px,0)")+(l?" scale("+l+")":"")}function Wt(i,a){i._leaflet_pos=a,rt.any3d?Xe(i,a):(i.style.left=a.x+"px",i.style.top=a.y+"px")}function Zi(i){return i._leaflet_pos||new V(0,0)}var mi,Aa,us;if("onselectstart"in document)mi=function(){_t(window,"selectstart",ee)},Aa=function(){Pt(window,"selectstart",ee)};else{var kn=Un(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);mi=function(){if(kn){var i=document.documentElement.style;us=i[kn],i[kn]="none"}},Aa=function(){kn&&(document.documentElement.style[kn]=us,us=void 0)}}function Ea(){_t(window,"dragstart",ee)}function sl(){Pt(window,"dragstart",ee)}var cs,Zn;function Ma(i){for(;i.tabIndex===-1;)i=i.parentNode;i.style&&(Hn(),cs=i,Zn=i.style.outlineStyle,i.style.outlineStyle="none",_t(window,"keydown",Hn))}function Hn(){cs&&(cs.style.outlineStyle=Zn,cs=void 0,Zn=void 0,Pt(window,"keydown",Hn))}function ln(i){do i=i.parentNode;while((!i.offsetWidth||!i.offsetHeight)&&i!==document.body);return i}function Hi(i){var a=i.getBoundingClientRect();return{x:a.width/i.offsetWidth||1,y:a.height/i.offsetHeight||1,boundingClientRect:a}}var Co={__proto__:null,TRANSFORM:La,TRANSITION:Rn,TRANSITION_END:nn,get:rs,getStyle:an,create:Dt,remove:qt,empty:xe,toFront:sn,toBack:Pn,hasClass:Bn,addClass:yt,removeClass:Vt,setClass:al,getClass:Ce,setOpacity:ze,testProp:Un,setTransform:Xe,setPosition:Wt,getPosition:Zi,get disableTextSelection(){return mi},get enableTextSelection(){return Aa},disableImageDrag:Ea,enableImageDrag:sl,preventOutline:Ma,restoreOutline:Hn,getSizedParentNode:ln,getScale:Hi};function _t(i,a,l,f){if(a&&typeof a=="object")for(var d in a)on(i,d,a[d],l);else{a=ht(a);for(var _=0,b=a.length;_<b;_++)on(i,a[_],l,f)}return this}var ti="_leaflet_events";function Pt(i,a,l,f){if(arguments.length===1)pi(i),delete i[ti];else if(a&&typeof a=="object")for(var d in a)gi(i,d,a[d],l);else if(a=ht(a),arguments.length===2)pi(i,function(M){return le(a,M)!==-1});else for(var _=0,b=a.length;_<b;_++)gi(i,a[_],l,f);return this}function pi(i,a){for(var l in i[ti]){var f=l.split(/\d/)[0];(!a||a(f))&&gi(i,f,null,null,l)}}var Oa={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function on(i,a,l,f){var d=a+A(l)+(f?"_"+A(f):"");if(i[ti]&&i[ti][d])return this;var _=function(M){return l.call(f||i,M||window.event)},b=_;!rt.touchNative&&rt.pointer&&a.indexOf("touch")===0?_=ue(i,a,_):rt.touch&&a==="dblclick"?_=os(i,_):"addEventListener"in i?a==="touchstart"||a==="touchmove"||a==="wheel"||a==="mousewheel"?i.addEventListener(Oa[a]||a,_,rt.passiveEvents?{passive:!1}:!1):a==="mouseenter"||a==="mouseleave"?(_=function(M){M=M||window.event,un(i,M)&&b(M)},i.addEventListener(Oa[a],_,!1)):i.addEventListener(a,b,!1):i.attachEvent("on"+a,_),i[ti]=i[ti]||{},i[ti][d]=_}function gi(i,a,l,f,d){d=d||a+A(l)+(f?"_"+A(f):"");var _=i[ti]&&i[ti][d];if(!_)return this;!rt.touchNative&&rt.pointer&&a.indexOf("touch")===0?So(i,a,_):rt.touch&&a==="dblclick"?Ai(i,_):"removeEventListener"in i?i.removeEventListener(Oa[a]||a,_,!1):i.detachEvent("on"+a,_),i[ti][d]=null}function Ei(i){return i.stopPropagation?i.stopPropagation():i.originalEvent?i.originalEvent._stopped=!0:i.cancelBubble=!0,this}function jn(i){return on(i,"wheel",Ei),this}function Gn(i){return _t(i,"mousedown touchstart dblclick contextmenu",Ei),i._leaflet_disable_click=!0,this}function ee(i){return i.preventDefault?i.preventDefault():i.returnValue=!1,this}function _i(i){return ee(i),Ei(i),this}function ll(i){if(i.composedPath)return i.composedPath();for(var a=[],l=i.target;l;)a.push(l),l=l.parentNode;return a}function we(i,a){if(!a)return new V(i.clientX,i.clientY);var l=Hi(a),f=l.boundingClientRect;return new V((i.clientX-f.left)/l.x-a.clientLeft,(i.clientY-f.top)/l.y-a.clientTop)}var rn=rt.linux&&rt.chrome?window.devicePixelRatio:rt.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Da(i){return rt.edge?i.wheelDeltaY/2:i.deltaY&&i.deltaMode===0?-i.deltaY/rn:i.deltaY&&i.deltaMode===1?-i.deltaY*20:i.deltaY&&i.deltaMode===2?-i.deltaY*60:i.deltaX||i.deltaZ?0:i.wheelDelta?(i.wheelDeltaY||i.wheelDelta)/2:i.detail&&Math.abs(i.detail)<32765?-i.detail*20:i.detail?i.detail/-32765*60:0}function un(i,a){var l=a.relatedTarget;if(!l)return!0;try{for(;l&&l!==i;)l=l.parentNode}catch{return!1}return l!==i}var pu={__proto__:null,on:_t,off:Pt,stopPropagation:Ei,disableScrollPropagation:jn,disableClickPropagation:Gn,preventDefault:ee,stop:_i,getPropagationPath:ll,getMousePosition:we,getWheelDelta:Da,isExternalTarget:un,addListener:_t,removeListener:Pt},fs=Q.extend({run:function(i,a,l,f){this.stop(),this._el=i,this._inProgress=!0,this._duration=l||.25,this._easeOutPower=1/Math.max(f||.5,.2),this._startPos=Zi(i),this._offset=a.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=q(this._animate,this),this._step()},_step:function(i){var a=+new Date-this._startTime,l=this._duration*1e3;a<l?this._runFrame(this._easeOut(a/l),i):(this._runFrame(1),this._complete())},_runFrame:function(i,a){var l=this._startPos.add(this._offset.multiplyBy(i));a&&l._round(),Wt(this._el,l),this.fire("step")},_complete:function(){F(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(i){return 1-Math.pow(1-i,this._easeOutPower)}}),wt=Q.extend({options:{crs:xa,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(i,a){a=st(this,a),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(i),this._initLayout(),this._onResize=x(this._onResize,this),this._initEvents(),a.maxBounds&&this.setMaxBounds(a.maxBounds),a.zoom!==void 0&&(this._zoom=this._limitZoom(a.zoom)),a.center&&a.zoom!==void 0&&this.setView(W(a.center),a.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Rn&&rt.any3d&&!rt.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),_t(this._proxy,nn,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(i,a,l){if(a=a===void 0?this._zoom:this._limitZoom(a),i=this._limitCenter(W(i),a,this.options.maxBounds),l=l||{},this._stop(),this._loaded&&!l.reset&&l!==!0){l.animate!==void 0&&(l.zoom=g({animate:l.animate},l.zoom),l.pan=g({animate:l.animate,duration:l.duration},l.pan));var f=this._zoom!==a?this._tryAnimatedZoom&&this._tryAnimatedZoom(i,a,l.zoom):this._tryAnimatedPan(i,l.pan);if(f)return clearTimeout(this._sizeTimer),this}return this._resetView(i,a,l.pan&&l.pan.noMoveStart),this},setZoom:function(i,a){return this._loaded?this.setView(this.getCenter(),i,{zoom:a}):(this._zoom=i,this)},zoomIn:function(i,a){return i=i||(rt.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+i,a)},zoomOut:function(i,a){return i=i||(rt.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-i,a)},setZoomAround:function(i,a,l){var f=this.getZoomScale(a),d=this.getSize().divideBy(2),_=i instanceof V?i:this.latLngToContainerPoint(i),b=_.subtract(d).multiplyBy(1-1/f),M=this.containerPointToLatLng(d.add(b));return this.setView(M,a,{zoom:l})},_getBoundsCenterZoom:function(i,a){a=a||{},i=i.getBounds?i.getBounds():et(i);var l=S(a.paddingTopLeft||a.padding||[0,0]),f=S(a.paddingBottomRight||a.padding||[0,0]),d=this.getBoundsZoom(i,!1,l.add(f));if(d=typeof a.maxZoom=="number"?Math.min(a.maxZoom,d):d,d===1/0)return{center:i.getCenter(),zoom:d};var _=f.subtract(l).divideBy(2),b=this.project(i.getSouthWest(),d),M=this.project(i.getNorthEast(),d),U=this.unproject(b.add(M).divideBy(2).add(_),d);return{center:U,zoom:d}},fitBounds:function(i,a){if(i=et(i),!i.isValid())throw new Error("Bounds are not valid.");var l=this._getBoundsCenterZoom(i,a);return this.setView(l.center,l.zoom,a)},fitWorld:function(i){return this.fitBounds([[-90,-180],[90,180]],i)},panTo:function(i,a){return this.setView(i,this._zoom,{pan:a})},panBy:function(i,a){if(i=S(i).round(),a=a||{},!i.x&&!i.y)return this.fire("moveend");if(a.animate!==!0&&!this.getSize().contains(i))return this._resetView(this.unproject(this.project(this.getCenter()).add(i)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new fs,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),a.noMoveStart||this.fire("movestart"),a.animate!==!1){yt(this._mapPane,"leaflet-pan-anim");var l=this._getMapPanePos().subtract(i).round();this._panAnim.run(this._mapPane,l,a.duration||.25,a.easeLinearity)}else this._rawPanBy(i),this.fire("move").fire("moveend");return this},flyTo:function(i,a,l){if(l=l||{},l.animate===!1||!rt.any3d)return this.setView(i,a,l);this._stop();var f=this.project(this.getCenter()),d=this.project(i),_=this.getSize(),b=this._zoom;i=W(i),a=a===void 0?b:a;var M=Math.max(_.x,_.y),U=M*this.getZoomScale(b,a),I=d.distanceTo(f)||1,$=1.42,nt=$*$;function ot(Qt){var Si=Qt?-1:1,Di=Qt?U:M,Vi=U*U-M*M+Si*nt*nt*I*I,zi=2*Di*nt*I,qa=Vi/zi,Cs=Math.sqrt(qa*qa+1)-qa,Va=Cs<1e-9?-18:Math.log(Cs);return Va}function gt(Qt){return(Math.exp(Qt)-Math.exp(-Qt))/2}function Yt(Qt){return(Math.exp(Qt)+Math.exp(-Qt))/2}function $t(Qt){return gt(Qt)/Yt(Qt)}var ve=ot(0);function Ke(Qt){return M*(Yt(ve)/Yt(ve+$*Qt))}function Io(Qt){return M*(Yt(ve)*$t(ve+$*Qt)-gt(ve))/nt}function qo(Qt){return 1-Math.pow(1-Qt,1.5)}var Ia=Date.now(),ta=(ot(1)-ve)/$,Vo=l.duration?1e3*l.duration:1e3*ta*.8;function ea(){var Qt=(Date.now()-Ia)/Vo,Si=qo(Qt)*ta;Qt<=1?(this._flyToFrame=q(ea,this),this._move(this.unproject(f.add(d.subtract(f).multiplyBy(Io(Si)/I)),b),this.getScaleZoom(M/Ke(Si),b),{flyTo:!0})):this._move(i,a)._moveEnd(!0)}return this._moveStart(!0,l.noMoveStart),ea.call(this),this},flyToBounds:function(i,a){var l=this._getBoundsCenterZoom(i,a);return this.flyTo(l.center,l.zoom,a)},setMaxBounds:function(i){return i=et(i),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),i.isValid()?(this.options.maxBounds=i,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(i){var a=this.options.minZoom;return this.options.minZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(i):this},setMaxZoom:function(i){var a=this.options.maxZoom;return this.options.maxZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(i):this},panInsideBounds:function(i,a){this._enforcingBounds=!0;var l=this.getCenter(),f=this._limitCenter(l,this._zoom,et(i));return l.equals(f)||this.panTo(f,a),this._enforcingBounds=!1,this},panInside:function(i,a){a=a||{};var l=S(a.paddingTopLeft||a.padding||[0,0]),f=S(a.paddingBottomRight||a.padding||[0,0]),d=this.project(this.getCenter()),_=this.project(i),b=this.getPixelBounds(),M=J([b.min.add(l),b.max.subtract(f)]),U=M.getSize();if(!M.contains(_)){this._enforcingBounds=!0;var I=_.subtract(M.getCenter()),$=M.extend(_).getSize().subtract(U);d.x+=I.x<0?-$.x:$.x,d.y+=I.y<0?-$.y:$.y,this.panTo(this.unproject(d),a),this._enforcingBounds=!1}return this},invalidateSize:function(i){if(!this._loaded)return this;i=g({animate:!1,pan:!0},i===!0?{animate:!0}:i);var a=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var l=this.getSize(),f=a.divideBy(2).round(),d=l.divideBy(2).round(),_=f.subtract(d);return!_.x&&!_.y?this:(i.animate&&i.pan?this.panBy(_):(i.pan&&this._rawPanBy(_),this.fire("move"),i.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(x(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:a,newSize:l}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(i){if(i=this._locateOptions=g({timeout:1e4,watch:!1},i),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var a=x(this._handleGeolocationResponse,this),l=x(this._handleGeolocationError,this);return i.watch?this._locationWatchId=navigator.geolocation.watchPosition(a,l,i):navigator.geolocation.getCurrentPosition(a,l,i),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(i){if(this._container._leaflet_id){var a=i.code,l=i.message||(a===1?"permission denied":a===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:a,message:"Geolocation error: "+l+"."})}},_handleGeolocationResponse:function(i){if(this._container._leaflet_id){var a=i.coords.latitude,l=i.coords.longitude,f=new lt(a,l),d=f.toBounds(i.coords.accuracy*2),_=this._locateOptions;if(_.setView){var b=this.getBoundsZoom(d);this.setView(f,_.maxZoom?Math.min(b,_.maxZoom):b)}var M={latlng:f,bounds:d,timestamp:i.timestamp};for(var U in i.coords)typeof i.coords[U]=="number"&&(M[U]=i.coords[U]);this.fire("locationfound",M)}},addHandler:function(i,a){if(!a)return this;var l=this[i]=new a(this);return this._handlers.push(l),this.options[i]&&l.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),qt(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(F(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var i;for(i in this._layers)this._layers[i].remove();for(i in this._panes)qt(this._panes[i]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(i,a){var l="leaflet-pane"+(i?" leaflet-"+i.replace("Pane","")+"-pane":""),f=Dt("div",l,a||this._mapPane);return i&&(this._panes[i]=f),f},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var i=this.getPixelBounds(),a=this.unproject(i.getBottomLeft()),l=this.unproject(i.getTopRight());return new K(a,l)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(i,a,l){i=et(i),l=S(l||[0,0]);var f=this.getZoom()||0,d=this.getMinZoom(),_=this.getMaxZoom(),b=i.getNorthWest(),M=i.getSouthEast(),U=this.getSize().subtract(l),I=J(this.project(M,f),this.project(b,f)).getSize(),$=rt.any3d?this.options.zoomSnap:1,nt=U.x/I.x,ot=U.y/I.y,gt=a?Math.max(nt,ot):Math.min(nt,ot);return f=this.getScaleZoom(gt,f),$&&(f=Math.round(f/($/100))*($/100),f=a?Math.ceil(f/$)*$:Math.floor(f/$)*$),Math.max(d,Math.min(_,f))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new V(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(i,a){var l=this._getTopLeftPoint(i,a);return new k(l,l.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(i){return this.options.crs.getProjectedBounds(i===void 0?this.getZoom():i)},getPane:function(i){return typeof i=="string"?this._panes[i]:i},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(i,a){var l=this.options.crs;return a=a===void 0?this._zoom:a,l.scale(i)/l.scale(a)},getScaleZoom:function(i,a){var l=this.options.crs;a=a===void 0?this._zoom:a;var f=l.zoom(i*l.scale(a));return isNaN(f)?1/0:f},project:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.latLngToPoint(W(i),a)},unproject:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.pointToLatLng(S(i),a)},layerPointToLatLng:function(i){var a=S(i).add(this.getPixelOrigin());return this.unproject(a)},latLngToLayerPoint:function(i){var a=this.project(W(i))._round();return a._subtract(this.getPixelOrigin())},wrapLatLng:function(i){return this.options.crs.wrapLatLng(W(i))},wrapLatLngBounds:function(i){return this.options.crs.wrapLatLngBounds(et(i))},distance:function(i,a){return this.options.crs.distance(W(i),W(a))},containerPointToLayerPoint:function(i){return S(i).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(i){return S(i).add(this._getMapPanePos())},containerPointToLatLng:function(i){var a=this.containerPointToLayerPoint(S(i));return this.layerPointToLatLng(a)},latLngToContainerPoint:function(i){return this.layerPointToContainerPoint(this.latLngToLayerPoint(W(i)))},mouseEventToContainerPoint:function(i){return we(i,this._container)},mouseEventToLayerPoint:function(i){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(i))},mouseEventToLatLng:function(i){return this.layerPointToLatLng(this.mouseEventToLayerPoint(i))},_initContainer:function(i){var a=this._container=rs(i);if(a){if(a._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");_t(a,"scroll",this._onScroll,this),this._containerId=A(a)},_initLayout:function(){var i=this._container;this._fadeAnimated=this.options.fadeAnimation&&rt.any3d,yt(i,"leaflet-container"+(rt.touch?" leaflet-touch":"")+(rt.retina?" leaflet-retina":"")+(rt.ielt9?" leaflet-oldie":"")+(rt.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var a=an(i,"position");a!=="absolute"&&a!=="relative"&&a!=="fixed"&&a!=="sticky"&&(i.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var i=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),Wt(this._mapPane,new V(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(yt(i.markerPane,"leaflet-zoom-hide"),yt(i.shadowPane,"leaflet-zoom-hide"))},_resetView:function(i,a,l){Wt(this._mapPane,new V(0,0));var f=!this._loaded;this._loaded=!0,a=this._limitZoom(a),this.fire("viewprereset");var d=this._zoom!==a;this._moveStart(d,l)._move(i,a)._moveEnd(d),this.fire("viewreset"),f&&this.fire("load")},_moveStart:function(i,a){return i&&this.fire("zoomstart"),a||this.fire("movestart"),this},_move:function(i,a,l,f){a===void 0&&(a=this._zoom);var d=this._zoom!==a;return this._zoom=a,this._lastCenter=i,this._pixelOrigin=this._getNewPixelOrigin(i),f?l&&l.pinch&&this.fire("zoom",l):((d||l&&l.pinch)&&this.fire("zoom",l),this.fire("move",l)),this},_moveEnd:function(i){return i&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return F(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(i){Wt(this._mapPane,this._getMapPanePos().subtract(i))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(i){this._targets={},this._targets[A(this._container)]=this;var a=i?Pt:_t;a(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&a(window,"resize",this._onResize,this),rt.any3d&&this.options.transform3DLimit&&(i?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){F(this._resizeRequest),this._resizeRequest=q(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var i=this._getMapPanePos();Math.max(Math.abs(i.x),Math.abs(i.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(i,a){for(var l=[],f,d=a==="mouseout"||a==="mouseover",_=i.target||i.srcElement,b=!1;_;){if(f=this._targets[A(_)],f&&(a==="click"||a==="preclick")&&this._draggableMoved(f)){b=!0;break}if(f&&f.listens(a,!0)&&(d&&!un(_,i)||(l.push(f),d))||_===this._container)break;_=_.parentNode}return!l.length&&!b&&!d&&this.listens(a,!0)&&(l=[this]),l},_isClickDisabled:function(i){for(;i&&i!==this._container;){if(i._leaflet_disable_click)return!0;i=i.parentNode}},_handleDOMEvent:function(i){var a=i.target||i.srcElement;if(!(!this._loaded||a._leaflet_disable_events||i.type==="click"&&this._isClickDisabled(a))){var l=i.type;l==="mousedown"&&Ma(a),this._fireDOMEvent(i,l)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(i,a,l){if(i.type==="click"){var f=g({},i);f.type="preclick",this._fireDOMEvent(f,f.type,l)}var d=this._findEventTargets(i,a);if(l){for(var _=[],b=0;b<l.length;b++)l[b].listens(a,!0)&&_.push(l[b]);d=_.concat(d)}if(d.length){a==="contextmenu"&&ee(i);var M=d[0],U={originalEvent:i};if(i.type!=="keypress"&&i.type!=="keydown"&&i.type!=="keyup"){var I=M.getLatLng&&(!M._radius||M._radius<=10);U.containerPoint=I?this.latLngToContainerPoint(M.getLatLng()):this.mouseEventToContainerPoint(i),U.layerPoint=this.containerPointToLayerPoint(U.containerPoint),U.latlng=I?M.getLatLng():this.layerPointToLatLng(U.layerPoint)}for(b=0;b<d.length;b++)if(d[b].fire(a,U,!0),U.originalEvent._stopped||d[b].options.bubblingMouseEvents===!1&&le(this._mouseEvents,a)!==-1)return}},_draggableMoved:function(i){return i=i.dragging&&i.dragging.enabled()?i:this,i.dragging&&i.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var i=0,a=this._handlers.length;i<a;i++)this._handlers[i].disable()},whenReady:function(i,a){return this._loaded?i.call(a||this,{target:this}):this.on("load",i,a),this},_getMapPanePos:function(){return Zi(this._mapPane)||new V(0,0)},_moved:function(){var i=this._getMapPanePos();return i&&!i.equals([0,0])},_getTopLeftPoint:function(i,a){var l=i&&a!==void 0?this._getNewPixelOrigin(i,a):this.getPixelOrigin();return l.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(i,a){var l=this.getSize()._divideBy(2);return this.project(i,a)._subtract(l)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(i,a,l){var f=this._getNewPixelOrigin(l,a);return this.project(i,a)._subtract(f)},_latLngBoundsToNewLayerBounds:function(i,a,l){var f=this._getNewPixelOrigin(l,a);return J([this.project(i.getSouthWest(),a)._subtract(f),this.project(i.getNorthWest(),a)._subtract(f),this.project(i.getSouthEast(),a)._subtract(f),this.project(i.getNorthEast(),a)._subtract(f)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(i){return this.latLngToLayerPoint(i).subtract(this._getCenterLayerPoint())},_limitCenter:function(i,a,l){if(!l)return i;var f=this.project(i,a),d=this.getSize().divideBy(2),_=new k(f.subtract(d),f.add(d)),b=this._getBoundsOffset(_,l,a);return Math.abs(b.x)<=1&&Math.abs(b.y)<=1?i:this.unproject(f.add(b),a)},_limitOffset:function(i,a){if(!a)return i;var l=this.getPixelBounds(),f=new k(l.min.add(i),l.max.add(i));return i.add(this._getBoundsOffset(f,a))},_getBoundsOffset:function(i,a,l){var f=J(this.project(a.getNorthEast(),l),this.project(a.getSouthWest(),l)),d=f.min.subtract(i.min),_=f.max.subtract(i.max),b=this._rebound(d.x,-_.x),M=this._rebound(d.y,-_.y);return new V(b,M)},_rebound:function(i,a){return i+a>0?Math.round(i-a)/2:Math.max(0,Math.ceil(i))-Math.max(0,Math.floor(a))},_limitZoom:function(i){var a=this.getMinZoom(),l=this.getMaxZoom(),f=rt.any3d?this.options.zoomSnap:1;return f&&(i=Math.round(i/f)*f),Math.max(a,Math.min(l,i))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){Vt(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(i,a){var l=this._getCenterOffset(i)._trunc();return(a&&a.animate)!==!0&&!this.getSize().contains(l)?!1:(this.panBy(l,a),!0)},_createAnimProxy:function(){var i=this._proxy=Dt("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(i),this.on("zoomanim",function(a){var l=La,f=this._proxy.style[l];Xe(this._proxy,this.project(a.center,a.zoom),this.getZoomScale(a.zoom,1)),f===this._proxy.style[l]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){qt(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var i=this.getCenter(),a=this.getZoom();Xe(this._proxy,this.project(i,a),this.getZoomScale(a,1))},_catchTransitionEnd:function(i){this._animatingZoom&&i.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(i,a,l){if(this._animatingZoom)return!0;if(l=l||{},!this._zoomAnimated||l.animate===!1||this._nothingToAnimate()||Math.abs(a-this._zoom)>this.options.zoomAnimationThreshold)return!1;var f=this.getZoomScale(a),d=this._getCenterOffset(i)._divideBy(1-1/f);return l.animate!==!0&&!this.getSize().contains(d)?!1:(q(function(){this._moveStart(!0,l.noMoveStart||!1)._animateZoom(i,a,!0)},this),!0)},_animateZoom:function(i,a,l,f){this._mapPane&&(l&&(this._animatingZoom=!0,this._animateToCenter=i,this._animateToZoom=a,yt(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:i,zoom:a,noUpdate:f}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(x(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&Vt(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function za(i,a){return new wt(i,a)}var Ne=Mt.extend({options:{position:"topright"},initialize:function(i){st(this,i)},getPosition:function(){return this.options.position},setPosition:function(i){var a=this._map;return a&&a.removeControl(this),this.options.position=i,a&&a.addControl(this),this},getContainer:function(){return this._container},addTo:function(i){this.remove(),this._map=i;var a=this._container=this.onAdd(i),l=this.getPosition(),f=i._controlCorners[l];return yt(a,"leaflet-control"),l.indexOf("bottom")!==-1?f.insertBefore(a,f.firstChild):f.appendChild(a),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(qt(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(i){this._map&&i&&i.screenX>0&&i.screenY>0&&this._map.getContainer().focus()}}),In=function(i){return new Ne(i)};wt.include({addControl:function(i){return i.addTo(this),this},removeControl:function(i){return i.remove(),this},_initControlPos:function(){var i=this._controlCorners={},a="leaflet-",l=this._controlContainer=Dt("div",a+"control-container",this._container);function f(d,_){var b=a+d+" "+a+_;i[d+_]=Dt("div",b,l)}f("top","left"),f("top","right"),f("bottom","left"),f("bottom","right")},_clearControlPos:function(){for(var i in this._controlCorners)qt(this._controlCorners[i]);qt(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var wo=Ne.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(i,a,l,f){return l<f?-1:f<l?1:0}},initialize:function(i,a,l){st(this,l),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var f in i)this._addLayer(i[f],f);for(f in a)this._addLayer(a[f],f,!0)},onAdd:function(i){this._initLayout(),this._update(),this._map=i,i.on("zoomend",this._checkDisabledLayers,this);for(var a=0;a<this._layers.length;a++)this._layers[a].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(i){return Ne.prototype.addTo.call(this,i),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var i=0;i<this._layers.length;i++)this._layers[i].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(i,a){return this._addLayer(i,a),this._map?this._update():this},addOverlay:function(i,a){return this._addLayer(i,a,!0),this._map?this._update():this},removeLayer:function(i){i.off("add remove",this._onLayerChange,this);var a=this._getLayer(A(i));return a&&this._layers.splice(this._layers.indexOf(a),1),this._map?this._update():this},expand:function(){yt(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var i=this._map.getSize().y-(this._container.offsetTop+50);return i<this._section.clientHeight?(yt(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=i+"px"):Vt(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return Vt(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var i="leaflet-control-layers",a=this._container=Dt("div",i),l=this.options.collapsed;a.setAttribute("aria-haspopup",!0),Gn(a),jn(a);var f=this._section=Dt("section",i+"-list");l&&(this._map.on("click",this.collapse,this),_t(a,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var d=this._layersLink=Dt("a",i+"-toggle",a);d.href="#",d.title="Layers",d.setAttribute("role","button"),_t(d,{keydown:function(_){_.keyCode===13&&this._expandSafely()},click:function(_){ee(_),this._expandSafely()}},this),l||this.expand(),this._baseLayersList=Dt("div",i+"-base",f),this._separator=Dt("div",i+"-separator",f),this._overlaysList=Dt("div",i+"-overlays",f),a.appendChild(f)},_getLayer:function(i){for(var a=0;a<this._layers.length;a++)if(this._layers[a]&&A(this._layers[a].layer)===i)return this._layers[a]},_addLayer:function(i,a,l){this._map&&i.on("add remove",this._onLayerChange,this),this._layers.push({layer:i,name:a,overlay:l}),this.options.sortLayers&&this._layers.sort(x(function(f,d){return this.options.sortFunction(f.layer,d.layer,f.name,d.name)},this)),this.options.autoZIndex&&i.setZIndex&&(this._lastZIndex++,i.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;xe(this._baseLayersList),xe(this._overlaysList),this._layerControlInputs=[];var i,a,l,f,d=0;for(l=0;l<this._layers.length;l++)f=this._layers[l],this._addItem(f),a=a||f.overlay,i=i||!f.overlay,d+=f.overlay?0:1;return this.options.hideSingleBase&&(i=i&&d>1,this._baseLayersList.style.display=i?"":"none"),this._separator.style.display=a&&i?"":"none",this},_onLayerChange:function(i){this._handlingClick||this._update();var a=this._getLayer(A(i.target)),l=a.overlay?i.type==="add"?"overlayadd":"overlayremove":i.type==="add"?"baselayerchange":null;l&&this._map.fire(l,a)},_createRadioElement:function(i,a){var l='<input type="radio" class="leaflet-control-layers-selector" name="'+i+'"'+(a?' checked="checked"':"")+"/>",f=document.createElement("div");return f.innerHTML=l,f.firstChild},_addItem:function(i){var a=document.createElement("label"),l=this._map.hasLayer(i.layer),f;i.overlay?(f=document.createElement("input"),f.type="checkbox",f.className="leaflet-control-layers-selector",f.defaultChecked=l):f=this._createRadioElement("leaflet-base-layers_"+A(this),l),this._layerControlInputs.push(f),f.layerId=A(i.layer),_t(f,"click",this._onInputClick,this);var d=document.createElement("span");d.innerHTML=" "+i.name;var _=document.createElement("span");a.appendChild(_),_.appendChild(f),_.appendChild(d);var b=i.overlay?this._overlaysList:this._baseLayersList;return b.appendChild(a),this._checkDisabledLayers(),a},_onInputClick:function(){if(!this._preventClick){var i=this._layerControlInputs,a,l,f=[],d=[];this._handlingClick=!0;for(var _=i.length-1;_>=0;_--)a=i[_],l=this._getLayer(a.layerId).layer,a.checked?f.push(l):a.checked||d.push(l);for(_=0;_<d.length;_++)this._map.hasLayer(d[_])&&this._map.removeLayer(d[_]);for(_=0;_<f.length;_++)this._map.hasLayer(f[_])||this._map.addLayer(f[_]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var i=this._layerControlInputs,a,l,f=this._map.getZoom(),d=i.length-1;d>=0;d--)a=i[d],l=this._getLayer(a.layerId).layer,a.disabled=l.options.minZoom!==void 0&&f<l.options.minZoom||l.options.maxZoom!==void 0&&f>l.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var i=this._section;this._preventClick=!0,_t(i,"click",ee),this.expand();var a=this;setTimeout(function(){Pt(i,"click",ee),a._preventClick=!1})}}),gu=function(i,a,l){return new wo(i,a,l)},ol=Ne.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(i){var a="leaflet-control-zoom",l=Dt("div",a+" leaflet-bar"),f=this.options;return this._zoomInButton=this._createButton(f.zoomInText,f.zoomInTitle,a+"-in",l,this._zoomIn),this._zoomOutButton=this._createButton(f.zoomOutText,f.zoomOutTitle,a+"-out",l,this._zoomOut),this._updateDisabled(),i.on("zoomend zoomlevelschange",this._updateDisabled,this),l},onRemove:function(i){i.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(i){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(i.shiftKey?3:1))},_zoomOut:function(i){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(i.shiftKey?3:1))},_createButton:function(i,a,l,f,d){var _=Dt("a",l,f);return _.innerHTML=i,_.href="#",_.title=a,_.setAttribute("role","button"),_.setAttribute("aria-label",a),Gn(_),_t(_,"click",_i),_t(_,"click",d,this),_t(_,"click",this._refocusOnMap,this),_},_updateDisabled:function(){var i=this._map,a="leaflet-disabled";Vt(this._zoomInButton,a),Vt(this._zoomOutButton,a),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||i._zoom===i.getMinZoom())&&(yt(this._zoomOutButton,a),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||i._zoom===i.getMaxZoom())&&(yt(this._zoomInButton,a),this._zoomInButton.setAttribute("aria-disabled","true"))}});wt.mergeOptions({zoomControl:!0}),wt.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new ol,this.addControl(this.zoomControl))});var rl=function(i){return new ol(i)},Lo=Ne.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(i){var a="leaflet-control-scale",l=Dt("div",a),f=this.options;return this._addScales(f,a+"-line",l),i.on(f.updateWhenIdle?"moveend":"move",this._update,this),i.whenReady(this._update,this),l},onRemove:function(i){i.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(i,a,l){i.metric&&(this._mScale=Dt("div",a,l)),i.imperial&&(this._iScale=Dt("div",a,l))},_update:function(){var i=this._map,a=i.getSize().y/2,l=i.distance(i.containerPointToLatLng([0,a]),i.containerPointToLatLng([this.options.maxWidth,a]));this._updateScales(l)},_updateScales:function(i){this.options.metric&&i&&this._updateMetric(i),this.options.imperial&&i&&this._updateImperial(i)},_updateMetric:function(i){var a=this._getRoundNum(i),l=a<1e3?a+" m":a/1e3+" km";this._updateScale(this._mScale,l,a/i)},_updateImperial:function(i){var a=i*3.2808399,l,f,d;a>5280?(l=a/5280,f=this._getRoundNum(l),this._updateScale(this._iScale,f+" mi",f/l)):(d=this._getRoundNum(a),this._updateScale(this._iScale,d+" ft",d/a))},_updateScale:function(i,a,l){i.style.width=Math.round(this.options.maxWidth*l)+"px",i.innerHTML=a},_getRoundNum:function(i){var a=Math.pow(10,(Math.floor(i)+"").length-1),l=i/a;return l=l>=10?10:l>=5?5:l>=3?3:l>=2?2:1,a*l}}),_u=function(i){return new Lo(i)},vu='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',ul=Ne.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(rt.inlineSvg?vu+" ":"")+"Leaflet</a>"},initialize:function(i){st(this,i),this._attributions={}},onAdd:function(i){i.attributionControl=this,this._container=Dt("div","leaflet-control-attribution"),Gn(this._container);for(var a in i._layers)i._layers[a].getAttribution&&this.addAttribution(i._layers[a].getAttribution());return this._update(),i.on("layeradd",this._addAttribution,this),this._container},onRemove:function(i){i.off("layeradd",this._addAttribution,this)},_addAttribution:function(i){i.layer.getAttribution&&(this.addAttribution(i.layer.getAttribution()),i.layer.once("remove",function(){this.removeAttribution(i.layer.getAttribution())},this))},setPrefix:function(i){return this.options.prefix=i,this._update(),this},addAttribution:function(i){return i?(this._attributions[i]||(this._attributions[i]=0),this._attributions[i]++,this._update(),this):this},removeAttribution:function(i){return i?(this._attributions[i]&&(this._attributions[i]--,this._update()),this):this},_update:function(){if(this._map){var i=[];for(var a in this._attributions)this._attributions[a]&&i.push(a);var l=[];this.options.prefix&&l.push(this.options.prefix),i.length&&l.push(i.join(", ")),this._container.innerHTML=l.join(' <span aria-hidden="true">|</span> ')}}});wt.mergeOptions({attributionControl:!0}),wt.addInitHook(function(){this.options.attributionControl&&new ul().addTo(this)});var yu=function(i){return new ul(i)};Ne.Layers=wo,Ne.Zoom=ol,Ne.Scale=Lo,Ne.Attribution=ul,In.layers=gu,In.zoom=rl,In.scale=_u,In.attribution=yu;var ei=Mt.extend({initialize:function(i){this._map=i},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});ei.addTo=function(i,a){return i.addHandler(a,this),this};var Su={Events:P},Ao=rt.touch?"touchstart mousedown":"mousedown",ji=Q.extend({options:{clickTolerance:3},initialize:function(i,a,l,f){st(this,f),this._element=i,this._dragStartTarget=a||i,this._preventOutline=l},enable:function(){this._enabled||(_t(this._dragStartTarget,Ao,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(ji._dragging===this&&this.finishDrag(!0),Pt(this._dragStartTarget,Ao,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(i){if(this._enabled&&(this._moved=!1,!Bn(this._element,"leaflet-zoom-anim"))){if(i.touches&&i.touches.length!==1){ji._dragging===this&&this.finishDrag();return}if(!(ji._dragging||i.shiftKey||i.which!==1&&i.button!==1&&!i.touches)&&(ji._dragging=this,this._preventOutline&&Ma(this._element),Ea(),mi(),!this._moving)){this.fire("down");var a=i.touches?i.touches[0]:i,l=ln(this._element);this._startPoint=new V(a.clientX,a.clientY),this._startPos=Zi(this._element),this._parentScale=Hi(l);var f=i.type==="mousedown";_t(document,f?"mousemove":"touchmove",this._onMove,this),_t(document,f?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(i){if(this._enabled){if(i.touches&&i.touches.length>1){this._moved=!0;return}var a=i.touches&&i.touches.length===1?i.touches[0]:i,l=new V(a.clientX,a.clientY)._subtract(this._startPoint);!l.x&&!l.y||Math.abs(l.x)+Math.abs(l.y)<this.options.clickTolerance||(l.x/=this._parentScale.x,l.y/=this._parentScale.y,ee(i),this._moved||(this.fire("dragstart"),this._moved=!0,yt(document.body,"leaflet-dragging"),this._lastTarget=i.target||i.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),yt(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(l),this._moving=!0,this._lastEvent=i,this._updatePosition())}},_updatePosition:function(){var i={originalEvent:this._lastEvent};this.fire("predrag",i),Wt(this._element,this._newPos),this.fire("drag",i)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(i){Vt(document.body,"leaflet-dragging"),this._lastTarget&&(Vt(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),Pt(document,"mousemove touchmove",this._onMove,this),Pt(document,"mouseup touchend touchcancel",this._onUp,this),sl(),Aa();var a=this._moved&&this._moving;this._moving=!1,ji._dragging=!1,a&&this.fire("dragend",{noInertia:i,distance:this._newPos.distanceTo(this._startPos)})}});function Eo(i,a,l){var f,d=[1,4,2,8],_,b,M,U,I,$,nt,ot;for(_=0,$=i.length;_<$;_++)i[_]._code=cn(i[_],a);for(M=0;M<4;M++){for(nt=d[M],f=[],_=0,$=i.length,b=$-1;_<$;b=_++)U=i[_],I=i[b],U._code&nt?I._code&nt||(ot=ds(I,U,nt,a,l),ot._code=cn(ot,a),f.push(ot)):(I._code&nt&&(ot=ds(I,U,nt,a,l),ot._code=cn(ot,a),f.push(ot)),f.push(U));i=f}return i}function hs(i,a){var l,f,d,_,b,M,U,I,$;if(!i||i.length===0)throw new Error("latlngs not passed");_e(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var nt=W([0,0]),ot=et(i),gt=ot.getNorthWest().distanceTo(ot.getSouthWest())*ot.getNorthEast().distanceTo(ot.getNorthWest());gt<1700&&(nt=cl(i));var Yt=i.length,$t=[];for(l=0;l<Yt;l++){var ve=W(i[l]);$t.push(a.project(W([ve.lat-nt.lat,ve.lng-nt.lng])))}for(M=U=I=0,l=0,f=Yt-1;l<Yt;f=l++)d=$t[l],_=$t[f],b=d.y*_.x-_.y*d.x,U+=(d.x+_.x)*b,I+=(d.y+_.y)*b,M+=b*3;M===0?$=$t[0]:$=[U/M,I/M];var Ke=a.unproject(S($));return W([Ke.lat+nt.lat,Ke.lng+nt.lng])}function cl(i){for(var a=0,l=0,f=0,d=0;d<i.length;d++){var _=W(i[d]);a+=_.lat,l+=_.lng,f++}return W([a/f,l/f])}var bu={__proto__:null,clipPolygon:Eo,polygonCenter:hs,centroid:cl};function Mo(i,a){if(!a||!i.length)return i.slice();var l=a*a;return i=Cu(i,l),i=xu(i,l),i}function fl(i,a,l){return Math.sqrt(fn(i,a,l,!0))}function Tu(i,a,l){return fn(i,a,l)}function xu(i,a){var l=i.length,f=typeof Uint8Array<"u"?Uint8Array:Array,d=new f(l);d[0]=d[l-1]=1,hl(i,d,a,0,l-1);var _,b=[];for(_=0;_<l;_++)d[_]&&b.push(i[_]);return b}function hl(i,a,l,f,d){var _=0,b,M,U;for(M=f+1;M<=d-1;M++)U=fn(i[M],i[f],i[d],!0),U>_&&(b=M,_=U);_>l&&(a[b]=1,hl(i,a,l,f,b),hl(i,a,l,b,d))}function Cu(i,a){for(var l=[i[0]],f=1,d=0,_=i.length;f<_;f++)wu(i[f],i[d])>a&&(l.push(i[f]),d=f);return d<_-1&&l.push(i[_-1]),l}var Oo;function Do(i,a,l,f,d){var _=f?Oo:cn(i,l),b=cn(a,l),M,U,I;for(Oo=b;;){if(!(_|b))return[i,a];if(_&b)return!1;M=_||b,U=ds(i,a,M,l,d),I=cn(U,l),M===_?(i=U,_=I):(a=U,b=I)}}function ds(i,a,l,f,d){var _=a.x-i.x,b=a.y-i.y,M=f.min,U=f.max,I,$;return l&8?(I=i.x+_*(U.y-i.y)/b,$=U.y):l&4?(I=i.x+_*(M.y-i.y)/b,$=M.y):l&2?(I=U.x,$=i.y+b*(U.x-i.x)/_):l&1&&(I=M.x,$=i.y+b*(M.x-i.x)/_),new V(I,$,d)}function cn(i,a){var l=0;return i.x<a.min.x?l|=1:i.x>a.max.x&&(l|=2),i.y<a.min.y?l|=4:i.y>a.max.y&&(l|=8),l}function wu(i,a){var l=a.x-i.x,f=a.y-i.y;return l*l+f*f}function fn(i,a,l,f){var d=a.x,_=a.y,b=l.x-d,M=l.y-_,U=b*b+M*M,I;return U>0&&(I=((i.x-d)*b+(i.y-_)*M)/U,I>1?(d=l.x,_=l.y):I>0&&(d+=b*I,_+=M*I)),b=i.x-d,M=i.y-_,f?b*b+M*M:new V(d,_)}function _e(i){return!Tt(i[0])||typeof i[0][0]!="object"&&typeof i[0][0]<"u"}function zo(i){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),_e(i)}function dl(i,a){var l,f,d,_,b,M,U,I;if(!i||i.length===0)throw new Error("latlngs not passed");_e(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var $=W([0,0]),nt=et(i),ot=nt.getNorthWest().distanceTo(nt.getSouthWest())*nt.getNorthEast().distanceTo(nt.getNorthWest());ot<1700&&($=cl(i));var gt=i.length,Yt=[];for(l=0;l<gt;l++){var $t=W(i[l]);Yt.push(a.project(W([$t.lat-$.lat,$t.lng-$.lng])))}for(l=0,f=0;l<gt-1;l++)f+=Yt[l].distanceTo(Yt[l+1])/2;if(f===0)I=Yt[0];else for(l=0,_=0;l<gt-1;l++)if(b=Yt[l],M=Yt[l+1],d=b.distanceTo(M),_+=d,_>f){U=(_-f)/d,I=[M.x-U*(M.x-b.x),M.y-U*(M.y-b.y)];break}var ve=a.unproject(S(I));return W([ve.lat+$.lat,ve.lng+$.lng])}var No={__proto__:null,simplify:Mo,pointToSegmentDistance:fl,closestPointOnSegment:Tu,clipSegment:Do,_getEdgeIntersection:ds,_getBitCode:cn,_sqClosestPointOnSegment:fn,isFlat:_e,_flat:zo,polylineCenter:dl},ms={project:function(i){return new V(i.lng,i.lat)},unproject:function(i){return new lt(i.y,i.x)},bounds:new k([-180,-90],[180,90])},ps={R:6378137,R_MINOR:6356752314245179e-9,bounds:new k([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(i){var a=Math.PI/180,l=this.R,f=i.lat*a,d=this.R_MINOR/l,_=Math.sqrt(1-d*d),b=_*Math.sin(f),M=Math.tan(Math.PI/4-f/2)/Math.pow((1-b)/(1+b),_/2);return f=-l*Math.log(Math.max(M,1e-10)),new V(i.lng*a*l,f)},unproject:function(i){for(var a=180/Math.PI,l=this.R,f=this.R_MINOR/l,d=Math.sqrt(1-f*f),_=Math.exp(-i.y/l),b=Math.PI/2-2*Math.atan(_),M=0,U=.1,I;M<15&&Math.abs(U)>1e-7;M++)I=d*Math.sin(b),I=Math.pow((1-I)/(1+I),d/2),U=Math.PI/2-2*Math.atan(_*I)-b,b+=U;return new lt(b*a,i.x*a/l)}},Ro={__proto__:null,LonLat:ms,Mercator:ps,SphericalMercator:Ta},qn=g({},Rt,{code:"EPSG:3395",projection:ps,transformation:function(){var i=.5/(Math.PI*ps.R);return Fi(i,.5,-i,.5)}()}),Po=g({},Rt,{code:"EPSG:4326",projection:ms,transformation:Fi(1/180,1,-1/180,.5)}),Lu=g({},Jt,{projection:ms,transformation:Fi(1,0,-1,0),scale:function(i){return Math.pow(2,i)},zoom:function(i){return Math.log(i)/Math.LN2},distance:function(i,a){var l=a.lng-i.lng,f=a.lat-i.lat;return Math.sqrt(l*l+f*f)},infinite:!0});Jt.Earth=Rt,Jt.EPSG3395=qn,Jt.EPSG3857=xa,Jt.EPSG900913=Fs,Jt.EPSG4326=Po,Jt.Simple=Lu;var ii=Q.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(i){return i.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(i){return i&&i.removeLayer(this),this},getPane:function(i){return this._map.getPane(i?this.options[i]||i:this.options.pane)},addInteractiveTarget:function(i){return this._map._targets[A(i)]=this,this},removeInteractiveTarget:function(i){return delete this._map._targets[A(i)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(i){var a=i.target;if(a.hasLayer(this)){if(this._map=a,this._zoomAnimated=a._zoomAnimated,this.getEvents){var l=this.getEvents();a.on(l,this),this.once("remove",function(){a.off(l,this)},this)}this.onAdd(a),this.fire("add"),a.fire("layeradd",{layer:this})}}});wt.include({addLayer:function(i){if(!i._layerAdd)throw new Error("The provided object is not a Layer.");var a=A(i);return this._layers[a]?this:(this._layers[a]=i,i._mapToAdd=this,i.beforeAdd&&i.beforeAdd(this),this.whenReady(i._layerAdd,i),this)},removeLayer:function(i){var a=A(i);return this._layers[a]?(this._loaded&&i.onRemove(this),delete this._layers[a],this._loaded&&(this.fire("layerremove",{layer:i}),i.fire("remove")),i._map=i._mapToAdd=null,this):this},hasLayer:function(i){return A(i)in this._layers},eachLayer:function(i,a){for(var l in this._layers)i.call(a,this._layers[l]);return this},_addLayers:function(i){i=i?Tt(i)?i:[i]:[];for(var a=0,l=i.length;a<l;a++)this.addLayer(i[a])},_addZoomLimit:function(i){(!isNaN(i.options.maxZoom)||!isNaN(i.options.minZoom))&&(this._zoomBoundLayers[A(i)]=i,this._updateZoomLevels())},_removeZoomLimit:function(i){var a=A(i);this._zoomBoundLayers[a]&&(delete this._zoomBoundLayers[a],this._updateZoomLevels())},_updateZoomLevels:function(){var i=1/0,a=-1/0,l=this._getZoomSpan();for(var f in this._zoomBoundLayers){var d=this._zoomBoundLayers[f].options;i=d.minZoom===void 0?i:Math.min(i,d.minZoom),a=d.maxZoom===void 0?a:Math.max(a,d.maxZoom)}this._layersMaxZoom=a===-1/0?void 0:a,this._layersMinZoom=i===1/0?void 0:i,l!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var hn=ii.extend({initialize:function(i,a){st(this,a),this._layers={};var l,f;if(i)for(l=0,f=i.length;l<f;l++)this.addLayer(i[l])},addLayer:function(i){var a=this.getLayerId(i);return this._layers[a]=i,this._map&&this._map.addLayer(i),this},removeLayer:function(i){var a=i in this._layers?i:this.getLayerId(i);return this._map&&this._layers[a]&&this._map.removeLayer(this._layers[a]),delete this._layers[a],this},hasLayer:function(i){var a=typeof i=="number"?i:this.getLayerId(i);return a in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(i){var a=Array.prototype.slice.call(arguments,1),l,f;for(l in this._layers)f=this._layers[l],f[i]&&f[i].apply(f,a);return this},onAdd:function(i){this.eachLayer(i.addLayer,i)},onRemove:function(i){this.eachLayer(i.removeLayer,i)},eachLayer:function(i,a){for(var l in this._layers)i.call(a,this._layers[l]);return this},getLayer:function(i){return this._layers[i]},getLayers:function(){var i=[];return this.eachLayer(i.push,i),i},setZIndex:function(i){return this.invoke("setZIndex",i)},getLayerId:function(i){return A(i)}}),Bo=function(i,a){return new hn(i,a)},ke=hn.extend({addLayer:function(i){return this.hasLayer(i)?this:(i.addEventParent(this),hn.prototype.addLayer.call(this,i),this.fire("layeradd",{layer:i}))},removeLayer:function(i){return this.hasLayer(i)?(i in this._layers&&(i=this._layers[i]),i.removeEventParent(this),hn.prototype.removeLayer.call(this,i),this.fire("layerremove",{layer:i})):this},setStyle:function(i){return this.invoke("setStyle",i)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var i=new K;for(var a in this._layers){var l=this._layers[a];i.extend(l.getBounds?l.getBounds():l.getLatLng())}return i}}),Na=function(i,a){return new ke(i,a)},Vn=Mt.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(i){st(this,i)},createIcon:function(i){return this._createIcon("icon",i)},createShadow:function(i){return this._createIcon("shadow",i)},_createIcon:function(i,a){var l=this._getIconUrl(i);if(!l){if(i==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var f=this._createImg(l,a&&a.tagName==="IMG"?a:null);return this._setIconStyles(f,i),(this.options.crossOrigin||this.options.crossOrigin==="")&&(f.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),f},_setIconStyles:function(i,a){var l=this.options,f=l[a+"Size"];typeof f=="number"&&(f=[f,f]);var d=S(f),_=S(a==="shadow"&&l.shadowAnchor||l.iconAnchor||d&&d.divideBy(2,!0));i.className="leaflet-marker-"+a+" "+(l.className||""),_&&(i.style.marginLeft=-_.x+"px",i.style.marginTop=-_.y+"px"),d&&(i.style.width=d.x+"px",i.style.height=d.y+"px")},_createImg:function(i,a){return a=a||document.createElement("img"),a.src=i,a},_getIconUrl:function(i){return rt.retina&&this.options[i+"RetinaUrl"]||this.options[i+"Url"]}});function gs(i){return new Vn(i)}var Yn=Vn.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(i){return typeof Yn.imagePath!="string"&&(Yn.imagePath=this._detectIconPath()),(this.options.imagePath||Yn.imagePath)+Vn.prototype._getIconUrl.call(this,i)},_stripUrl:function(i){var a=function(l,f,d){var _=f.exec(l);return _&&_[d]};return i=a(i,/^url\((['"])?(.+)\1\)$/,2),i&&a(i,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var i=Dt("div","leaflet-default-icon-path",document.body),a=an(i,"background-image")||an(i,"backgroundImage");if(document.body.removeChild(i),a=this._stripUrl(a),a)return a;var l=document.querySelector('link[href$="leaflet.css"]');return l?l.href.substring(0,l.href.length-11-1):""}}),ml=ei.extend({initialize:function(i){this._marker=i},addHooks:function(){var i=this._marker._icon;this._draggable||(this._draggable=new ji(i,i,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),yt(i,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&Vt(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(i){var a=this._marker,l=a._map,f=this._marker.options.autoPanSpeed,d=this._marker.options.autoPanPadding,_=Zi(a._icon),b=l.getPixelBounds(),M=l.getPixelOrigin(),U=J(b.min._subtract(M).add(d),b.max._subtract(M).subtract(d));if(!U.contains(_)){var I=S((Math.max(U.max.x,_.x)-U.max.x)/(b.max.x-U.max.x)-(Math.min(U.min.x,_.x)-U.min.x)/(b.min.x-U.min.x),(Math.max(U.max.y,_.y)-U.max.y)/(b.max.y-U.max.y)-(Math.min(U.min.y,_.y)-U.min.y)/(b.min.y-U.min.y)).multiplyBy(f);l.panBy(I,{animate:!1}),this._draggable._newPos._add(I),this._draggable._startPos._add(I),Wt(a._icon,this._draggable._newPos),this._onDrag(i),this._panRequest=q(this._adjustPan.bind(this,i))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(i){this._marker.options.autoPan&&(F(this._panRequest),this._panRequest=q(this._adjustPan.bind(this,i)))},_onDrag:function(i){var a=this._marker,l=a._shadow,f=Zi(a._icon),d=a._map.layerPointToLatLng(f);l&&Wt(l,f),a._latlng=d,i.latlng=d,i.oldLatLng=this._oldLatLng,a.fire("move",i).fire("drag",i)},_onDragEnd:function(i){F(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",i)}}),Xn=ii.extend({options:{icon:new Yn,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(i,a){st(this,a),this._latlng=W(i)},onAdd:function(i){this._zoomAnimated=this._zoomAnimated&&i.options.markerZoomAnimation,this._zoomAnimated&&i.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(i){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&i.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(i){var a=this._latlng;return this._latlng=W(i),this.update(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},setZIndexOffset:function(i){return this.options.zIndexOffset=i,this.update()},getIcon:function(){return this.options.icon},setIcon:function(i){return this.options.icon=i,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var i=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(i)}return this},_initIcon:function(){var i=this.options,a="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),l=i.icon.createIcon(this._icon),f=!1;l!==this._icon&&(this._icon&&this._removeIcon(),f=!0,i.title&&(l.title=i.title),l.tagName==="IMG"&&(l.alt=i.alt||"")),yt(l,a),i.keyboard&&(l.tabIndex="0",l.setAttribute("role","button")),this._icon=l,i.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&_t(l,"focus",this._panOnFocus,this);var d=i.icon.createShadow(this._shadow),_=!1;d!==this._shadow&&(this._removeShadow(),_=!0),d&&(yt(d,a),d.alt=""),this._shadow=d,i.opacity<1&&this._updateOpacity(),f&&this.getPane().appendChild(this._icon),this._initInteraction(),d&&_&&this.getPane(i.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&Pt(this._icon,"focus",this._panOnFocus,this),qt(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&qt(this._shadow),this._shadow=null},_setPos:function(i){this._icon&&Wt(this._icon,i),this._shadow&&Wt(this._shadow,i),this._zIndex=i.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(i){this._icon&&(this._icon.style.zIndex=this._zIndex+i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center).round();this._setPos(a)},_initInteraction:function(){if(this.options.interactive&&(yt(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),ml)){var i=this.options.draggable;this.dragging&&(i=this.dragging.enabled(),this.dragging.disable()),this.dragging=new ml(this),i&&this.dragging.enable()}},setOpacity:function(i){return this.options.opacity=i,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var i=this.options.opacity;this._icon&&ze(this._icon,i),this._shadow&&ze(this._shadow,i)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var i=this._map;if(i){var a=this.options.icon.options,l=a.iconSize?S(a.iconSize):S(0,0),f=a.iconAnchor?S(a.iconAnchor):S(0,0);i.panInside(this._latlng,{paddingTopLeft:f,paddingBottomRight:l.subtract(f)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function pl(i,a){return new Xn(i,a)}var Mi=ii.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(i){this._renderer=i.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(i){return st(this,i),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&i&&Object.prototype.hasOwnProperty.call(i,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),Ra=Mi.extend({options:{fill:!0,radius:10},initialize:function(i,a){st(this,a),this._latlng=W(i),this._radius=this.options.radius},setLatLng:function(i){var a=this._latlng;return this._latlng=W(i),this.redraw(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(i){return this.options.radius=this._radius=i,this.redraw()},getRadius:function(){return this._radius},setStyle:function(i){var a=i&&i.radius||this._radius;return Mi.prototype.setStyle.call(this,i),this.setRadius(a),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var i=this._radius,a=this._radiusY||i,l=this._clickTolerance(),f=[i+l,a+l];this._pxBounds=new k(this._point.subtract(f),this._point.add(f))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(i){return i.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function Uo(i,a){return new Ra(i,a)}var gl=Ra.extend({initialize:function(i,a,l){if(typeof a=="number"&&(a=g({},l,{radius:a})),st(this,a),this._latlng=W(i),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(i){return this._mRadius=i,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var i=[this._radius,this._radiusY||this._radius];return new K(this._map.layerPointToLatLng(this._point.subtract(i)),this._map.layerPointToLatLng(this._point.add(i)))},setStyle:Mi.prototype.setStyle,_project:function(){var i=this._latlng.lng,a=this._latlng.lat,l=this._map,f=l.options.crs;if(f.distance===Rt.distance){var d=Math.PI/180,_=this._mRadius/Rt.R/d,b=l.project([a+_,i]),M=l.project([a-_,i]),U=b.add(M).divideBy(2),I=l.unproject(U).lat,$=Math.acos((Math.cos(_*d)-Math.sin(a*d)*Math.sin(I*d))/(Math.cos(a*d)*Math.cos(I*d)))/d;(isNaN($)||$===0)&&($=_/Math.cos(Math.PI/180*a)),this._point=U.subtract(l.getPixelOrigin()),this._radius=isNaN($)?0:U.x-l.project([I,i-$]).x,this._radiusY=U.y-b.y}else{var nt=f.unproject(f.project(this._latlng).subtract([this._mRadius,0]));this._point=l.latLngToLayerPoint(this._latlng),this._radius=this._point.x-l.latLngToLayerPoint(nt).x}this._updateBounds()}});function Au(i,a,l){return new gl(i,a,l)}var Oi=Mi.extend({options:{smoothFactor:1,noClip:!1},initialize:function(i,a){st(this,a),this._setLatLngs(i)},getLatLngs:function(){return this._latlngs},setLatLngs:function(i){return this._setLatLngs(i),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(i){for(var a=1/0,l=null,f=fn,d,_,b=0,M=this._parts.length;b<M;b++)for(var U=this._parts[b],I=1,$=U.length;I<$;I++){d=U[I-1],_=U[I];var nt=f(i,d,_,!0);nt<a&&(a=nt,l=f(i,d,_))}return l&&(l.distance=Math.sqrt(a)),l},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return dl(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(i,a){return a=a||this._defaultShape(),i=W(i),a.push(i),this._bounds.extend(i),this.redraw()},_setLatLngs:function(i){this._bounds=new K,this._latlngs=this._convertLatLngs(i)},_defaultShape:function(){return _e(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(i){for(var a=[],l=_e(i),f=0,d=i.length;f<d;f++)l?(a[f]=W(i[f]),this._bounds.extend(a[f])):a[f]=this._convertLatLngs(i[f]);return a},_project:function(){var i=new k;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,i),this._bounds.isValid()&&i.isValid()&&(this._rawPxBounds=i,this._updateBounds())},_updateBounds:function(){var i=this._clickTolerance(),a=new V(i,i);this._rawPxBounds&&(this._pxBounds=new k([this._rawPxBounds.min.subtract(a),this._rawPxBounds.max.add(a)]))},_projectLatlngs:function(i,a,l){var f=i[0]instanceof lt,d=i.length,_,b;if(f){for(b=[],_=0;_<d;_++)b[_]=this._map.latLngToLayerPoint(i[_]),l.extend(b[_]);a.push(b)}else for(_=0;_<d;_++)this._projectLatlngs(i[_],a,l)},_clipPoints:function(){var i=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}var a=this._parts,l,f,d,_,b,M,U;for(l=0,d=0,_=this._rings.length;l<_;l++)for(U=this._rings[l],f=0,b=U.length;f<b-1;f++)M=Do(U[f],U[f+1],i,f,!0),M&&(a[d]=a[d]||[],a[d].push(M[0]),(M[1]!==U[f+1]||f===b-2)&&(a[d].push(M[1]),d++))}},_simplifyPoints:function(){for(var i=this._parts,a=this.options.smoothFactor,l=0,f=i.length;l<f;l++)i[l]=Mo(i[l],a)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(i,a){var l,f,d,_,b,M,U=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(l=0,_=this._parts.length;l<_;l++)for(M=this._parts[l],f=0,b=M.length,d=b-1;f<b;d=f++)if(!(!a&&f===0)&&fl(i,M[d],M[f])<=U)return!0;return!1}});function Eu(i,a){return new Oi(i,a)}Oi._flat=zo;var Kn=Oi.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return hs(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(i){var a=Oi.prototype._convertLatLngs.call(this,i),l=a.length;return l>=2&&a[0]instanceof lt&&a[0].equals(a[l-1])&&a.pop(),a},_setLatLngs:function(i){Oi.prototype._setLatLngs.call(this,i),_e(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return _e(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var i=this._renderer._bounds,a=this.options.weight,l=new V(a,a);if(i=new k(i.min.subtract(l),i.max.add(l)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}for(var f=0,d=this._rings.length,_;f<d;f++)_=Eo(this._rings[f],i,!0),_.length&&this._parts.push(_)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(i){var a=!1,l,f,d,_,b,M,U,I;if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(_=0,U=this._parts.length;_<U;_++)for(l=this._parts[_],b=0,I=l.length,M=I-1;b<I;M=b++)f=l[b],d=l[M],f.y>i.y!=d.y>i.y&&i.x<(d.x-f.x)*(i.y-f.y)/(d.y-f.y)+f.x&&(a=!a);return a||Oi.prototype._containsPoint.call(this,i,!0)}});function Ze(i,a){return new Kn(i,a)}var He=ke.extend({initialize:function(i,a){st(this,a),this._layers={},i&&this.addData(i)},addData:function(i){var a=Tt(i)?i:i.features,l,f,d;if(a){for(l=0,f=a.length;l<f;l++)d=a[l],(d.geometries||d.geometry||d.features||d.coordinates)&&this.addData(d);return this}var _=this.options;if(_.filter&&!_.filter(i))return this;var b=Pa(i,_);return b?(b.feature=Qn(i),b.defaultOptions=b.options,this.resetStyle(b),_.onEachFeature&&_.onEachFeature(i,b),this.addLayer(b)):this},resetStyle:function(i){return i===void 0?this.eachLayer(this.resetStyle,this):(i.options=g({},i.defaultOptions),this._setLayerStyle(i,this.options.style),this)},setStyle:function(i){return this.eachLayer(function(a){this._setLayerStyle(a,i)},this)},_setLayerStyle:function(i,a){i.setStyle&&(typeof a=="function"&&(a=a(i.feature)),i.setStyle(a))}});function Pa(i,a){var l=i.type==="Feature"?i.geometry:i,f=l?l.coordinates:null,d=[],_=a&&a.pointToLayer,b=a&&a.coordsToLatLng||_s,M,U,I,$;if(!f&&!l)return null;switch(l.type){case"Point":return M=b(f),_l(_,i,M,a);case"MultiPoint":for(I=0,$=f.length;I<$;I++)M=b(f[I]),d.push(_l(_,i,M,a));return new ke(d);case"LineString":case"MultiLineString":return U=Ba(f,l.type==="LineString"?0:1,b),new Oi(U,a);case"Polygon":case"MultiPolygon":return U=Ba(f,l.type==="Polygon"?1:2,b),new Kn(U,a);case"GeometryCollection":for(I=0,$=l.geometries.length;I<$;I++){var nt=Pa({geometry:l.geometries[I],type:"Feature",properties:i.properties},a);nt&&d.push(nt)}return new ke(d);case"FeatureCollection":for(I=0,$=l.features.length;I<$;I++){var ot=Pa(l.features[I],a);ot&&d.push(ot)}return new ke(d);default:throw new Error("Invalid GeoJSON object.")}}function _l(i,a,l,f){return i?i(a,l):new Xn(l,f&&f.markersInheritOptions&&f)}function _s(i){return new lt(i[1],i[0],i[2])}function Ba(i,a,l){for(var f=[],d=0,_=i.length,b;d<_;d++)b=a?Ba(i[d],a-1,l):(l||_s)(i[d]),f.push(b);return f}function Ua(i,a){return i=W(i),i.alt!==void 0?[Y(i.lng,a),Y(i.lat,a),Y(i.alt,a)]:[Y(i.lng,a),Y(i.lat,a)]}function vs(i,a,l,f){for(var d=[],_=0,b=i.length;_<b;_++)d.push(a?vs(i[_],_e(i[_])?0:a-1,l,f):Ua(i[_],f));return!a&&l&&d.length>0&&d.push(d[0].slice()),d}function ni(i,a){return i.feature?g({},i.feature,{geometry:a}):Qn(a)}function Qn(i){return i.type==="Feature"||i.type==="FeatureCollection"?i:{type:"Feature",properties:{},geometry:i}}var dn={toGeoJSON:function(i){return ni(this,{type:"Point",coordinates:Ua(this.getLatLng(),i)})}};Xn.include(dn),gl.include(dn),Ra.include(dn),Oi.include({toGeoJSON:function(i){var a=!_e(this._latlngs),l=vs(this._latlngs,a?1:0,!1,i);return ni(this,{type:(a?"Multi":"")+"LineString",coordinates:l})}}),Kn.include({toGeoJSON:function(i){var a=!_e(this._latlngs),l=a&&!_e(this._latlngs[0]),f=vs(this._latlngs,l?2:a?1:0,!0,i);return a||(f=[f]),ni(this,{type:(l?"Multi":"")+"Polygon",coordinates:f})}}),hn.include({toMultiPoint:function(i){var a=[];return this.eachLayer(function(l){a.push(l.toGeoJSON(i).geometry.coordinates)}),ni(this,{type:"MultiPoint",coordinates:a})},toGeoJSON:function(i){var a=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(a==="MultiPoint")return this.toMultiPoint(i);var l=a==="GeometryCollection",f=[];return this.eachLayer(function(d){if(d.toGeoJSON){var _=d.toGeoJSON(i);if(l)f.push(_.geometry);else{var b=Qn(_);b.type==="FeatureCollection"?f.push.apply(f,b.features):f.push(b)}}}),l?ni(this,{geometries:f,type:"GeometryCollection"}):{type:"FeatureCollection",features:f}}});function ys(i,a){return new He(i,a)}var ko=ys,vi=ii.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(i,a,l){this._url=i,this._bounds=et(a),st(this,l)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(yt(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){qt(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(i){return this.options.opacity=i,this._image&&this._updateOpacity(),this},setStyle:function(i){return i.opacity&&this.setOpacity(i.opacity),this},bringToFront:function(){return this._map&&sn(this._image),this},bringToBack:function(){return this._map&&Pn(this._image),this},setUrl:function(i){return this._url=i,this._image&&(this._image.src=i),this},setBounds:function(i){return this._bounds=et(i),this._map&&this._reset(),this},getEvents:function(){var i={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var i=this._url.tagName==="IMG",a=this._image=i?this._url:Dt("img");if(yt(a,"leaflet-image-layer"),this._zoomAnimated&&yt(a,"leaflet-zoom-animated"),this.options.className&&yt(a,this.options.className),a.onselectstart=X,a.onmousemove=X,a.onload=x(this.fire,this,"load"),a.onerror=x(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(a.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),i){this._url=a.src;return}a.src=this._url,a.alt=this.options.alt},_animateZoom:function(i){var a=this._map.getZoomScale(i.zoom),l=this._map._latLngBoundsToNewLayerBounds(this._bounds,i.zoom,i.center).min;Xe(this._image,l,a)},_reset:function(){var i=this._image,a=new k(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),l=a.getSize();Wt(i,a.min),i.style.width=l.x+"px",i.style.height=l.y+"px"},_updateOpacity:function(){ze(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var i=this.options.errorOverlayUrl;i&&this._url!==i&&(this._url=i,this._image.src=i)},getCenter:function(){return this._bounds.getCenter()}}),Fn=function(i,a,l){return new vi(i,a,l)},Ss=vi.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var i=this._url.tagName==="VIDEO",a=this._image=i?this._url:Dt("video");if(yt(a,"leaflet-image-layer"),this._zoomAnimated&&yt(a,"leaflet-zoom-animated"),this.options.className&&yt(a,this.options.className),a.onselectstart=X,a.onmousemove=X,a.onloadeddata=x(this.fire,this,"load"),i){for(var l=a.getElementsByTagName("source"),f=[],d=0;d<l.length;d++)f.push(l[d].src);this._url=l.length>0?f:[a.src];return}Tt(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(a.style,"objectFit")&&(a.style.objectFit="fill"),a.autoplay=!!this.options.autoplay,a.loop=!!this.options.loop,a.muted=!!this.options.muted,a.playsInline=!!this.options.playsInline;for(var _=0;_<this._url.length;_++){var b=Dt("source");b.src=this._url[_],a.appendChild(b)}}});function Zo(i,a,l){return new Ss(i,a,l)}var Gi=vi.extend({_initImage:function(){var i=this._image=this._url;yt(i,"leaflet-image-layer"),this._zoomAnimated&&yt(i,"leaflet-zoom-animated"),this.options.className&&yt(i,this.options.className),i.onselectstart=X,i.onmousemove=X}});function Ho(i,a,l){return new Gi(i,a,l)}var ai=ii.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(i,a){i&&(i instanceof lt||Tt(i))?(this._latlng=W(i),st(this,a)):(st(this,i),this._source=a),this.options.content&&(this._content=this.options.content)},openOn:function(i){return i=arguments.length?i:this._source._map,i.hasLayer(this)||i.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(i){return this._map?this.close():(arguments.length?this._source=i:i=this._source,this._prepareOpen(),this.openOn(i._map)),this},onAdd:function(i){this._zoomAnimated=i._zoomAnimated,this._container||this._initLayout(),i._fadeAnimated&&ze(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),i._fadeAnimated&&ze(this._container,1),this.bringToFront(),this.options.interactive&&(yt(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(i){i._fadeAnimated?(ze(this._container,0),this._removeTimeout=setTimeout(x(qt,void 0,this._container),200)):qt(this._container),this.options.interactive&&(Vt(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(i){return this._latlng=W(i),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(i){return this._content=i,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var i={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&sn(this._container),this},bringToBack:function(){return this._map&&Pn(this._container),this},_prepareOpen:function(i){var a=this._source;if(!a._map)return!1;if(a instanceof ke){a=null;var l=this._source._layers;for(var f in l)if(l[f]._map){a=l[f];break}if(!a)return!1;this._source=a}if(!i)if(a.getCenter)i=a.getCenter();else if(a.getLatLng)i=a.getLatLng();else if(a.getBounds)i=a.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(i),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var i=this._contentNode,a=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof a=="string")i.innerHTML=a;else{for(;i.hasChildNodes();)i.removeChild(i.firstChild);i.appendChild(a)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var i=this._map.latLngToLayerPoint(this._latlng),a=S(this.options.offset),l=this._getAnchor();this._zoomAnimated?Wt(this._container,i.add(l)):a=a.add(i).add(l);var f=this._containerBottom=-a.y,d=this._containerLeft=-Math.round(this._containerWidth/2)+a.x;this._container.style.bottom=f+"px",this._container.style.left=d+"px"}},_getAnchor:function(){return[0,0]}});wt.include({_initOverlay:function(i,a,l,f){var d=a;return d instanceof i||(d=new i(f).setContent(a)),l&&d.setLatLng(l),d}}),ii.include({_initOverlay:function(i,a,l,f){var d=l;return d instanceof i?(st(d,f),d._source=this):(d=a&&!f?a:new i(f,this),d.setContent(l)),d}});var ka=ai.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(i){return i=arguments.length?i:this._source._map,!i.hasLayer(this)&&i._popup&&i._popup.options.autoClose&&i.removeLayer(i._popup),i._popup=this,ai.prototype.openOn.call(this,i)},onAdd:function(i){ai.prototype.onAdd.call(this,i),i.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Mi||this._source.on("preclick",Ei))},onRemove:function(i){ai.prototype.onRemove.call(this,i),i.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Mi||this._source.off("preclick",Ei))},getEvents:function(){var i=ai.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(i.preclick=this.close),this.options.keepInView&&(i.moveend=this._adjustPan),i},_initLayout:function(){var i="leaflet-popup",a=this._container=Dt("div",i+" "+(this.options.className||"")+" leaflet-zoom-animated"),l=this._wrapper=Dt("div",i+"-content-wrapper",a);if(this._contentNode=Dt("div",i+"-content",l),Gn(a),jn(this._contentNode),_t(a,"contextmenu",Ei),this._tipContainer=Dt("div",i+"-tip-container",a),this._tip=Dt("div",i+"-tip",this._tipContainer),this.options.closeButton){var f=this._closeButton=Dt("a",i+"-close-button",a);f.setAttribute("role","button"),f.setAttribute("aria-label","Close popup"),f.href="#close",f.innerHTML='<span aria-hidden="true">&#215;</span>',_t(f,"click",function(d){ee(d),this.close()},this)}},_updateLayout:function(){var i=this._contentNode,a=i.style;a.width="",a.whiteSpace="nowrap";var l=i.offsetWidth;l=Math.min(l,this.options.maxWidth),l=Math.max(l,this.options.minWidth),a.width=l+1+"px",a.whiteSpace="",a.height="";var f=i.offsetHeight,d=this.options.maxHeight,_="leaflet-popup-scrolled";d&&f>d?(a.height=d+"px",yt(i,_)):Vt(i,_),this._containerWidth=this._container.offsetWidth},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center),l=this._getAnchor();Wt(this._container,a.add(l))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var i=this._map,a=parseInt(an(this._container,"marginBottom"),10)||0,l=this._container.offsetHeight+a,f=this._containerWidth,d=new V(this._containerLeft,-l-this._containerBottom);d._add(Zi(this._container));var _=i.layerPointToContainerPoint(d),b=S(this.options.autoPanPadding),M=S(this.options.autoPanPaddingTopLeft||b),U=S(this.options.autoPanPaddingBottomRight||b),I=i.getSize(),$=0,nt=0;_.x+f+U.x>I.x&&($=_.x+f-I.x+U.x),_.x-$-M.x<0&&($=_.x-M.x),_.y+l+U.y>I.y&&(nt=_.y+l-I.y+U.y),_.y-nt-M.y<0&&(nt=_.y-M.y),($||nt)&&(this.options.keepInView&&(this._autopanning=!0),i.fire("autopanstart").panBy([$,nt]))}},_getAnchor:function(){return S(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Mu=function(i,a){return new ka(i,a)};wt.mergeOptions({closePopupOnClick:!0}),wt.include({openPopup:function(i,a,l){return this._initOverlay(ka,i,a,l).openOn(this),this},closePopup:function(i){return i=arguments.length?i:this._popup,i&&i.close(),this}}),ii.include({bindPopup:function(i,a){return this._popup=this._initOverlay(ka,this._popup,i,a),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(i){return this._popup&&(this instanceof ke||(this._popup._source=this),this._popup._prepareOpen(i||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(i){return this._popup&&this._popup.setContent(i),this},getPopup:function(){return this._popup},_openPopup:function(i){if(!(!this._popup||!this._map)){_i(i);var a=i.layer||i.target;if(this._popup._source===a&&!(a instanceof Mi)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(i.latlng);return}this._popup._source=a,this.openPopup(i.latlng)}},_movePopup:function(i){this._popup.setLatLng(i.latlng)},_onKeyPress:function(i){i.originalEvent.keyCode===13&&this._openPopup(i)}});var bs=ai.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(i){ai.prototype.onAdd.call(this,i),this.setOpacity(this.options.opacity),i.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(i){ai.prototype.onRemove.call(this,i),i.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var i=ai.prototype.getEvents.call(this);return this.options.permanent||(i.preclick=this.close),i},_initLayout:function(){var i="leaflet-tooltip",a=i+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=Dt("div",a),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+A(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(i){var a,l,f=this._map,d=this._container,_=f.latLngToContainerPoint(f.getCenter()),b=f.layerPointToContainerPoint(i),M=this.options.direction,U=d.offsetWidth,I=d.offsetHeight,$=S(this.options.offset),nt=this._getAnchor();M==="top"?(a=U/2,l=I):M==="bottom"?(a=U/2,l=0):M==="center"?(a=U/2,l=I/2):M==="right"?(a=0,l=I/2):M==="left"?(a=U,l=I/2):b.x<_.x?(M="right",a=0,l=I/2):(M="left",a=U+($.x+nt.x)*2,l=I/2),i=i.subtract(S(a,l,!0)).add($).add(nt),Vt(d,"leaflet-tooltip-right"),Vt(d,"leaflet-tooltip-left"),Vt(d,"leaflet-tooltip-top"),Vt(d,"leaflet-tooltip-bottom"),yt(d,"leaflet-tooltip-"+M),Wt(d,i)},_updatePosition:function(){var i=this._map.latLngToLayerPoint(this._latlng);this._setPosition(i)},setOpacity:function(i){this.options.opacity=i,this._container&&ze(this._container,i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center);this._setPosition(a)},_getAnchor:function(){return S(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),Ou=function(i,a){return new bs(i,a)};wt.include({openTooltip:function(i,a,l){return this._initOverlay(bs,i,a,l).openOn(this),this},closeTooltip:function(i){return i.close(),this}}),ii.include({bindTooltip:function(i,a){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(bs,this._tooltip,i,a),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(i){if(!(!i&&this._tooltipHandlersAdded)){var a=i?"off":"on",l={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?l.add=this._openTooltip:(l.mouseover=this._openTooltip,l.mouseout=this.closeTooltip,l.click=this._openTooltip,this._map?this._addFocusListeners():l.add=this._addFocusListeners),this._tooltip.options.sticky&&(l.mousemove=this._moveTooltip),this[a](l),this._tooltipHandlersAdded=!i}},openTooltip:function(i){return this._tooltip&&(this instanceof ke||(this._tooltip._source=this),this._tooltip._prepareOpen(i)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(i){return this._tooltip&&this._tooltip.setContent(i),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&(_t(a,"focus",function(){this._tooltip._source=i,this.openTooltip()},this),_t(a,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&a.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(i){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var a=this;this._map.once("moveend",function(){a._openOnceFlag=!1,a._openTooltip(i)});return}this._tooltip._source=i.layer||i.target,this.openTooltip(this._tooltip.options.sticky?i.latlng:void 0)}},_moveTooltip:function(i){var a=i.latlng,l,f;this._tooltip.options.sticky&&i.originalEvent&&(l=this._map.mouseEventToContainerPoint(i.originalEvent),f=this._map.containerPointToLayerPoint(l),a=this._map.layerPointToLatLng(f)),this._tooltip.setLatLng(a)}});var vl=Vn.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(i){var a=i&&i.tagName==="DIV"?i:document.createElement("div"),l=this.options;if(l.html instanceof Element?(xe(a),a.appendChild(l.html)):a.innerHTML=l.html!==!1?l.html:"",l.bgPos){var f=S(l.bgPos);a.style.backgroundPosition=-f.x+"px "+-f.y+"px"}return this._setIconStyles(a,"icon"),a},createShadow:function(){return null}});function jo(i){return new vl(i)}Vn.Default=Yn;var mn=ii.extend({options:{tileSize:256,opacity:1,updateWhenIdle:rt.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(i){st(this,i)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(i){i._addZoomLimit(this)},onRemove:function(i){this._removeAllTiles(),qt(this._container),i._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(sn(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Pn(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(i){return this.options.opacity=i,this._updateOpacity(),this},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var i=this._clampZoom(this._map.getZoom());i!==this._tileZoom&&(this._tileZoom=i,this._updateLevels()),this._update()}return this},getEvents:function(){var i={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=E(this._onMoveEnd,this.options.updateInterval,this)),i.move=this._onMove),this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},createTile:function(){return document.createElement("div")},getTileSize:function(){var i=this.options.tileSize;return i instanceof V?i:new V(i,i)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(i){for(var a=this.getPane().children,l=-i(-1/0,1/0),f=0,d=a.length,_;f<d;f++)_=a[f].style.zIndex,a[f]!==this._container&&_&&(l=i(l,+_));isFinite(l)&&(this.options.zIndex=l+i(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!rt.ielt9){ze(this._container,this.options.opacity);var i=+new Date,a=!1,l=!1;for(var f in this._tiles){var d=this._tiles[f];if(!(!d.current||!d.loaded)){var _=Math.min(1,(i-d.loaded)/200);ze(d.el,_),_<1?a=!0:(d.active?l=!0:this._onOpaqueTile(d),d.active=!0)}}l&&!this._noPrune&&this._pruneTiles(),a&&(F(this._fadeFrame),this._fadeFrame=q(this._updateOpacity,this))}},_onOpaqueTile:X,_initContainer:function(){this._container||(this._container=Dt("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var i=this._tileZoom,a=this.options.maxZoom;if(i!==void 0){for(var l in this._levels)l=Number(l),this._levels[l].el.children.length||l===i?(this._levels[l].el.style.zIndex=a-Math.abs(i-l),this._onUpdateLevel(l)):(qt(this._levels[l].el),this._removeTilesAtZoom(l),this._onRemoveLevel(l),delete this._levels[l]);var f=this._levels[i],d=this._map;return f||(f=this._levels[i]={},f.el=Dt("div","leaflet-tile-container leaflet-zoom-animated",this._container),f.el.style.zIndex=a,f.origin=d.project(d.unproject(d.getPixelOrigin()),i).round(),f.zoom=i,this._setZoomTransform(f,d.getCenter(),d.getZoom()),X(f.el.offsetWidth),this._onCreateLevel(f)),this._level=f,f}},_onUpdateLevel:X,_onRemoveLevel:X,_onCreateLevel:X,_pruneTiles:function(){if(this._map){var i,a,l=this._map.getZoom();if(l>this.options.maxZoom||l<this.options.minZoom){this._removeAllTiles();return}for(i in this._tiles)a=this._tiles[i],a.retain=a.current;for(i in this._tiles)if(a=this._tiles[i],a.current&&!a.active){var f=a.coords;this._retainParent(f.x,f.y,f.z,f.z-5)||this._retainChildren(f.x,f.y,f.z,f.z+2)}for(i in this._tiles)this._tiles[i].retain||this._removeTile(i)}},_removeTilesAtZoom:function(i){for(var a in this._tiles)this._tiles[a].coords.z===i&&this._removeTile(a)},_removeAllTiles:function(){for(var i in this._tiles)this._removeTile(i)},_invalidateAll:function(){for(var i in this._levels)qt(this._levels[i].el),this._onRemoveLevel(Number(i)),delete this._levels[i];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(i,a,l,f){var d=Math.floor(i/2),_=Math.floor(a/2),b=l-1,M=new V(+d,+_);M.z=+b;var U=this._tileCoordsToKey(M),I=this._tiles[U];return I&&I.active?(I.retain=!0,!0):(I&&I.loaded&&(I.retain=!0),b>f?this._retainParent(d,_,b,f):!1)},_retainChildren:function(i,a,l,f){for(var d=2*i;d<2*i+2;d++)for(var _=2*a;_<2*a+2;_++){var b=new V(d,_);b.z=l+1;var M=this._tileCoordsToKey(b),U=this._tiles[M];if(U&&U.active){U.retain=!0;continue}else U&&U.loaded&&(U.retain=!0);l+1<f&&this._retainChildren(d,_,l+1,f)}},_resetView:function(i){var a=i&&(i.pinch||i.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),a,a)},_animateZoom:function(i){this._setView(i.center,i.zoom,!0,i.noUpdate)},_clampZoom:function(i){var a=this.options;return a.minNativeZoom!==void 0&&i<a.minNativeZoom?a.minNativeZoom:a.maxNativeZoom!==void 0&&a.maxNativeZoom<i?a.maxNativeZoom:i},_setView:function(i,a,l,f){var d=Math.round(a);this.options.maxZoom!==void 0&&d>this.options.maxZoom||this.options.minZoom!==void 0&&d<this.options.minZoom?d=void 0:d=this._clampZoom(d);var _=this.options.updateWhenZooming&&d!==this._tileZoom;(!f||_)&&(this._tileZoom=d,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),d!==void 0&&this._update(i),l||this._pruneTiles(),this._noPrune=!!l),this._setZoomTransforms(i,a)},_setZoomTransforms:function(i,a){for(var l in this._levels)this._setZoomTransform(this._levels[l],i,a)},_setZoomTransform:function(i,a,l){var f=this._map.getZoomScale(l,i.zoom),d=i.origin.multiplyBy(f).subtract(this._map._getNewPixelOrigin(a,l)).round();rt.any3d?Xe(i.el,d,f):Wt(i.el,d)},_resetGrid:function(){var i=this._map,a=i.options.crs,l=this._tileSize=this.getTileSize(),f=this._tileZoom,d=this._map.getPixelWorldBounds(this._tileZoom);d&&(this._globalTileRange=this._pxBoundsToTileRange(d)),this._wrapX=a.wrapLng&&!this.options.noWrap&&[Math.floor(i.project([0,a.wrapLng[0]],f).x/l.x),Math.ceil(i.project([0,a.wrapLng[1]],f).x/l.y)],this._wrapY=a.wrapLat&&!this.options.noWrap&&[Math.floor(i.project([a.wrapLat[0],0],f).y/l.x),Math.ceil(i.project([a.wrapLat[1],0],f).y/l.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(i){var a=this._map,l=a._animatingZoom?Math.max(a._animateToZoom,a.getZoom()):a.getZoom(),f=a.getZoomScale(l,this._tileZoom),d=a.project(i,this._tileZoom).floor(),_=a.getSize().divideBy(f*2);return new k(d.subtract(_),d.add(_))},_update:function(i){var a=this._map;if(a){var l=this._clampZoom(a.getZoom());if(i===void 0&&(i=a.getCenter()),this._tileZoom!==void 0){var f=this._getTiledPixelBounds(i),d=this._pxBoundsToTileRange(f),_=d.getCenter(),b=[],M=this.options.keepBuffer,U=new k(d.getBottomLeft().subtract([M,-M]),d.getTopRight().add([M,-M]));if(!(isFinite(d.min.x)&&isFinite(d.min.y)&&isFinite(d.max.x)&&isFinite(d.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var I in this._tiles){var $=this._tiles[I].coords;($.z!==this._tileZoom||!U.contains(new V($.x,$.y)))&&(this._tiles[I].current=!1)}if(Math.abs(l-this._tileZoom)>1){this._setView(i,l);return}for(var nt=d.min.y;nt<=d.max.y;nt++)for(var ot=d.min.x;ot<=d.max.x;ot++){var gt=new V(ot,nt);if(gt.z=this._tileZoom,!!this._isValidTile(gt)){var Yt=this._tiles[this._tileCoordsToKey(gt)];Yt?Yt.current=!0:b.push(gt)}}if(b.sort(function(ve,Ke){return ve.distanceTo(_)-Ke.distanceTo(_)}),b.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var $t=document.createDocumentFragment();for(ot=0;ot<b.length;ot++)this._addTile(b[ot],$t);this._level.el.appendChild($t)}}}},_isValidTile:function(i){var a=this._map.options.crs;if(!a.infinite){var l=this._globalTileRange;if(!a.wrapLng&&(i.x<l.min.x||i.x>l.max.x)||!a.wrapLat&&(i.y<l.min.y||i.y>l.max.y))return!1}if(!this.options.bounds)return!0;var f=this._tileCoordsToBounds(i);return et(this.options.bounds).overlaps(f)},_keyToBounds:function(i){return this._tileCoordsToBounds(this._keyToTileCoords(i))},_tileCoordsToNwSe:function(i){var a=this._map,l=this.getTileSize(),f=i.scaleBy(l),d=f.add(l),_=a.unproject(f,i.z),b=a.unproject(d,i.z);return[_,b]},_tileCoordsToBounds:function(i){var a=this._tileCoordsToNwSe(i),l=new K(a[0],a[1]);return this.options.noWrap||(l=this._map.wrapLatLngBounds(l)),l},_tileCoordsToKey:function(i){return i.x+":"+i.y+":"+i.z},_keyToTileCoords:function(i){var a=i.split(":"),l=new V(+a[0],+a[1]);return l.z=+a[2],l},_removeTile:function(i){var a=this._tiles[i];a&&(qt(a.el),delete this._tiles[i],this.fire("tileunload",{tile:a.el,coords:this._keyToTileCoords(i)}))},_initTile:function(i){yt(i,"leaflet-tile");var a=this.getTileSize();i.style.width=a.x+"px",i.style.height=a.y+"px",i.onselectstart=X,i.onmousemove=X,rt.ielt9&&this.options.opacity<1&&ze(i,this.options.opacity)},_addTile:function(i,a){var l=this._getTilePos(i),f=this._tileCoordsToKey(i),d=this.createTile(this._wrapCoords(i),x(this._tileReady,this,i));this._initTile(d),this.createTile.length<2&&q(x(this._tileReady,this,i,null,d)),Wt(d,l),this._tiles[f]={el:d,coords:i,current:!0},a.appendChild(d),this.fire("tileloadstart",{tile:d,coords:i})},_tileReady:function(i,a,l){a&&this.fire("tileerror",{error:a,tile:l,coords:i});var f=this._tileCoordsToKey(i);l=this._tiles[f],l&&(l.loaded=+new Date,this._map._fadeAnimated?(ze(l.el,0),F(this._fadeFrame),this._fadeFrame=q(this._updateOpacity,this)):(l.active=!0,this._pruneTiles()),a||(yt(l.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:l.el,coords:i})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),rt.ielt9||!this._map._fadeAnimated?q(this._pruneTiles,this):setTimeout(x(this._pruneTiles,this),250)))},_getTilePos:function(i){return i.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(i){var a=new V(this._wrapX?j(i.x,this._wrapX):i.x,this._wrapY?j(i.y,this._wrapY):i.y);return a.z=i.z,a},_pxBoundsToTileRange:function(i){var a=this.getTileSize();return new k(i.min.unscaleBy(a).floor(),i.max.unscaleBy(a).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var i in this._tiles)if(!this._tiles[i].loaded)return!1;return!0}});function si(i){return new mn(i)}var pn=mn.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(i,a){this._url=i,a=st(this,a),a.detectRetina&&rt.retina&&a.maxZoom>0?(a.tileSize=Math.floor(a.tileSize/2),a.zoomReverse?(a.zoomOffset--,a.minZoom=Math.min(a.maxZoom,a.minZoom+1)):(a.zoomOffset++,a.maxZoom=Math.max(a.minZoom,a.maxZoom-1)),a.minZoom=Math.max(0,a.minZoom)):a.zoomReverse?a.minZoom=Math.min(a.maxZoom,a.minZoom):a.maxZoom=Math.max(a.minZoom,a.maxZoom),typeof a.subdomains=="string"&&(a.subdomains=a.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(i,a){return this._url===i&&a===void 0&&(a=!0),this._url=i,a||this.redraw(),this},createTile:function(i,a){var l=document.createElement("img");return _t(l,"load",x(this._tileOnLoad,this,a,l)),_t(l,"error",x(this._tileOnError,this,a,l)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(l.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(l.referrerPolicy=this.options.referrerPolicy),l.alt="",l.src=this.getTileUrl(i),l},getTileUrl:function(i){var a={r:rt.retina?"@2x":"",s:this._getSubdomain(i),x:i.x,y:i.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var l=this._globalTileRange.max.y-i.y;this.options.tms&&(a.y=l),a["-y"]=l}return Gt(this._url,g(a,this.options))},_tileOnLoad:function(i,a){rt.ielt9?setTimeout(x(i,this,null,a),0):i(null,a)},_tileOnError:function(i,a,l){var f=this.options.errorTileUrl;f&&a.getAttribute("src")!==f&&(a.src=f),i(l,a)},_onTileRemove:function(i){i.tile.onload=null},_getZoomForUrl:function(){var i=this._tileZoom,a=this.options.maxZoom,l=this.options.zoomReverse,f=this.options.zoomOffset;return l&&(i=a-i),i+f},_getSubdomain:function(i){var a=Math.abs(i.x+i.y)%this.options.subdomains.length;return this.options.subdomains[a]},_abortLoading:function(){var i,a;for(i in this._tiles)if(this._tiles[i].coords.z!==this._tileZoom&&(a=this._tiles[i].el,a.onload=X,a.onerror=X,!a.complete)){a.src=St;var l=this._tiles[i].coords;qt(a),delete this._tiles[i],this.fire("tileabort",{tile:a,coords:l})}},_removeTile:function(i){var a=this._tiles[i];if(a)return a.el.setAttribute("src",St),mn.prototype._removeTile.call(this,i)},_tileReady:function(i,a,l){if(!(!this._map||l&&l.getAttribute("src")===St))return mn.prototype._tileReady.call(this,i,a,l)}});function je(i,a){return new pn(i,a)}var Ge=pn.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(i,a){this._url=i;var l=g({},this.defaultWmsParams);for(var f in a)f in this.options||(l[f]=a[f]);a=st(this,a);var d=a.detectRetina&&rt.retina?2:1,_=this.getTileSize();l.width=_.x*d,l.height=_.y*d,this.wmsParams=l},onAdd:function(i){this._crs=this.options.crs||i.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var a=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[a]=this._crs.code,pn.prototype.onAdd.call(this,i)},getTileUrl:function(i){var a=this._tileCoordsToNwSe(i),l=this._crs,f=J(l.project(a[0]),l.project(a[1])),d=f.min,_=f.max,b=(this._wmsVersion>=1.3&&this._crs===Po?[d.y,d.x,_.y,_.x]:[d.x,d.y,_.x,_.y]).join(","),M=pn.prototype.getTileUrl.call(this,i);return M+xt(this.wmsParams,M,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+b},setParams:function(i,a){return g(this.wmsParams,i),a||this.redraw(),this}});function Jn(i,a){return new Ge(i,a)}pn.WMS=Ge,je.wms=Jn;var li=ii.extend({options:{padding:.1},initialize:function(i){st(this,i),A(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),yt(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var i={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(i.zoomanim=this._onAnimZoom),i},_onAnimZoom:function(i){this._updateTransform(i.center,i.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(i,a){var l=this._map.getZoomScale(a,this._zoom),f=this._map.getSize().multiplyBy(.5+this.options.padding),d=this._map.project(this._center,a),_=f.multiplyBy(-l).add(d).subtract(this._map._getNewPixelOrigin(i,a));rt.any3d?Xe(this._container,_,l):Wt(this._container,_)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var i in this._layers)this._layers[i]._reset()},_onZoomEnd:function(){for(var i in this._layers)this._layers[i]._project()},_updatePaths:function(){for(var i in this._layers)this._layers[i]._update()},_update:function(){var i=this.options.padding,a=this._map.getSize(),l=this._map.containerPointToLayerPoint(a.multiplyBy(-i)).round();this._bounds=new k(l,l.add(a.multiplyBy(1+i*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Za=li.extend({options:{tolerance:0},getEvents:function(){var i=li.prototype.getEvents.call(this);return i.viewprereset=this._onViewPreReset,i},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){li.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var i=this._container=document.createElement("canvas");_t(i,"mousemove",this._onMouseMove,this),_t(i,"click dblclick mousedown mouseup contextmenu",this._onClick,this),_t(i,"mouseout",this._handleMouseOut,this),i._leaflet_disable_events=!0,this._ctx=i.getContext("2d")},_destroyContainer:function(){F(this._redrawRequest),delete this._ctx,qt(this._container),Pt(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var i;this._redrawBounds=null;for(var a in this._layers)i=this._layers[a],i._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){li.prototype._update.call(this);var i=this._bounds,a=this._container,l=i.getSize(),f=rt.retina?2:1;Wt(a,i.min),a.width=f*l.x,a.height=f*l.y,a.style.width=l.x+"px",a.style.height=l.y+"px",rt.retina&&this._ctx.scale(2,2),this._ctx.translate(-i.min.x,-i.min.y),this.fire("update")}},_reset:function(){li.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(i){this._updateDashArray(i),this._layers[A(i)]=i;var a=i._order={layer:i,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=a),this._drawLast=a,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(i){this._requestRedraw(i)},_removePath:function(i){var a=i._order,l=a.next,f=a.prev;l?l.prev=f:this._drawLast=f,f?f.next=l:this._drawFirst=l,delete i._order,delete this._layers[A(i)],this._requestRedraw(i)},_updatePath:function(i){this._extendRedrawBounds(i),i._project(),i._update(),this._requestRedraw(i)},_updateStyle:function(i){this._updateDashArray(i),this._requestRedraw(i)},_updateDashArray:function(i){if(typeof i.options.dashArray=="string"){var a=i.options.dashArray.split(/[, ]+/),l=[],f,d;for(d=0;d<a.length;d++){if(f=Number(a[d]),isNaN(f))return;l.push(f)}i.options._dashArray=l}else i.options._dashArray=i.options.dashArray},_requestRedraw:function(i){this._map&&(this._extendRedrawBounds(i),this._redrawRequest=this._redrawRequest||q(this._redraw,this))},_extendRedrawBounds:function(i){if(i._pxBounds){var a=(i.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new k,this._redrawBounds.extend(i._pxBounds.min.subtract([a,a])),this._redrawBounds.extend(i._pxBounds.max.add([a,a]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var i=this._redrawBounds;if(i){var a=i.getSize();this._ctx.clearRect(i.min.x,i.min.y,a.x,a.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var i,a=this._redrawBounds;if(this._ctx.save(),a){var l=a.getSize();this._ctx.beginPath(),this._ctx.rect(a.min.x,a.min.y,l.x,l.y),this._ctx.clip()}this._drawing=!0;for(var f=this._drawFirst;f;f=f.next)i=f.layer,(!a||i._pxBounds&&i._pxBounds.intersects(a))&&i._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(i,a){if(this._drawing){var l,f,d,_,b=i._parts,M=b.length,U=this._ctx;if(M){for(U.beginPath(),l=0;l<M;l++){for(f=0,d=b[l].length;f<d;f++)_=b[l][f],U[f?"lineTo":"moveTo"](_.x,_.y);a&&U.closePath()}this._fillStroke(U,i)}}},_updateCircle:function(i){if(!(!this._drawing||i._empty())){var a=i._point,l=this._ctx,f=Math.max(Math.round(i._radius),1),d=(Math.max(Math.round(i._radiusY),1)||f)/f;d!==1&&(l.save(),l.scale(1,d)),l.beginPath(),l.arc(a.x,a.y/d,f,0,Math.PI*2,!1),d!==1&&l.restore(),this._fillStroke(l,i)}},_fillStroke:function(i,a){var l=a.options;l.fill&&(i.globalAlpha=l.fillOpacity,i.fillStyle=l.fillColor||l.color,i.fill(l.fillRule||"evenodd")),l.stroke&&l.weight!==0&&(i.setLineDash&&i.setLineDash(a.options&&a.options._dashArray||[]),i.globalAlpha=l.opacity,i.lineWidth=l.weight,i.strokeStyle=l.color,i.lineCap=l.lineCap,i.lineJoin=l.lineJoin,i.stroke())},_onClick:function(i){for(var a=this._map.mouseEventToLayerPoint(i),l,f,d=this._drawFirst;d;d=d.next)l=d.layer,l.options.interactive&&l._containsPoint(a)&&(!(i.type==="click"||i.type==="preclick")||!this._map._draggableMoved(l))&&(f=l);this._fireEvent(f?[f]:!1,i)},_onMouseMove:function(i){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var a=this._map.mouseEventToLayerPoint(i);this._handleMouseHover(i,a)}},_handleMouseOut:function(i){var a=this._hoveredLayer;a&&(Vt(this._container,"leaflet-interactive"),this._fireEvent([a],i,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(i,a){if(!this._mouseHoverThrottled){for(var l,f,d=this._drawFirst;d;d=d.next)l=d.layer,l.options.interactive&&l._containsPoint(a)&&(f=l);f!==this._hoveredLayer&&(this._handleMouseOut(i),f&&(yt(this._container,"leaflet-interactive"),this._fireEvent([f],i,"mouseover"),this._hoveredLayer=f)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,i),this._mouseHoverThrottled=!0,setTimeout(x(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(i,a,l){this._map._fireDOMEvent(a,l||a.type,i)},_bringToFront:function(i){var a=i._order;if(a){var l=a.next,f=a.prev;if(l)l.prev=f;else return;f?f.next=l:l&&(this._drawFirst=l),a.prev=this._drawLast,this._drawLast.next=a,a.next=null,this._drawLast=a,this._requestRedraw(i)}},_bringToBack:function(i){var a=i._order;if(a){var l=a.next,f=a.prev;if(f)f.next=l;else return;l?l.prev=f:f&&(this._drawLast=f),a.prev=null,a.next=this._drawFirst,this._drawFirst.prev=a,this._drawFirst=a,this._requestRedraw(i)}}});function Ha(i){return rt.canvas?new Za(i):null}var gn=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(i){return document.createElement("<lvml:"+i+' class="lvml">')}}catch{}return function(i){return document.createElement("<"+i+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),Wn={_initContainer:function(){this._container=Dt("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(li.prototype._update.call(this),this.fire("update"))},_initPath:function(i){var a=i._container=gn("shape");yt(a,"leaflet-vml-shape "+(this.options.className||"")),a.coordsize="1 1",i._path=gn("path"),a.appendChild(i._path),this._updateStyle(i),this._layers[A(i)]=i},_addPath:function(i){var a=i._container;this._container.appendChild(a),i.options.interactive&&i.addInteractiveTarget(a)},_removePath:function(i){var a=i._container;qt(a),i.removeInteractiveTarget(a),delete this._layers[A(i)]},_updateStyle:function(i){var a=i._stroke,l=i._fill,f=i.options,d=i._container;d.stroked=!!f.stroke,d.filled=!!f.fill,f.stroke?(a||(a=i._stroke=gn("stroke")),d.appendChild(a),a.weight=f.weight+"px",a.color=f.color,a.opacity=f.opacity,f.dashArray?a.dashStyle=Tt(f.dashArray)?f.dashArray.join(" "):f.dashArray.replace(/( *, *)/g," "):a.dashStyle="",a.endcap=f.lineCap.replace("butt","flat"),a.joinstyle=f.lineJoin):a&&(d.removeChild(a),i._stroke=null),f.fill?(l||(l=i._fill=gn("fill")),d.appendChild(l),l.color=f.fillColor||f.color,l.opacity=f.fillOpacity):l&&(d.removeChild(l),i._fill=null)},_updateCircle:function(i){var a=i._point.round(),l=Math.round(i._radius),f=Math.round(i._radiusY||l);this._setPath(i,i._empty()?"M0 0":"AL "+a.x+","+a.y+" "+l+","+f+" 0,"+65535*360)},_setPath:function(i,a){i._path.v=a},_bringToFront:function(i){sn(i._container)},_bringToBack:function(i){Pn(i._container)}},ja=rt.vml?gn:ro,Ii=li.extend({_initContainer:function(){this._container=ja("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=ja("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){qt(this._container),Pt(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){li.prototype._update.call(this);var i=this._bounds,a=i.getSize(),l=this._container;(!this._svgSize||!this._svgSize.equals(a))&&(this._svgSize=a,l.setAttribute("width",a.x),l.setAttribute("height",a.y)),Wt(l,i.min),l.setAttribute("viewBox",[i.min.x,i.min.y,a.x,a.y].join(" ")),this.fire("update")}},_initPath:function(i){var a=i._path=ja("path");i.options.className&&yt(a,i.options.className),i.options.interactive&&yt(a,"leaflet-interactive"),this._updateStyle(i),this._layers[A(i)]=i},_addPath:function(i){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(i._path),i.addInteractiveTarget(i._path)},_removePath:function(i){qt(i._path),i.removeInteractiveTarget(i._path),delete this._layers[A(i)]},_updatePath:function(i){i._project(),i._update()},_updateStyle:function(i){var a=i._path,l=i.options;a&&(l.stroke?(a.setAttribute("stroke",l.color),a.setAttribute("stroke-opacity",l.opacity),a.setAttribute("stroke-width",l.weight),a.setAttribute("stroke-linecap",l.lineCap),a.setAttribute("stroke-linejoin",l.lineJoin),l.dashArray?a.setAttribute("stroke-dasharray",l.dashArray):a.removeAttribute("stroke-dasharray"),l.dashOffset?a.setAttribute("stroke-dashoffset",l.dashOffset):a.removeAttribute("stroke-dashoffset")):a.setAttribute("stroke","none"),l.fill?(a.setAttribute("fill",l.fillColor||l.color),a.setAttribute("fill-opacity",l.fillOpacity),a.setAttribute("fill-rule",l.fillRule||"evenodd")):a.setAttribute("fill","none"))},_updatePoly:function(i,a){this._setPath(i,uo(i._parts,a))},_updateCircle:function(i){var a=i._point,l=Math.max(Math.round(i._radius),1),f=Math.max(Math.round(i._radiusY),1)||l,d="a"+l+","+f+" 0 1,0 ",_=i._empty()?"M0 0":"M"+(a.x-l)+","+a.y+d+l*2+",0 "+d+-l*2+",0 ";this._setPath(i,_)},_setPath:function(i,a){i._path.setAttribute("d",a)},_bringToFront:function(i){sn(i._path)},_bringToBack:function(i){Pn(i._path)}});rt.vml&&Ii.include(Wn);function _n(i){return rt.svg||rt.vml?new Ii(i):null}wt.include({getRenderer:function(i){var a=i.options.renderer||this._getPaneRenderer(i.options.pane)||this.options.renderer||this._renderer;return a||(a=this._renderer=this._createRenderer()),this.hasLayer(a)||this.addLayer(a),a},_getPaneRenderer:function(i){if(i==="overlayPane"||i===void 0)return!1;var a=this._paneRenderers[i];return a===void 0&&(a=this._createRenderer({pane:i}),this._paneRenderers[i]=a),a},_createRenderer:function(i){return this.options.preferCanvas&&Ha(i)||_n(i)}});var Go=Kn.extend({initialize:function(i,a){Kn.prototype.initialize.call(this,this._boundsToLatLngs(i),a)},setBounds:function(i){return this.setLatLngs(this._boundsToLatLngs(i))},_boundsToLatLngs:function(i){return i=et(i),[i.getSouthWest(),i.getNorthWest(),i.getNorthEast(),i.getSouthEast()]}});function Ie(i,a){return new Go(i,a)}Ii.create=ja,Ii.pointsToPath=uo,He.geometryToLayer=Pa,He.coordsToLatLng=_s,He.coordsToLatLngs=Ba,He.latLngToCoords=Ua,He.latLngsToCoords=vs,He.getFeature=ni,He.asFeature=Qn,wt.mergeOptions({boxZoom:!0});var Ts=ei.extend({initialize:function(i){this._map=i,this._container=i._container,this._pane=i._panes.overlayPane,this._resetStateTimeout=0,i.on("unload",this._destroy,this)},addHooks:function(){_t(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){Pt(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){qt(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(i){if(!i.shiftKey||i.which!==1&&i.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),mi(),Ea(),this._startPoint=this._map.mouseEventToContainerPoint(i),_t(document,{contextmenu:_i,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(i){this._moved||(this._moved=!0,this._box=Dt("div","leaflet-zoom-box",this._container),yt(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(i);var a=new k(this._point,this._startPoint),l=a.getSize();Wt(this._box,a.min),this._box.style.width=l.x+"px",this._box.style.height=l.y+"px"},_finish:function(){this._moved&&(qt(this._box),Vt(this._container,"leaflet-crosshair")),Aa(),sl(),Pt(document,{contextmenu:_i,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(i){if(!(i.which!==1&&i.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(x(this._resetState,this),0);var a=new K(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(a).fire("boxzoomend",{boxZoomBounds:a})}},_onKeyDown:function(i){i.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});wt.addInitHook("addHandler","boxZoom",Ts),wt.mergeOptions({doubleClickZoom:!0});var yi=ei.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(i){var a=this._map,l=a.getZoom(),f=a.options.zoomDelta,d=i.originalEvent.shiftKey?l-f:l+f;a.options.doubleClickZoom==="center"?a.setZoom(d):a.setZoomAround(i.containerPoint,d)}});wt.addInitHook("addHandler","doubleClickZoom",yi),wt.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var yl=ei.extend({addHooks:function(){if(!this._draggable){var i=this._map;this._draggable=new ji(i._mapPane,i._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),i.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),i.on("zoomend",this._onZoomEnd,this),i.whenReady(this._onZoomEnd,this))}yt(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){Vt(this._map._container,"leaflet-grab"),Vt(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var i=this._map;if(i._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var a=et(this._map.options.maxBounds);this._offsetLimit=J(this._map.latLngToContainerPoint(a.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(a.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;i.fire("movestart").fire("dragstart"),i.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(i){if(this._map.options.inertia){var a=this._lastTime=+new Date,l=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(l),this._times.push(a),this._prunePositions(a)}this._map.fire("move",i).fire("drag",i)},_prunePositions:function(i){for(;this._positions.length>1&&i-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var i=this._map.getSize().divideBy(2),a=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=a.subtract(i).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(i,a){return i-(i-a)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var i=this._draggable._newPos.subtract(this._draggable._startPos),a=this._offsetLimit;i.x<a.min.x&&(i.x=this._viscousLimit(i.x,a.min.x)),i.y<a.min.y&&(i.y=this._viscousLimit(i.y,a.min.y)),i.x>a.max.x&&(i.x=this._viscousLimit(i.x,a.max.x)),i.y>a.max.y&&(i.y=this._viscousLimit(i.y,a.max.y)),this._draggable._newPos=this._draggable._startPos.add(i)}},_onPreDragWrap:function(){var i=this._worldWidth,a=Math.round(i/2),l=this._initialWorldOffset,f=this._draggable._newPos.x,d=(f-a+l)%i+a-l,_=(f+a+l)%i-a-l,b=Math.abs(d+l)<Math.abs(_+l)?d:_;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=b},_onDragEnd:function(i){var a=this._map,l=a.options,f=!l.inertia||i.noInertia||this._times.length<2;if(a.fire("dragend",i),f)a.fire("moveend");else{this._prunePositions(+new Date);var d=this._lastPos.subtract(this._positions[0]),_=(this._lastTime-this._times[0])/1e3,b=l.easeLinearity,M=d.multiplyBy(b/_),U=M.distanceTo([0,0]),I=Math.min(l.inertiaMaxSpeed,U),$=M.multiplyBy(I/U),nt=I/(l.inertiaDeceleration*b),ot=$.multiplyBy(-nt/2).round();!ot.x&&!ot.y?a.fire("moveend"):(ot=a._limitOffset(ot,a.options.maxBounds),q(function(){a.panBy(ot,{duration:nt,easeLinearity:b,noMoveStart:!0,animate:!0})}))}}});wt.addInitHook("addHandler","dragging",yl),wt.mergeOptions({keyboard:!0,keyboardPanDelta:80});var Ga=ei.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(i){this._map=i,this._setPanDelta(i.options.keyboardPanDelta),this._setZoomDelta(i.options.zoomDelta)},addHooks:function(){var i=this._map._container;i.tabIndex<=0&&(i.tabIndex="0"),_t(i,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),Pt(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var i=document.body,a=document.documentElement,l=i.scrollTop||a.scrollTop,f=i.scrollLeft||a.scrollLeft;this._map._container.focus(),window.scrollTo(f,l)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(i){var a=this._panKeys={},l=this.keyCodes,f,d;for(f=0,d=l.left.length;f<d;f++)a[l.left[f]]=[-1*i,0];for(f=0,d=l.right.length;f<d;f++)a[l.right[f]]=[i,0];for(f=0,d=l.down.length;f<d;f++)a[l.down[f]]=[0,i];for(f=0,d=l.up.length;f<d;f++)a[l.up[f]]=[0,-1*i]},_setZoomDelta:function(i){var a=this._zoomKeys={},l=this.keyCodes,f,d;for(f=0,d=l.zoomIn.length;f<d;f++)a[l.zoomIn[f]]=i;for(f=0,d=l.zoomOut.length;f<d;f++)a[l.zoomOut[f]]=-i},_addHooks:function(){_t(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){Pt(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(i){if(!(i.altKey||i.ctrlKey||i.metaKey)){var a=i.keyCode,l=this._map,f;if(a in this._panKeys){if(!l._panAnim||!l._panAnim._inProgress)if(f=this._panKeys[a],i.shiftKey&&(f=S(f).multiplyBy(3)),l.options.maxBounds&&(f=l._limitOffset(S(f),l.options.maxBounds)),l.options.worldCopyJump){var d=l.wrapLatLng(l.unproject(l.project(l.getCenter()).add(f)));l.panTo(d)}else l.panBy(f)}else if(a in this._zoomKeys)l.setZoom(l.getZoom()+(i.shiftKey?3:1)*this._zoomKeys[a]);else if(a===27&&l._popup&&l._popup.options.closeOnEscapeKey)l.closePopup();else return;_i(i)}}});wt.addInitHook("addHandler","keyboard",Ga),wt.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var qi=ei.extend({addHooks:function(){_t(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){Pt(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(i){var a=Da(i),l=this._map.options.wheelDebounceTime;this._delta+=a,this._lastMousePos=this._map.mouseEventToContainerPoint(i),this._startTime||(this._startTime=+new Date);var f=Math.max(l-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(x(this._performZoom,this),f),_i(i)},_performZoom:function(){var i=this._map,a=i.getZoom(),l=this._map.options.zoomSnap||0;i._stop();var f=this._delta/(this._map.options.wheelPxPerZoomLevel*4),d=4*Math.log(2/(1+Math.exp(-Math.abs(f))))/Math.LN2,_=l?Math.ceil(d/l)*l:d,b=i._limitZoom(a+(this._delta>0?_:-_))-a;this._delta=0,this._startTime=null,b&&(i.options.scrollWheelZoom==="center"?i.setZoom(a+b):i.setZoomAround(this._lastMousePos,a+b))}});wt.addInitHook("addHandler","scrollWheelZoom",qi);var Sl=600;wt.mergeOptions({tapHold:rt.touchNative&&rt.safari&&rt.mobile,tapTolerance:15});var xs=ei.extend({addHooks:function(){_t(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){Pt(this._map._container,"touchstart",this._onDown,this)},_onDown:function(i){if(clearTimeout(this._holdTimeout),i.touches.length===1){var a=i.touches[0];this._startPos=this._newPos=new V(a.clientX,a.clientY),this._holdTimeout=setTimeout(x(function(){this._cancel(),this._isTapValid()&&(_t(document,"touchend",ee),_t(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",a))},this),Sl),_t(document,"touchend touchcancel contextmenu",this._cancel,this),_t(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function i(){Pt(document,"touchend",ee),Pt(document,"touchend touchcancel",i)},_cancel:function(){clearTimeout(this._holdTimeout),Pt(document,"touchend touchcancel contextmenu",this._cancel,this),Pt(document,"touchmove",this._onMove,this)},_onMove:function(i){var a=i.touches[0];this._newPos=new V(a.clientX,a.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(i,a){var l=new MouseEvent(i,{bubbles:!0,cancelable:!0,view:window,screenX:a.screenX,screenY:a.screenY,clientX:a.clientX,clientY:a.clientY});l._simulated=!0,a.target.dispatchEvent(l)}});wt.addInitHook("addHandler","tapHold",xs),wt.mergeOptions({touchZoom:rt.touch,bounceAtZoomLimits:!0});var vn=ei.extend({addHooks:function(){yt(this._map._container,"leaflet-touch-zoom"),_t(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){Vt(this._map._container,"leaflet-touch-zoom"),Pt(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(i){var a=this._map;if(!(!i.touches||i.touches.length!==2||a._animatingZoom||this._zooming)){var l=a.mouseEventToContainerPoint(i.touches[0]),f=a.mouseEventToContainerPoint(i.touches[1]);this._centerPoint=a.getSize()._divideBy(2),this._startLatLng=a.containerPointToLatLng(this._centerPoint),a.options.touchZoom!=="center"&&(this._pinchStartLatLng=a.containerPointToLatLng(l.add(f)._divideBy(2))),this._startDist=l.distanceTo(f),this._startZoom=a.getZoom(),this._moved=!1,this._zooming=!0,a._stop(),_t(document,"touchmove",this._onTouchMove,this),_t(document,"touchend touchcancel",this._onTouchEnd,this),ee(i)}},_onTouchMove:function(i){if(!(!i.touches||i.touches.length!==2||!this._zooming)){var a=this._map,l=a.mouseEventToContainerPoint(i.touches[0]),f=a.mouseEventToContainerPoint(i.touches[1]),d=l.distanceTo(f)/this._startDist;if(this._zoom=a.getScaleZoom(d,this._startZoom),!a.options.bounceAtZoomLimits&&(this._zoom<a.getMinZoom()&&d<1||this._zoom>a.getMaxZoom()&&d>1)&&(this._zoom=a._limitZoom(this._zoom)),a.options.touchZoom==="center"){if(this._center=this._startLatLng,d===1)return}else{var _=l._add(f)._divideBy(2)._subtract(this._centerPoint);if(d===1&&_.x===0&&_.y===0)return;this._center=a.unproject(a.project(this._pinchStartLatLng,this._zoom).subtract(_),this._zoom)}this._moved||(a._moveStart(!0,!1),this._moved=!0),F(this._animRequest);var b=x(a._move,a,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=q(b,this,!0),ee(i)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,F(this._animRequest),Pt(document,"touchmove",this._onTouchMove,this),Pt(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});wt.addInitHook("addHandler","touchZoom",vn),wt.BoxZoom=Ts,wt.DoubleClickZoom=yi,wt.Drag=yl,wt.Keyboard=Ga,wt.ScrollWheelZoom=qi,wt.TapHold=xs,wt.TouchZoom=vn,u.Bounds=k,u.Browser=rt,u.CRS=Jt,u.Canvas=Za,u.Circle=gl,u.CircleMarker=Ra,u.Class=Mt,u.Control=Ne,u.DivIcon=vl,u.DivOverlay=ai,u.DomEvent=pu,u.DomUtil=Co,u.Draggable=ji,u.Evented=Q,u.FeatureGroup=ke,u.GeoJSON=He,u.GridLayer=mn,u.Handler=ei,u.Icon=Vn,u.ImageOverlay=vi,u.LatLng=lt,u.LatLngBounds=K,u.Layer=ii,u.LayerGroup=hn,u.LineUtil=No,u.Map=wt,u.Marker=Xn,u.Mixin=Su,u.Path=Mi,u.Point=V,u.PolyUtil=bu,u.Polygon=Kn,u.Polyline=Oi,u.Popup=ka,u.PosAnimation=fs,u.Projection=Ro,u.Rectangle=Go,u.Renderer=li,u.SVG=Ii,u.SVGOverlay=Gi,u.TileLayer=pn,u.Tooltip=bs,u.Transformation=En,u.Util=vt,u.VideoOverlay=Ss,u.bind=x,u.bounds=J,u.canvas=Ha,u.circle=Au,u.circleMarker=Uo,u.control=In,u.divIcon=jo,u.extend=g,u.featureGroup=Na,u.geoJSON=ys,u.geoJson=ko,u.gridLayer=si,u.icon=gs,u.imageOverlay=Fn,u.latLng=W,u.latLngBounds=et,u.layerGroup=Bo,u.map=za,u.marker=pl,u.point=S,u.polygon=Ze,u.polyline=Eu,u.popup=Mu,u.rectangle=Ie,u.setOptions=st,u.stamp=A,u.svg=_n,u.svgOverlay=Ho,u.tileLayer=je,u.tooltip=Ou,u.transformation=Fi,u.version=h,u.videoOverlay=Zo;var $n=window.L;u.noConflict=function(){return window.L=$n,this},window.L=u})}(ao,ao.exports)),ao.exports}var Qs=p_();const su=Fm(Qs);function lu(p,r,u){return Object.freeze({instance:p,context:r,container:u})}function ou(p,r){return r==null?function(h,g){const y=it.useRef(void 0);return y.current||(y.current=p(h,g)),y}:function(h,g){const y=it.useRef(void 0);y.current||(y.current=p(h,g));const x=it.useRef(h),{instance:N}=y.current;return it.useEffect(function(){x.current!==h&&(r(N,h,x.current),x.current=h)},[N,h,r]),y}}function g_(p,r){it.useEffect(function(){return(r.layerContainer??r.map).addLayer(p.instance),function(){r.layerContainer?.removeLayer(p.instance),r.map.removeLayer(p.instance)}},[r,p])}function hp(p){return function(u){const h=oo(),g=p(Nf(u,h),h);return up(h.map,u.attribution),fp(g.current,u.eventHandlers),g_(g.current,h),g}}function __(p,r){const u=ou(p,r),h=hp(u);return cp(h)}function v_(p,r){const u=ou(p),h=d_(u,r);return c_(h)}function y_(p,r){const u=ou(p,r),h=hp(u);return f_(h)}function S_(p,r,u){const{opacity:h,zIndex:g}=r;h!=null&&h!==u.opacity&&p.setOpacity(h),g!=null&&g!==u.zIndex&&p.setZIndex(g)}function b_(){return oo().map}const T_=ou(function({children:r,...u},h){const g=new Qs.Control.Layers(void 0,void 0,u);return lu(g,zf(h,{layersControl:g}))},function(r,u,h){u.collapsed!==h.collapsed&&(u.collapsed===!0?r.collapse():r.expand())}),x_=h_(T_),$a=cp(x_);function dp(p){return function(u){const h=oo(),g=it.useRef(u),[y,x]=it.useState(null),{layersControl:N,map:A}=h,E=it.useCallback(Y=>{N!=null&&(g.current.checked&&A.addLayer(Y),p(N,Y,g.current.name),x(Y))},[p,N,A]),j=it.useCallback(Y=>{N?.removeLayer(Y),x(null)},[N]),X=it.useMemo(()=>zf(h,{layerContainer:{addLayer:E,removeLayer:j}}),[h,E,j]);return it.useEffect(()=>{y!==null&&g.current!==u&&(u.checked===!0&&(g.current.checked==null||g.current.checked===!1)?A.addLayer(y):g.current.checked===!0&&(u.checked==null||u.checked===!1)&&A.removeLayer(y),g.current=u)}),u.children?Qr.createElement(au,{value:X},u.children):null}}$a.BaseLayer=dp(function(r,u,h){r.addBaseLayer(u,h)});$a.Overlay=dp(function(r,u,h){r.addOverlay(u,h)});function C_({bounds:p,boundsOptions:r,center:u,children:h,className:g,id:y,placeholder:x,style:N,whenReady:A,zoom:E,...j},X){const[Y]=it.useState({className:g,id:y,style:N}),[dt,ht]=it.useState(null),st=it.useRef(void 0);it.useImperativeHandle(X,()=>dt?.map??null,[dt]);const xt=it.useCallback(Gt=>{if(Gt!==null&&!st.current){const Tt=new Qs.Map(Gt,j);st.current=Tt,u!=null&&E!=null?Tt.setView(u,E):p!=null&&Tt.fitBounds(p,r),A!=null&&Tt.whenReady(A),ht(u_(Tt))}},[]);it.useEffect(()=>()=>{dt?.map.remove()},[dt]);const Kt=dt?Qr.createElement(au,{value:dt},h):x??null;return Qr.createElement("div",{...Y,ref:xt},Kt)}const w_=it.forwardRef(C_),L_=__(function({position:r,...u},h){const g=new Qs.Marker(r,u);return lu(g,zf(h,{overlayContainer:g}))},function(r,u,h){u.position!==h.position&&r.setLatLng(u.position),u.icon!=null&&u.icon!==h.icon&&r.setIcon(u.icon),u.zIndexOffset!=null&&u.zIndexOffset!==h.zIndexOffset&&r.setZIndexOffset(u.zIndexOffset),u.opacity!=null&&u.opacity!==h.opacity&&r.setOpacity(u.opacity),r.dragging!=null&&u.draggable!==h.draggable&&(u.draggable===!0?r.dragging.enable():r.dragging.disable())}),A_=v_(function(r,u){const h=new Qs.Popup(r,u.overlayContainer);return lu(h,u)},function(r,u,{position:h},g){it.useEffect(function(){const{instance:x}=r;function N(E){E.popup===x&&(x.update(),g(!0))}function A(E){E.popup===x&&g(!1)}return u.map.on({popupopen:N,popupclose:A}),u.overlayContainer==null?(h!=null&&x.setLatLng(h),x.openOn(u.map)):u.overlayContainer.bindPopup(x),function(){u.map.off({popupopen:N,popupclose:A}),u.overlayContainer?.unbindPopup(),u.map.removeLayer(x)}},[r,u,g,h])}),Yr=y_(function({url:r,...u},h){const g=new Qs.TileLayer(r,Nf(u,h));return lu(g,h)},function(r,u,h){S_(r,u,h);const{url:g}=u;g!=null&&g!==h.url&&r.setUrl(g)});delete su.Icon.Default.prototype._getIconUrl;su.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});const E_=p=>su.divIcon({className:"photo-marker",html:`
      <div class="photo-marker-container">
        <img src="${p}" alt="Photo" class="photo-marker-image" />
        <div class="photo-marker-pin"></div>
      </div>
    `,iconSize:[40,50],iconAnchor:[20,50],popupAnchor:[0,-50]}),M_=({photos:p})=>{const r=b_();return it.useEffect(()=>{if(p.length===0)return;const u=p.filter(h=>h.hasGPS&&h.latitude&&h.longitude);if(u.length!==0)if(u.length===1){const h=u[0];r.setView([h.latitude,h.longitude],10)}else{const h=su.latLngBounds(u.map(g=>[g.latitude,g.longitude]));r.fitBounds(h,{padding:[20,20]})}},[p,r]),null},O_=({photos:p,onPhotoLocationUpdate:r})=>{const u=p.filter(h=>h.hasGPS&&h.latitude!==void 0&&h.longitude!==void 0&&h.latitude!==null&&h.longitude!==null&&!isNaN(h.latitude)&&!isNaN(h.longitude));return u.length===0?O.jsx("div",{className:"map-container",children:O.jsx("div",{className:"map-placeholder",children:O.jsxs("div",{className:"map-placeholder-content",children:[O.jsx("h3",{children:"World Map"}),O.jsx("p",{children:"Upload photos with GPS data to see them plotted on the map"})]})})}):O.jsxs("div",{className:"map-container",children:[O.jsx("div",{className:"map-header",children:O.jsxs("h3",{children:["Photo Locations (",u.length," photos)"]})}),O.jsxs(w_,{center:[20,0],zoom:2,style:{height:"100%",width:"100%"},className:"photo-map",children:[O.jsxs($a,{position:"topright",children:[O.jsx($a.BaseLayer,{checked:!0,name:"Street Map",children:O.jsx(Yr,{attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"})}),O.jsx($a.BaseLayer,{name:"Satellite",children:O.jsx(Yr,{attribution:'© <a href="https://www.esri.com/">Esri</a> — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",maxZoom:19})}),O.jsx($a.BaseLayer,{name:"Terrain",children:O.jsx(Yr,{attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',url:"https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png"})}),O.jsx($a.BaseLayer,{name:"Dark Mode",children:O.jsx(Yr,{attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',url:"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png"})})]}),O.jsx(M_,{photos:u}),u.map(h=>O.jsx(L_,{position:[h.latitude,h.longitude],icon:E_(h.url),draggable:!0,eventHandlers:{dragend:g=>{const x=g.target.getLatLng();console.log(`Photo ${h.name} dragged to:`,x.lat,x.lng),r&&r(h.id,x.lat,x.lng)}},children:O.jsx(A_,{maxWidth:300,className:"photo-popup",children:O.jsxs("div",{className:"popup-content",children:[O.jsx("img",{src:h.url,alt:h.name,className:"popup-image"}),O.jsxs("div",{className:"popup-info",children:[O.jsx("h4",{children:h.name}),O.jsxs("p",{className:"popup-coordinates",children:["📍 ",h.latitude?.toFixed(6),", ",h.longitude?.toFixed(6)]}),h.metadata?.DateTimeOriginal&&O.jsxs("p",{className:"popup-date",children:["📅 ",new Date(h.metadata.DateTimeOriginal).toLocaleDateString()]}),h.metadata?.Make&&h.metadata?.Model&&O.jsxs("p",{className:"popup-camera",children:["📷 ",h.metadata.Make," ",h.metadata.Model]})]})]})})},h.id))]})]})};class D_{COLLECTIONS_KEY="photo-mapper-collections";CURRENT_COLLECTION_KEY="photo-mapper-current";async saveCollection(r,u,h){console.log("StorageService.saveCollection called:",{name:r,photosCount:u.length,description:h});const g=this.getAllCollections(),y=`collection-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;console.log("Converting photos to base64...");const x=await Promise.all(u.map(async(A,E)=>{console.log(`Converting photo ${E+1}/${u.length}:`,A.file?.name);const j=await this.fileToBase64(A.file);return{...A,base64Data:j,fileType:A.file.type,fileName:A.file.name}}));console.log("Creating collection object...");const N={id:y,name:r,description:h,photos:x,createdAt:new Date,updatedAt:new Date};return console.log("Saving to localStorage..."),g[y]=N,localStorage.setItem(this.COLLECTIONS_KEY,JSON.stringify(g)),console.log("Collection saved successfully with ID:",y),y}async updateCollection(r,u,h,g){console.log("StorageService.updateCollection called:",{id:r,photosCount:u.length,name:h,description:g});const y=this.getAllCollections(),x=y[r];if(!x)throw new Error(`Collection with id ${r} not found`);console.log("Converting photos to base64...");const N=await Promise.all(u.map(async(E,j)=>{console.log(`Converting photo ${j+1}/${u.length}:`,E.file?.name);const X=await this.fileToBase64(E.file);return{...E,base64Data:X,fileType:E.file.type,fileName:E.file.name}}));console.log("Updating collection object...");const A={...x,name:h||x.name,description:g!==void 0?g:x.description,photos:N,updatedAt:new Date};console.log("Saving updated collection to localStorage..."),y[r]=A,localStorage.setItem(this.COLLECTIONS_KEY,JSON.stringify(y)),console.log("Collection updated successfully")}async loadCollection(r){const h=this.getAllCollections()[r];if(!h)return null;const g=await Promise.all(h.photos.map(async y=>{if(y.base64Data){const x=await this.base64ToFile(y.base64Data,y.fileName||"image.jpg",y.fileType||"image/jpeg"),N=URL.createObjectURL(x);return{...y,file:x,url:N,base64Data:void 0}}return y}));return{...h,photos:g,createdAt:new Date(h.createdAt),updatedAt:new Date(h.updatedAt)}}getAllCollectionMetadata(){const r=this.getAllCollections();return Object.values(r).map(u=>({id:u.id,name:u.name,description:u.description,photoCount:u.photos.length,createdAt:new Date(u.createdAt),updatedAt:new Date(u.updatedAt)}))}deleteCollection(r){const u=this.getAllCollections();return u[r]?(delete u[r],localStorage.setItem(this.COLLECTIONS_KEY,JSON.stringify(u)),!0):!1}saveCurrentCollection(r){const u={photos:r.map(h=>({...h,file:void 0,url:void 0})),savedAt:new Date};localStorage.setItem(this.CURRENT_COLLECTION_KEY,JSON.stringify(u))}loadCurrentCollection(){const r=localStorage.getItem(this.CURRENT_COLLECTION_KEY);if(!r)return null;try{return JSON.parse(r).photos||null}catch(u){return console.error("Error loading current collection:",u),null}}clearCurrentCollection(){localStorage.removeItem(this.CURRENT_COLLECTION_KEY)}getAllCollections(){const r=localStorage.getItem(this.COLLECTIONS_KEY);if(!r)return{};try{return JSON.parse(r)}catch(u){return console.error("Error parsing collections data:",u),{}}}async fileToBase64(r){return new Promise((u,h)=>{const g=new FileReader;g.onload=()=>{typeof g.result=="string"?u(g.result):h(new Error("Failed to convert file to base64"))},g.onerror=h,g.readAsDataURL(r)})}async base64ToFile(r,u,h){const y=await(await fetch(r)).blob();return new File([y],u,{type:h})}async exportCollection(r){const u=await this.loadCollection(r);if(!u)return;const h={...u,photos:await Promise.all(u.photos.map(async A=>({...A,base64Data:await this.fileToBase64(A.file),file:void 0,url:void 0})))},g=JSON.stringify(h,null,2),y=new Blob([g],{type:"application/json"}),x=URL.createObjectURL(y),N=document.createElement("a");N.href=x,N.download=`photo-mapper-${u.name.replace(/[^a-z0-9]/gi,"_")}.json`,document.body.appendChild(N),N.click(),document.body.removeChild(N),URL.revokeObjectURL(x)}async importCollection(r){try{const u=await r.text(),h=JSON.parse(u);if(!h.name||!Array.isArray(h.photos))throw new Error("Invalid collection file format");const g=`collection-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,y={...h,id:g,createdAt:new Date,updatedAt:new Date},x=this.getAllCollections();return x[g]=y,localStorage.setItem(this.COLLECTIONS_KEY,JSON.stringify(x)),g}catch(u){return console.error("Error importing collection:",u),null}}}const hi=new D_,z_=({currentPhotos:p,onLoadCollection:r,getCurrentPhotos:u,onClose:h})=>{const[g,y]=it.useState([]),[x,N]=it.useState(p),[A,E]=it.useState(""),[j,X]=it.useState(""),[Y,dt]=it.useState(!1),[ht,st]=it.useState(!1),[xt,Kt]=it.useState("manage");it.useEffect(()=>{Gt(),N(u())},[u]),it.useEffect(()=>{N(p)},[p]);const Gt=it.useCallback(()=>{const q=hi.getAllCollectionMetadata();y(q.sort((F,vt)=>vt.updatedAt.getTime()-F.updatedAt.getTime()))},[]),Tt=it.useCallback(()=>{N(u())},[u]),le=it.useCallback(async()=>{if(console.log("handleSaveCollection called"),!A.trim()){console.log("No save name provided"),alert("Please enter a collection name");return}const q=u();if(console.log("Photos to save:",q.length,q),q.length===0){console.log("No photos to save"),alert("No photos to save");return}console.log("Starting save process..."),dt(!0);try{const F=await hi.saveCollection(A.trim(),q,j.trim()||void 0);console.log("Collection saved with ID:",F),E(""),X(""),Gt(),alert("Collection saved successfully!")}catch(F){console.error("Error saving collection:",F),alert(`Error saving collection: ${F instanceof Error?F.message:String(F)}`)}finally{dt(!1)}},[A,j,u,Gt]),St=it.useCallback(async(q,F=!1)=>{st(!0);try{const vt=await hi.loadCollection(q);vt?(r(vt.photos,F),F?(alert(`Collection loaded! Your current photos have been added to "${vt.name}". Switch to the "Save" tab to save the combined collection.`),Kt("save"),setTimeout(()=>{Tt()},100)):h()):alert("Error loading collection")}catch(vt){console.error("Error loading collection:",vt),alert("Error loading collection. Please try again.")}finally{st(!1)}},[r,h,Kt,Tt]),ne=it.useCallback(async q=>{st(!0);try{const F=await hi.loadCollection(q);if(F){const vt=u(),Mt=new Set(F.photos.map(Q=>Q.id)),Nt=vt.filter(Q=>!Mt.has(Q.id));if(Nt.length===0){alert("No new photos to add - all current photos are already in this collection.");return}const P=[...F.photos,...Nt];await hi.updateCollection(q,P),r(P,!1),await Gt(),alert(`Successfully added ${Nt.length} new photos to "${F.name}"! Collection now has ${P.length} photos total.`),h()}else alert("Error loading collection")}catch(F){console.error("Error appending to collection:",F),alert("Error updating collection. Please try again.")}finally{st(!1)}},[u,r,Gt,h]),oe=it.useCallback(async(q,F)=>{confirm(`Are you sure you want to delete "${F}"? This action cannot be undone.`)&&(hi.deleteCollection(q)?(Gt(),alert("Collection deleted successfully")):alert("Error deleting collection"))},[Gt]),re=it.useCallback(async q=>{try{await hi.exportCollection(q)}catch(F){console.error("Error exporting collection:",F),alert("Error exporting collection. Please try again.")}},[]),de=it.useCallback(async q=>{const F=q.target.files?.[0];if(F){try{await hi.importCollection(F)?(Gt(),alert("Collection imported successfully!")):alert("Error importing collection. Please check the file format.")}catch(vt){console.error("Error importing collection:",vt),alert("Error importing collection. Please try again.")}q.target.value=""}},[Gt]),tt=q=>q.toLocaleDateString()+" "+q.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return O.jsx("div",{className:"collection-manager-overlay",children:O.jsxs("div",{className:"collection-manager-modal",children:[O.jsxs("div",{className:"collection-manager-header",children:[O.jsx("h3",{children:"Collection Manager"}),O.jsx("button",{className:"close-button",onClick:h,children:"×"})]}),O.jsxs("div",{className:"collection-manager-tabs",children:[O.jsxs("button",{className:`tab ${xt==="manage"?"active":""}`,onClick:()=>Kt("manage"),children:["Saved Collections (",g.length,")"]}),O.jsxs("button",{className:`tab ${xt==="save"?"active":""}`,onClick:()=>Kt("save"),children:["Save Current (",x.length," photos)"]})]}),xt==="manage"&&O.jsxs("div",{className:"manage-tab",children:[O.jsxs("div",{className:"import-export-section",children:[O.jsx("input",{type:"file",id:"import-input",accept:".json",onChange:de,style:{display:"none"}}),O.jsx("label",{htmlFor:"import-input",className:"import-button",children:"📁 Import Collection"})]}),g.length===0?O.jsxs("div",{className:"no-collections",children:[O.jsx("p",{children:"No saved collections yet."}),O.jsx("p",{children:"Save your current photos to create your first collection!"})]}):O.jsx("div",{className:"collections-list",children:g.map(q=>O.jsxs("div",{className:"collection-item",children:[O.jsxs("div",{className:"collection-info",children:[O.jsx("div",{className:"collection-name",children:q.name}),q.description&&O.jsx("div",{className:"collection-description",children:q.description}),O.jsxs("div",{className:"collection-meta",children:[q.photoCount," photos • ",tt(q.updatedAt)]})]}),O.jsxs("div",{className:"collection-actions",children:[O.jsx("button",{onClick:()=>St(q.id,!1),disabled:ht,className:"load-button",children:"Load"}),O.jsx("button",{onClick:()=>ne(q.id),disabled:ht||p.length===0,className:"load-button",title:p.length===0?"Upload some photos first to append":"Add your current photos to this collection and save automatically",children:"Append"}),O.jsx("button",{onClick:()=>re(q.id),className:"export-button",children:"Export"}),O.jsx("button",{onClick:()=>oe(q.id,q.name),className:"delete-button",children:"Delete"})]})]},q.id))})]}),xt==="save"&&O.jsx("div",{className:"save-tab",children:x.length===0?O.jsxs("div",{className:"no-photos",children:[O.jsx("p",{children:"No photos to save."}),O.jsx("p",{children:"Upload some photos first to create a collection!"})]}):O.jsxs("div",{className:"save-form",children:[O.jsxs("div",{className:"form-group",children:[O.jsx("label",{htmlFor:"collection-name",children:"Collection Name *"}),O.jsx("input",{id:"collection-name",type:"text",value:A,onChange:q=>E(q.target.value),placeholder:"e.g., Summer Vacation 2024",className:"form-input"})]}),O.jsxs("div",{className:"form-group",children:[O.jsx("label",{htmlFor:"collection-description",children:"Description (optional)"}),O.jsx("textarea",{id:"collection-description",value:j,onChange:q=>X(q.target.value),placeholder:"Add a description for this collection...",className:"form-textarea",rows:3})]}),O.jsxs("div",{className:"save-summary",children:[O.jsxs("p",{children:[O.jsx("strong",{children:"Photos to save:"})," ",x.length]}),O.jsxs("p",{children:[O.jsx("strong",{children:"With GPS:"})," ",x.filter(q=>q.hasGPS).length]}),O.jsxs("p",{children:[O.jsx("strong",{children:"Manual locations:"})," ",x.filter(q=>q.locationSource==="manual").length]}),O.jsx("button",{onClick:Tt,className:"load-button",style:{marginTop:"8px",fontSize:"12px",padding:"4px 8px"},children:"Refresh Count"})]}),O.jsx("button",{onClick:le,disabled:Y||!A.trim(),className:"save-collection-button",children:Y?"Saving...":"Save Collection"})]})})]})})};function N_(){const[p,r]=it.useState([]),[u,h]=it.useState(!1),[g,y]=it.useState(0);it.useEffect(()=>{const Y=hi.loadCurrentCollection();Y&&Y.length>0&&(confirm("Found a previous session with photos. Would you like to restore it?")?r(Y):hi.clearCurrentCollection())},[]),it.useEffect(()=>{p.length>0?hi.saveCurrentCollection(p):hi.clearCurrentCollection()},[p]);const x=Y=>{const dt=new Set(p.map(st=>st.id));Y.some(st=>dt.has(st.id))?(console.log("Updating existing photos with changes"),r(Y)):(console.log("Adding new photos to collection"),r(st=>[...st,...Y]))},N=(Y,dt=!1)=>{if(dt){const ht=new Set(Y.map(Kt=>Kt.id)),st=p.filter(Kt=>!ht.has(Kt.id)),xt=[...Y,...st];r(xt),console.log(`Appended ${st.length} new photos to collection. Total: ${xt.length}`)}else r(Y)},A=()=>{p.length>0&&confirm("Are you sure you want to clear all photos? Unsaved changes will be lost.")&&(console.log("Clearing all photos. Current count:",p.length),r([]),y(Y=>Y+1),hi.clearCurrentCollection(),console.log("Photos cleared. New state should be empty array."))},E=(Y,dt,ht)=>{console.log(`Updating photo ${Y} location to:`,dt,ht),r(st=>st.map(xt=>xt.id===Y?{...xt,latitude:dt,longitude:ht,hasGPS:!0,locationSource:"manual"}:xt))},j=p.filter(Y=>Y.hasGPS),X=p.filter(Y=>!Y.hasGPS);return console.log("App render - photos.length:",p.length),O.jsxs("div",{className:"app",children:[O.jsx("header",{className:"app-header",children:O.jsxs("div",{className:"header-content",children:[O.jsxs("div",{className:"header-text",children:[O.jsx("h1",{children:"TravellerNext"}),O.jsx("p",{children:"Your travel companion - Upload photos and explore them on an interactive world map"}),O.jsxs("div",{className:"collections-section",children:[O.jsx("button",{onClick:()=>h(!0),className:"collections-button",children:"Collections"}),p.length>0&&O.jsx("button",{onClick:A,className:"clear-all-button",children:"Clear All"})]})]}),O.jsx("div",{className:"header-actions"})]})}),O.jsx("main",{className:"app-main",children:O.jsxs("div",{className:"content-layout",children:[O.jsxs("div",{className:"upload-section",children:[O.jsx(l_,{onPhotosUploaded:x,clearTrigger:g,photos:p}),p.length>0&&O.jsxs("div",{className:"photo-summary",children:[O.jsx("h3",{children:"Photo Summary"}),O.jsxs("div",{className:"summary-stats",children:[O.jsxs("div",{className:"stat",children:[O.jsx("span",{className:"stat-number",children:p.length}),O.jsx("span",{className:"stat-label",children:"Total Photos"})]}),O.jsxs("div",{className:"stat",children:[O.jsx("span",{className:"stat-number",children:j.length}),O.jsx("span",{className:"stat-label",children:"With GPS"})]}),O.jsxs("div",{className:"stat",children:[O.jsx("span",{className:"stat-number",children:X.length}),O.jsx("span",{className:"stat-label",children:"Without GPS"})]})]})]})]}),O.jsx("div",{className:"map-section",children:O.jsx(O_,{photos:p,onPhotoLocationUpdate:E})})]})}),u&&O.jsx(z_,{currentPhotos:p,onLoadCollection:N,getCurrentPhotos:()=>p,onClose:()=>h(!1)})]})}Bg.createRoot(document.getElementById("root")).render(O.jsx(it.StrictMode,{children:O.jsx(N_,{})}));
